const axios = require('axios');

const BASE_URL = 'http://localhost:9654/api/v1.0';

// Test data
const testSubject = {
  fullName: 'Nguyễn Văn Test',
  photos: ['https://example.com/photo1.jpg'],
  dob: '01/01/1990',
  gender: 'Male',
  idNumber: '123456789012',
  phones: ['0123456789'],
  permanentAddress: 'Hà Nội',
  temporaryAddress: 'TP.HCM',
  currentResidence: '<PERSON><PERSON><PERSON>',
  category: 'Hình sự',
  dangerLevel: 'nghiêm trọng',
  legalStatus: '<PERSON>ang điều tra',
  description: '<PERSON><PERSON><PERSON> tượng có biểu hiện bất thường',
  businessNotes: 'Cần theo dõi chặt chẽ'
};

async function testAPIStructure() {
  console.log('🧪 Testing API Structure (without auth)...\n');

  const endpoints = [
    { method: 'POST', url: '/admin/criminal-subject/create', data: testSubject },
    { method: 'POST', url: '/admin/criminal-subject/list', data: { page: 1, limit: 10 } },
    { method: 'POST', url: '/admin/criminal-subject/get', data: { subjectId: '507f1f77bcf86cd799439011' } },
    { method: 'POST', url: '/admin/criminal-subject/update', data: { subjectId: '507f1f77bcf86cd799439011', fullName: 'Updated Name' } },
    { method: 'POST', url: '/admin/criminal-subject/inactive', data: { subjectId: '507f1f77bcf86cd799439011' } },
    { method: 'POST', url: '/criminal-subject-update/create', data: {
      subjectId: '507f1f77bcf86cd799439011',
      contactDate: Date.now(),
      livingCondition: 'Test'
    }},
    { method: 'POST', url: '/criminal-subject-update/list', data: { page: 1, limit: 10 } },
    { method: 'POST', url: '/criminal-subject-update/get', data: { updateId: '507f1f77bcf86cd799439011' } }
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`🔍 Testing ${endpoint.method} ${endpoint.url}...`);

      const response = await axios({
        method: endpoint.method.toLowerCase(),
        url: `${BASE_URL}${endpoint.url}`,
        data: endpoint.data,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      console.log(`✅ ${endpoint.url} - Status: ${response.status}`);
      console.log(`   Response:`, response.data);

    } catch (error) {
      if (error.response) {
        console.log(`⚠️  ${endpoint.url} - Status: ${error.response.status}`);
        console.log(`   Expected error (no auth):`, error.response.data);
      } else {
        console.error(`❌ ${endpoint.url} - Network Error:`, error.message);
      }
    }
    console.log('');
  }
}

async function testHealthAndStructure() {
  try {
    // Test health first
    console.log('🧪 Testing Health Check...');
    const healthResponse = await axios.get('http://localhost:9654/health');
    console.log('✅ Health Check Success:', healthResponse.data);
    console.log('\n' + '='.repeat(60) + '\n');

    // Test API structure
    await testAPIStructure();

    console.log('🎉 API Structure tests completed!');

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

// Run tests
testHealthAndStructure();

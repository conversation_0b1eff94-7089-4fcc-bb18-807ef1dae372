# Dự án IOC Công An phường Hồng Bàng

Hệ thống backend phục vụ Trung tâm Điều hành Thông minh (IOC) Công An phường <PERSON>ồ<PERSON>, xây dựng trên nền tảng Node.js, tích hợp MongoDB và Redis.

## Tổng quan

Dự án cung cấp nền tảng backend cho hệ thống IOC Công An phường Hồng <PERSON>, bao gồm các thành phần: kết nối cơ sở dữ liệu, x<PERSON><PERSON> thực, ghi log, quản lý người dùng và khung API có cấu trúc rõ ràng, phục vụ các nghiệp vụ của công an địa phương.

## Tính năng

- Cấu trúc API RESTful có version
- Tích hợ<PERSON>go<PERSON> (Mongoose)
- Redis cho cache và quản lý phiên đăng nhập
- <PERSON><PERSON><PERSON> thực JWT
- Hỗ trợ Socket.io cho giao tiếp thời gian thực
- Ghi log có cấu trúc với Winston
- Middleware xác thực, phân quyền, xử lý request
- Tổ chức mã nguồn rõ ràng, dễ mở rộng

## Yêu cầu hệ thống

- Node.js (khuyến nghị v14 trở lên)
- MongoDB
- Redis

## Cài đặt

1. Clone mã nguồn:

   ```bash
   git clone https://github.com/sonnt612/ioc-hongbang-service
   cd ioc-hongbang-service
   ```

2. Cài đặt các package:

   ```bash
   npm install
   ```

3. Cấu hình biến môi trường:
   - Ứng dụng sử dụng package `config` để quản lý cấu hình.
   - Tạo hoặc chỉnh sửa file `config/default.json` với thông tin phù hợp môi trường của bạn:
   ```json
   {
     "redis": {
       "connections": {
         "master": {
           "host": "localhost",
           "port": 6379,
           "database": 0,
           "password": "your_redis_password"
         }
       }
     },
     "mongo": {
       "connections": {
         "master": {
           "host": "localhost",
           "port": 27017,
           "database": "app-db",
           "options": {
             "useUnifiedTopology": true,
             "useNewUrlParser": true,
             "user": "your_mongo_user",
             "pass": "your_mongo_password"
           }
         }
       }
     },
     "port": 3000,
     "logLevel": "info",
     "secretKey": "your_secret_key_for_jwt",
     "serviceName": "IOC-HONGBANG-SERVICE"
   }
   ```

## Cấu trúc dự án

```
ioc-hongbang-service/
├── index.js                  # Điểm khởi động ứng dụng
├── lib/                      # Thư viện core
│   ├── connections/          # Kết nối DB (MongoDB, Redis)
│   ├── middleware/           # Middleware cho Express
│   ├── models/               # Mongoose models
│   ├── routes/               # Định nghĩa API routes
│   ├── logger/               # Tiện ích ghi log
│   ├── util/                 # Hàm tiện ích
│   ├── const.js              # Các hằng số
│   └── message.js            # Thông điệp phản hồi
├── public/                   # File tĩnh
├── config/                   # File cấu hình
└── logs/                     # Log ứng dụng
```

## Sử dụng

Khởi động server phát triển với hot reload:

```bash
npm run dev
```

Khởi động server production:

```bash
npm start
```

Server sẽ chạy tại http://localhost:3000 (hoặc port bạn cấu hình).

## Cấu trúc API

Các endpoint API theo cấu trúc:

```
/api/{version}/{route}
```

Ví dụ:

- `/api/v1.0/auth/login` - Đăng nhập
- `/api/v1.0/auth/register` - Đăng ký
- `/api/v1.0/user/profile` - Lấy thông tin người dùng
- `/api/v1.0/user/update` - Cập nhật thông tin người dùng

## Giấy phép

Dự án phát hành theo giấy phép MIT - xem chi tiết trong file package.json.

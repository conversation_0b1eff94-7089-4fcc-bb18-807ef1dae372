const axios = require('axios');

async function testHealthCheck() {
  try {
    console.log('🧪 Testing Health Check...');
    const response = await axios.get('http://localhost:9654/health');
    console.log('✅ Health Check Success:', response.data);
    return true;
  } catch (error) {
    console.error('❌ Health Check Error:', error.response?.data || error.message);
    return false;
  }
}

async function testModelsLoading() {
  try {
    console.log('🧪 Testing Models Loading...');
    
    // Test if models are loaded correctly
    const CriminalSubjectModel = require('./lib/models/criminalSubject');
    const CriminalSubjectUpdateModel = require('./lib/models/criminalSubjectUpdate');
    
    console.log('✅ CriminalSubject Model loaded:', !!CriminalSubjectModel);
    console.log('✅ CriminalSubjectUpdate Model loaded:', !!CriminalSubjectUpdateModel);
    
    // Test model schema
    const subjectSchema = CriminalSubjectModel.schema.paths;
    const updateSchema = CriminalSubjectUpdateModel.schema.paths;
    
    console.log('📋 CriminalSubject fields:', Object.keys(subjectSchema));
    console.log('📋 CriminalSubjectUpdate fields:', Object.keys(updateSchema));
    
    return true;
  } catch (error) {
    console.error('❌ Models Loading Error:', error.message);
    return false;
  }
}

async function testConstants() {
  try {
    console.log('🧪 Testing Constants...');
    
    const CONSTANTS = require('./lib/const');
    const MESSAGES = require('./lib/message');
    
    console.log('✅ CRIMINAL_SUBJECT constants:', CONSTANTS.CRIMINAL_SUBJECT);
    console.log('✅ CRIMINAL_SUBJECT messages:', MESSAGES.CRIMINAL_SUBJECT);
    
    return true;
  } catch (error) {
    console.error('❌ Constants Error:', error.message);
    return false;
  }
}

async function testUtilFunctions() {
  try {
    console.log('🧪 Testing Util Functions...');
    
    const { change_alias } = require('./lib/util/tool');
    
    const testName = 'Nguyễn Văn Thành';
    const alias = change_alias(testName);
    
    console.log('✅ change_alias test:');
    console.log('  Input:', testName);
    console.log('  Output:', alias);
    
    return true;
  } catch (error) {
    console.error('❌ Util Functions Error:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting Simple Tests...\n');
  
  // Test constants and utils first
  await testConstants();
  console.log('\n' + '='.repeat(50));
  
  await testUtilFunctions();
  console.log('\n' + '='.repeat(50));
  
  await testModelsLoading();
  console.log('\n' + '='.repeat(50));
  
  // Test health check
  await testHealthCheck();
  
  console.log('\n🎉 All simple tests completed!');
}

// Run tests
runTests().catch(console.error);

const axios = require('axios');

const BASE_URL = 'http://localhost:9654/api/v1.0';

// Test data
const testSubject = {
  fullName: 'Nguyễn Văn Test',
  photos: ['https://example.com/photo1.jpg'],
  dob: '01/01/1990',
  gender: 'Male',
  idNumber: '123456789012',
  phones: ['0123456789'],
  permanentAddress: '<PERSON>à Nội',
  temporaryAddress: 'TP.HCM',
  currentResidence: 'Hải <PERSON>ong',
  category: '<PERSON>ình sự',
  dangerLevel: 'nghiêm trọng',
  legalStatus: '<PERSON>ang điều tra',
  description: '<PERSON><PERSON>i tượng có biểu hiện bất thường',
  businessNotes: 'Cần theo dõi chặt chẽ'
};

const testUpdate = {
  contactDate: Date.now(),
  livingCondition: 'Sinh hoạt bình thường',
  populationMovement: 'Không có di chuyển',
  abnormalSigns: '<PERSON>hông có dấu hiệu bất thường'
};

// Mock token (you need to get real token from login)
const mockToken = 'your-jwt-token-here';

const headers = {
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${mockToken}`
};

async function testCreateSubject() {
  try {
    console.log('🧪 Testing Create Criminal Subject...');
    const response = await axios.post(`${BASE_URL}/admin/criminal-subject/create`, testSubject, { headers });
    console.log('✅ Create Success:', response.data);
    return response.data.data;
  } catch (error) {
    console.error('❌ Create Error:', error.response?.data || error.message);
    return null;
  }
}

async function testGetSubject(subjectId) {
  try {
    console.log('🧪 Testing Get Criminal Subject...');
    const response = await axios.post(`${BASE_URL}/admin/criminal-subject/get`, { subjectId }, { headers });
    console.log('✅ Get Success:', response.data);
    return response.data.data;
  } catch (error) {
    console.error('❌ Get Error:', error.response?.data || error.message);
    return null;
  }
}

async function testListSubjects() {
  try {
    console.log('🧪 Testing List Criminal Subjects...');
    const response = await axios.post(`${BASE_URL}/admin/criminal-subject/list`, {
      page: 1,
      limit: 10,
      textSearch: 'Test',
      category: 'Hình sự'
    }, { headers });
    console.log('✅ List Success:', response.data);
    return response.data.data;
  } catch (error) {
    console.error('❌ List Error:', error.response?.data || error.message);
    return null;
  }
}

async function testCreateUpdate(subjectId) {
  try {
    console.log('🧪 Testing Create Subject Update...');
    const updateData = { ...testUpdate, subjectId };
    const response = await axios.post(`${BASE_URL}/criminal-subject-update/create`, updateData, { headers });
    console.log('✅ Create Update Success:', response.data);
    return response.data.data;
  } catch (error) {
    console.error('❌ Create Update Error:', error.response?.data || error.message);
    return null;
  }
}

async function testHealthCheck() {
  try {
    console.log('🧪 Testing Health Check...');
    const response = await axios.get(`http://localhost:9654/health`);
    console.log('✅ Health Check Success:', response.data);
    return true;
  } catch (error) {
    console.error('❌ Health Check Error:', error.response?.data || error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting Criminal Subject API Tests...\n');
  
  // Test health check first
  const isHealthy = await testHealthCheck();
  if (!isHealthy) {
    console.log('❌ Server is not healthy, stopping tests');
    return;
  }
  
  console.log('\n' + '='.repeat(50));
  
  // Test create subject
  const createdSubject = await testCreateSubject();
  if (!createdSubject) {
    console.log('❌ Cannot proceed without created subject');
    return;
  }
  
  console.log('\n' + '='.repeat(50));
  
  // Test get subject
  await testGetSubject(createdSubject._id);
  
  console.log('\n' + '='.repeat(50));
  
  // Test list subjects
  await testListSubjects();
  
  console.log('\n' + '='.repeat(50));
  
  // Test create update
  await testCreateUpdate(createdSubject._id);
  
  console.log('\n🎉 All tests completed!');
}

// Run tests
runTests().catch(console.error);

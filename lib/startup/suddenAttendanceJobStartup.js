/**
 * Khởi tạo job chấm công đột xuất khi server start
 */

const suddenAttendanceJob = require('../jobs/suddenAttendanceJob');

class SuddenAttendanceJobStartup {
  /**
   * Khởi tạo job khi server start
   */
  static init() {
    try {
      console.log('[STARTUP] Initializing sudden attendance job...');

      // Khởi động job với các cron schedules tự động
      suddenAttendanceJob.init();

      console.log('[STARTUP] Sudden attendance job initialized successfully');
      console.log('[STARTUP] Job will run every minute for status updates and every 5 minutes for reminders');

    } catch (error) {
      console.error('[STARTUP] Error initializing sudden attendance job:', error);
    }
  }

  /**
   * Dừng job khi server shutdown
   */
  static shutdown() {
    try {
      console.log('[SHUTDOWN] Stopping sudden attendance job...');
      suddenAttendanceJob.stop();
      console.log('[SHUTDOWN] Sudden attendance job stopped');
    } catch (error) {
      console.error('[SHUTDOWN] Error stopping sudden attendance job:', error);
    }
  }
}

module.exports = SuddenAttendanceJobStartup;

/**
 * Khởi tạo job đồng bộ trạng thái attendance khi server start
 */

const attendanceStatusSyncJob = require('../jobs/attendanceStatusSyncJob');

class AttendanceStatusSyncJobStartup {
  /**
   * Khởi tạo job khi server start
   */
  static init() {
    try {
      console.log('[STARTUP] Initializing attendance status sync job...');

      // Khởi động job với schedule mặc định: 12:00 và 18:00 hàng ngày
      attendanceStatusSyncJob.start('0 12,18 * * *');

      console.log('[STARTUP] Attendance status sync job initialized successfully');
      console.log('[STARTUP] Job will run daily at 12:00 and 18:00 (Asia/Ho_Chi_Minh timezone)');

    } catch (error) {
      console.error('[STARTUP] Error initializing attendance status sync job:', error);
    }
  }

  /**
   * Dừng job khi server shutdown
   */
  static shutdown() {
    try {
      console.log('[SHUTDOWN] Stopping attendance status sync job...');
      attendanceStatusSyncJob.stop();
      console.log('[SHUTDOWN] Attendance status sync job stopped');
    } catch (error) {
      console.error('[SHUTDOWN] Error stopping attendance status sync job:', error);
    }
  }
}

module.exports = AttendanceStatusSyncJobStartup;

/**
 * Debouncer Startup Script
 *
 * Initialize và setup Statistics Debouncer System khi server khởi động
 * - Setup callback functions
 * - Initialize health monitoring
 * - Setup graceful shutdown
 * - Recovery từ previous state (nếu có)
 */

const { setRecalculationCallback, getStatus } = require('../utils/debouncer');
const { performHealthCheck } = require('../middleware/debouncerHealthCheck');
const StatisticsTrigger = require('../utils/statisticsTrigger');

/**
 * Initialize debouncer system
 */
function initializeDebouncer() {
  console.log('🚀 Initializing Statistics Debouncer System...');

  try {
    // 1. Setup callback function
    setupRecalculationCallback();

    // 2. Setup health monitoring
    setupHealthMonitoring();

    // 3. Setup graceful shutdown
    // setupGracefulShutdown();

    // 4. Recovery check
    performRecoveryCheck();

    // 5. Log initialization success
    logInitializationSuccess();

    console.log('✅ Statistics Debouncer System initialized successfully');

  } catch (error) {
    console.error('❌ Failed to initialize Statistics Debouncer System:', error);
    throw error;
  }
}

/**
 * Setup recalculation callback
 */
function setupRecalculationCallback() {
  console.log('🔗 Setting up recalculation callback...');

  // Callback đã được setup trong StatisticsTrigger
  // Chỉ cần verify nó hoạt động
  const status = getStatus();
  console.log('📊 Debouncer status:', {
    initialized: true,
    metrics: status.metrics
  });
}

/**
 * Setup health monitoring
 */
function setupHealthMonitoring() {
  console.log('🏥 Setting up health monitoring...');

  // Periodic health check every 5 minutes
  const healthCheckInterval = setInterval(() => {
    const healthReport = performHealthCheck();

    if (!healthReport.isHealthy) {
      console.warn('⚠️ Debouncer health issues detected:', healthReport.issues);
    }

    // Log health status (only if there are issues or every hour)
    const now = new Date();
    if (!healthReport.isHealthy || now.getMinutes() === 0) {
      console.log('🏥 Debouncer health check:', {
        isHealthy: healthReport.isHealthy,
        issueCount: healthReport.issues.length,
        status: healthReport.status
      });
    }
  }, 5 * 60 * 1000); // 5 minutes

  // Store interval for cleanup
  global.debouncerHealthCheckInterval = healthCheckInterval;
}

/**
 * Setup graceful shutdown
 */
function setupGracefulShutdown() {
  console.log('🛑 Setting up graceful shutdown handlers...');

  const gracefulShutdown = async (signal) => {
    console.log(`📡 Received ${signal}, starting graceful shutdown...`);

    try {
      // 1. Stop health monitoring
      if (global.debouncerHealthCheckInterval) {
        clearInterval(global.debouncerHealthCheckInterval);
        console.log('🏥 Health monitoring stopped');
      }

      // 2. Get current status
      const status = getStatus();
      console.log('📊 Final debouncer status:', {
        executingTasks: status.executingTasks.length,
        activeTimers: status.activeTimers,
        queueSizes: status.queueSizes,
        totalEvents: status.metrics.totalEvents
      });

      // 3. Wait for executing tasks (with timeout)
      if (status.executingTasks.length > 0) {
        console.log(`⏳ Waiting for ${status.executingTasks.length} executing tasks...`);

        const maxWaitTime = 30000; // 30 seconds
        const startTime = Date.now();

        while (getStatus().executingTasks.length > 0 && (Date.now() - startTime) < maxWaitTime) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

        const finalStatus = getStatus();
        if (finalStatus.executingTasks.length > 0) {
          console.warn(`⚠️ Force stopping ${finalStatus.executingTasks.length} remaining tasks`);
        } else {
          console.log('✅ All executing tasks completed');
        }
      }

      // 4. Log final metrics
      const finalMetrics = getStatus().metrics;
      console.log('📈 Final debouncer metrics:', finalMetrics);

      console.log('✅ Debouncer graceful shutdown completed');

    } catch (error) {
      console.error('❌ Error during debouncer shutdown:', error);
    }
  };

  // Handle different shutdown signals
  process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
  process.on('SIGINT', () => gracefulShutdown('SIGINT'));
  process.on('SIGUSR2', () => gracefulShutdown('SIGUSR2')); // nodemon restart
}

/**
 * Perform recovery check
 */
function performRecoveryCheck() {
  console.log('🔄 Performing recovery check...');

  // Check if there are any persisted states to recover
  // This is a placeholder for future persistence features

  const status = getStatus();
  if (status.executingTasks.length > 0 || status.activeTimers > 0) {
    console.warn('⚠️ Found existing debouncer state on startup:', {
      executingTasks: status.executingTasks.length,
      activeTimers: status.activeTimers
    });

    // In a real implementation, you might want to:
    // 1. Restore from Redis/Database
    // 2. Validate state consistency
    // 3. Resume or cleanup as needed
  }

  console.log('✅ Recovery check completed');
}

/**
 * Log initialization success with system info
 */
function logInitializationSuccess() {
  const status = getStatus();
  const systemInfo = {
    nodeVersion: process.version,
    platform: process.platform,
    memoryUsage: process.memoryUsage(),
    uptime: process.uptime()
  };

  console.log('🎉 Debouncer System Ready:', {
    status: 'initialized',
    health: status.health.isHealthy ? 'healthy' : 'unhealthy',
    system: systemInfo,
    timestamp: new Date().toISOString()
  });
}

/**
 * Test debouncer functionality
 */
async function testDebouncerFunctionality() {
  console.log('🧪 Testing debouncer functionality...');

  try {
    const { debounceStatisticsUpdate } = require('../utils/debouncer');

    // Send a test event
    debounceStatisticsUpdate('reports_summary', {
      type: 'startup_test',
      timestamp: Date.now(),
      message: 'Debouncer startup test'
    });

    // Wait a moment and check status
    await new Promise(resolve => setTimeout(resolve, 100));

    const status = getStatus();
    if (status.metrics.totalEvents > 0) {
      console.log('✅ Debouncer functionality test passed');
    } else {
      console.warn('⚠️ Debouncer functionality test inconclusive');
    }

  } catch (error) {
    console.error('❌ Debouncer functionality test failed:', error);
  }
}

/**
 * Get initialization report
 */
function getInitializationReport() {
  const status = getStatus();

  return {
    timestamp: new Date().toISOString(),
    status: 'initialized',
    health: status.health,
    metrics: status.metrics,
    system: {
      nodeVersion: process.version,
      platform: process.platform,
      memoryUsage: process.memoryUsage(),
      uptime: process.uptime()
    }
  };
}

module.exports = {
  initializeDebouncer,
  setupRecalculationCallback,
  setupHealthMonitoring,
  setupGracefulShutdown,
  performRecoveryCheck,
  testDebouncerFunctionality,
  getInitializationReport
};
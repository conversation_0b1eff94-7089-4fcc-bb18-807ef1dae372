const cron = require('node-cron');
const statisticsMetadataService = require('../services/statisticsMetadataService');

/**
 * Background job xử lý metadata thống kê định kỳ
 * - Tính toán metadata mỗi 10 phút
 * - X<PERSON> lý trigger events mỗi phút
 * - Gửi real-time notifications khi có thay đổi
 */
class StatisticsMetadataJob {
  constructor() {
    this.metadataInterval = null;
    // ❌ REMOVED: triggerInterval - Không cần thiết nữa với debouncer system
  }

  /**
   * Khởi động tất cả jobs
   */
  start() {
    this.startMetadataCalculation();
    // ❌ REMOVED: startTriggerEventProcessing() - Đã có debouncer system
    console.log(`[${new Date().toISOString()}] Statistics metadata jobs started`);
  }

  /**
   * Khởi động job tính toán metadata định kỳ
   * Chạy mỗi 10 phút
   */
  startMetadataCalculation() {
    // Chạy ngay lần đầu
    this.calculateMetadata();

    // Lên lịch chạy mỗi 10 phút
    cron.schedule('*/10 * * * *', () => {
      this.calculateMetadata();
    }, {
      timezone: "Asia/Ho_Chi_Minh"
    });

    console.log(`[${new Date().toISOString()}] Metadata calculation job scheduled - every 10 minutes`);
  }

  /**
   * ❌ REMOVED: Khởi động job xử lý trigger events
   * Không cần thiết nữa vì đã có debouncer system
   */
  // startTriggerEventProcessing() - REMOVED

  /**
   * Thực hiện tính toán metadata
   */
  async calculateMetadata() {
    try {
      console.log(`[${new Date().toISOString()}] Starting metadata calculation...`);

      const result = await statisticsMetadataService.calculateAllMetadata();

      if (result.success) {
        console.log(`[${new Date().toISOString()}] Metadata calculation completed successfully`);
      } else {
        console.error(`[${new Date().toISOString()}] Metadata calculation failed:`, result.error);
      }

      // Log kết quả
      if (global.logger) {
        global.logger.logInfo('[STATISTICS_METADATA_JOB]', result);
      }

      return result;

    } catch (error) {
      console.error(`[${new Date().toISOString()}] Metadata calculation error:`, error);

      if (global.logger) {
        global.logger.logError(['Statistics metadata calculation error:', error]);
      }

      return { success: false, error: error.message };
    }
  }

  /**
   * ❌ REMOVED: Xử lý trigger events
   * Không cần thiết nữa vì đã có debouncer system
   */
  // async processTriggerEvents() - REMOVED

  /**
   * Dừng tất cả jobs
   */
  stop() {
    if (this.metadataInterval) {
      clearInterval(this.metadataInterval);
      this.metadataInterval = null;
    }

    // ❌ REMOVED: triggerInterval - Không cần thiết nữa
    // if (this.triggerInterval) {
    //   clearInterval(this.triggerInterval);
    //   this.triggerInterval = null;
    // }

    console.log(`[${new Date().toISOString()}] Statistics metadata jobs stopped`);
  }

  /**
   * Chạy metadata calculation ngay lập tức (để test)
   */
  async runMetadataCalculationNow() {
    console.log(`[${new Date().toISOString()}] Running metadata calculation manually...`);

    try {
      const result = await this.calculateMetadata();
      console.log('Manual metadata calculation result:', result);
      return result;
    } catch (error) {
      console.error('Manual metadata calculation error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * ❌ REMOVED: Chạy trigger event processing ngay lập tức
   * Không cần thiết nữa vì đã có debouncer system
   */
  // async runTriggerProcessingNow() - REMOVED

  /**
   * ❌ DEPRECATED: Trigger một event cụ thể
   * Sử dụng debouncer system thay thế
   * @param {String} eventType - Loại event
   * @param {Object} eventData - Dữ liệu event
   */
  triggerEvent(eventType, eventData = {}) {
    console.warn(`[${new Date().toISOString()}] DEPRECATED: triggerEvent() - Use debouncer system instead`);

    // Giữ lại để backward compatibility nhưng không làm gì
    return { success: true, eventType, eventData, deprecated: true };
  }

  /**
   * Invalidate cache cho một loại thống kê cụ thể
   * @param {String} type - Loại thống kê
   */
  async invalidateCache(type) {
    try {
      await statisticsMetadataService.invalidateCache(type);
      console.log(`[${new Date().toISOString()}] Cache invalidated for ${type}:`, { type });

      return { success: true, type };
    } catch (error) {
      console.error(`[${new Date().toISOString()}] Error invalidating cache for ${type}:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Lấy thống kê về job performance
   */
  getJobStats() {
    return {
      metadataJobActive: !!this.metadataInterval,
      // ❌ REMOVED: triggerJobActive - Không cần thiết nữa
      lastMetadataRun: this.lastMetadataRun || null,
      // ❌ REMOVED: lastTriggerRun - Không cần thiết nữa
      uptime: process.uptime(),
      note: 'Trigger processing now handled by debouncer system'
    };
  }
}

module.exports = new StatisticsMetadataJob();

/**
 * Background job tạo lịch làm việc tự động hàng tuần
 * Chạy vào 22h thứ 6 hàng tuần
 */

const cron = require('node-cron');
const scheduleService = require('../services/scheduleService');

class WeeklyScheduleJob {
  /**
   * Khởi động job tự động tạo lịch hàng tuần
   */
  start() {
    // Chạy vào 22h thứ 6 hàng tuần (0 22 * * 5)
    cron.schedule('0 22 * * 5', async () => {
      console.log(`[${new Date().toISOString()}] Starting weekly schedule creation job...`);

      try {
        const result = await scheduleService.createAutoWeeklySchedule();

        if (result.success) {
          console.log(`[${new Date().toISOString()}] Weekly schedule job completed: ${result.message.body}`);
        } else {
          console.error(`[${new Date().toISOString()}] Weekly schedule job failed: ${result.message.body}`);
        }

        // Log kết quả
        if (global.logger) {
          global.logger.logInfo('[WEEKLY_SCHEDULE_JOB]', result);
        }

      } catch (error) {
        console.error(`[${new Date().toISOString()}] Weekly schedule job error:`, error);

        if (global.logger) {
          global.logger.logError(['Weekly schedule job error:', error]);
        }
      }
    }, {
      timezone: "Asia/Ho_Chi_Minh"
    });

    console.log(`[${new Date().toISOString()}] Weekly schedule job started - will run at 22:00 every Friday`);
  }

  /**
   * Chạy job ngay lập tức (để test)
   */
  async runNow() {
    console.log(`[${new Date().toISOString()}] Running weekly schedule job manually...`);

    try {
      const result = await scheduleService.createAutoWeeklySchedule();
      console.log('Manual weekly schedule result:', result);
      return result;
    } catch (error) {
      console.error('Manual weekly schedule error:', error);
      return {
        success: false,
        message: {
          head: 'Lỗi chạy thủ công',
          body: error.message
        }
      };
    }
  }
}

module.exports = new WeeklyScheduleJob();
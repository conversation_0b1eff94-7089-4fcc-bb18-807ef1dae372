/**
 * Job xử lý thông báo điểm danh định kỳ
 * Ch<PERSON>y vào các thời điểm cố định: 7h30, 7h55, 8h, 13h30, 13h55, 14h hàng ngày
 * Nhắc nhở những người chưa điểm danh và có trạng thái scheduled
 */

const cron = require('node-cron');
const moment = require('moment');
const WorkSchedule = require('../models/workSchedule');
const AttendanceRecord = require('../models/attendanceRecord');
const PushNotifyManager = require('./pushNotify');

class AttendanceNotificationJob {
  constructor() {
    this.isRunning = false;
  }

  /**
   * Khởi tạo các cron jobs cho thông báo điểm danh
   */
  init() {
    console.log('[AttendanceNotificationJob] Initializing attendance notification jobs...');

    // Ca sáng - 30 phút trước (7h30)
    cron.schedule('30 7 * * *', () => {
      this.sendAttendanceReminder('morning', 30);
    }, {
      timezone: "Asia/Ho_Chi_Minh"
    });

    // Ca sáng - 5 phút trước (7h55)
    cron.schedule('55 7 * * *', () => {
      this.sendAttendanceReminder('morning', 5);
    }, {
      timezone: "Asia/Ho_Chi_Minh"
    });

    // Ca sáng - đúng giờ (8h00)
    cron.schedule('0 8 * * *', () => {
      this.sendAttendanceReminder('morning', 0);
    }, {
      timezone: "Asia/Ho_Chi_Minh"
    });

    // Ca chiều - 30 phút trước (13h30)
    cron.schedule('30 13 * * *', () => {
      this.sendAttendanceReminder('afternoon', 30);
    }, {
      timezone: "Asia/Ho_Chi_Minh"
    });

    // Ca chiều - 5 phút trước (13h55)
    cron.schedule('55 13 * * *', () => {
      this.sendAttendanceReminder('afternoon', 5);
    }, {
      timezone: "Asia/Ho_Chi_Minh"
    });

    // Ca chiều - đúng giờ (14h00)
    cron.schedule('0 14 * * *', () => {
      this.sendAttendanceReminder('afternoon', 0);
    }, {
      timezone: "Asia/Ho_Chi_Minh"
    });

    console.log('[AttendanceNotificationJob] Attendance notification jobs initialized');
  }

  /**
   * Gửi thông báo nhắc nhở điểm danh
   * @param {String} shift - Ca làm việc: 'morning' hoặc 'afternoon'
   * @param {Number} minutesBefore - Số phút trước giờ làm việc (30, 5, 0)
   */
  async sendAttendanceReminder(shift, minutesBefore) {
    if (this.isRunning) {
      console.log('[AttendanceNotificationJob] Job is already running, skipping...');
      return;
    }

    try {
      this.isRunning = true;
      console.log(`[AttendanceNotificationJob] Starting ${shift} shift reminder (${minutesBefore} minutes before)`);

      const today = moment().format('DD-MM-YYYY');

      // 1. Lấy tất cả lịch làm việc của ngày hôm nay cho ca này
      const workSchedules = await WorkSchedule.find({
        date: today,
        status: 1,
        'shifts.type': shift
      }).populate('user', '_id name').lean();

      console.log(`[AttendanceNotificationJob] Found ${workSchedules.length} work schedules for ${shift} shift on ${today}`);

      if (workSchedules.length === 0) {
        console.log('[AttendanceNotificationJob] No work schedules found');
        return;
      }

      // 2. Lấy danh sách những người đã điểm danh cho ca này
      const checkedInUsers = await AttendanceRecord.find({
        date: today,
        shift: shift
      }).distinct('user');

      console.log(`[AttendanceNotificationJob] Found ${checkedInUsers.length} users already checked in for ${shift} shift`);

      // 3. Lọc những người cần nhắc nhở
      const usersToNotify = [];

      for (const schedule of workSchedules) {
        // Tìm ca làm việc tương ứng
        const shiftData = schedule.shifts.find(s => s.type === shift);

        if (!shiftData) continue;

        // Chỉ nhắc nhở những người có trạng thái 'scheduled'
        // if (shiftData.status !== 'scheduled') {
        //   continue;
        // }

        // Kiểm tra xem đã điểm danh chưa
        const hasCheckedIn = checkedInUsers.some(userId =>
          userId.toString() === schedule.user._id.toString()
        );

        if (!hasCheckedIn) {
          usersToNotify.push({
            userId: schedule.user._id,
            userName: schedule.user.name,
            startTime: shiftData.startTime
          });
        }
      }

      console.log(`[AttendanceNotificationJob] Found ${usersToNotify.length} users to notify for ${shift} shift`);

      if (usersToNotify.length === 0) {
        console.log('[AttendanceNotificationJob] No users need notification');
        return;
      }

      // 4. Gửi thông báo cho từng người
      const shiftText = shift === 'morning' ? 'sáng' : 'chiều';
      let title, message;

      if (minutesBefore === 30) {
        title = 'Nhắc nhở điểm danh';
        message = `Còn 30 phút nữa đến giờ làm việc ca ${shiftText}. Vui lòng chuẩn bị điểm danh!`;
      } else if (minutesBefore === 5) {
        title = 'Cảnh báo điểm danh';
        message = `Còn 5 phút nữa đến giờ làm việc ca ${shiftText}. Vui lòng điểm danh ngay!`;
      } else {
        title = 'Đến giờ điểm danh';
        message = `Đã đến giờ làm việc ca ${shiftText}. Vui lòng điểm danh ngay!`;
      }

      const notificationData = {
        link: 'MainContainer',
        extras: {
          tabIndex: 2
        },
        linkWeb: '/attendance'
      };

      const idNotification = '68b1786375317f41a34b6976';
      SavedNotificationModel
        .findByIdAndUpdate(
          idNotification,
          {
            title,
            description: message,
            seen: [],
            updatedAt: Date.now()
          }
        );

      let successCount = 0;
      let failCount = 0;

      for (const user of usersToNotify) {
        try {
          await PushNotifyManager.sendToMember(
            user.userId,
            title,
            message,
            notificationData,
            '', // attendance_reminder
            'ioc'
          );

          successCount++;
          console.log(`[AttendanceNotificationJob] Sent reminder to ${user.userName} (${user.userId})`);
        } catch (error) {
          failCount++;
          console.error(`[AttendanceNotificationJob] Failed to send reminder to ${user.userName}:`, error);
        }
      }

      console.log(`[AttendanceNotificationJob] Completed ${shift} shift reminder: ${successCount} success, ${failCount} failed`);

    } catch (error) {
      console.error(`[AttendanceNotificationJob] Error in sendAttendanceReminder:`, error);
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Test function - gửi thông báo ngay lập tức (để test)
   * @param {String} shift - Ca làm việc
   * @param {Number} minutesBefore - Số phút trước
   */
  async testReminder(shift = 'morning', minutesBefore = 30) {
    console.log(`[AttendanceNotificationJob] Testing ${shift} reminder (${minutesBefore} minutes before)`);
    await this.sendAttendanceReminder(shift, minutesBefore);
  }

  /**
   * Dừng tất cả jobs
   */
  stop() {
    console.log('[AttendanceNotificationJob] Stopping attendance notification jobs...');
    // Cron jobs sẽ tự động dừng khi process kết thúc
  }
}

module.exports = new AttendanceNotificationJob();
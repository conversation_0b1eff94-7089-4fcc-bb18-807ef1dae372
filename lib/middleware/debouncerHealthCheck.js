/**
 * Debouncer Health Check Middleware
 *
 * Middleware để kiểm tra health của debouncer system
 * và tự động cleanup nếu cần thiết
 */

const { getStatus, forceCleanup } = require('../utils/debouncer');

/**
 * Health check middleware
 * Chạy mỗi request để monitor debouncer health
 */
function debouncerHealthCheck(req, res, next) {
  try {
    const status = getStatus();

    // Check for critical issues
    const criticalIssues = checkCriticalIssues(status);

    if (criticalIssues.length > 0) {
      console.warn('🚨 Debouncer critical issues detected:', criticalIssues);

      // Auto-recovery for some issues
      handleCriticalIssues(criticalIssues, status);
    }

    // Add debouncer status to request for debugging
    req.debouncerStatus = {
      isHealthy: status.health.isHealthy,
      executingTasks: status.executingTasks.length,
      activeTimers: status.activeTimers,
      totalEvents: status.metrics.totalEvents
    };

    next();

  } catch (error) {
    console.error('Error in debouncer health check:', error);
    // Don't block the request, just log the error
    next();
  }
}

/**
 * Check for critical issues that need immediate attention
 */
function checkCriticalIssues(status) {
  const issues = [];

  // Check for stuck tasks (running > 5 minutes)
  if (status.executingTasks.length > 0) {
    // In a real implementation, you'd track execution start times
    issues.push({
      type: 'stuck_tasks',
      severity: 'high',
      count: status.executingTasks.length,
      tasks: status.executingTasks
    });
  }

  // Check error rate
  const errorRate = status.metrics.totalEvents > 0
    ? (status.metrics.executionErrors / status.metrics.totalEvents)
    : 0;

  if (errorRate > 0.2) { // > 20% error rate is critical
    issues.push({
      type: 'high_error_rate',
      severity: 'critical',
      errorRate: errorRate,
      errorCount: status.metrics.executionErrors
    });
  }

  // Check queue overflow
  const totalQueueSize = Object.values(status.queueSizes).reduce((sum, size) => sum + size, 0);
  if (totalQueueSize > 100) {
    issues.push({
      type: 'queue_overflow',
      severity: 'high',
      totalQueueSize: totalQueueSize,
      queues: status.queueSizes
    });
  }

  // Check memory usage
  const memoryUsage = process.memoryUsage();
  const memoryMB = memoryUsage.rss / 1024 / 1024;
  if (memoryMB > 1000) { // > 1GB is concerning
    issues.push({
      type: 'high_memory_usage',
      severity: 'medium',
      memoryMB: memoryMB,
      memoryUsage: memoryUsage
    });
  }

  return issues;
}

/**
 * Handle critical issues with auto-recovery
 */
function handleCriticalIssues(issues, status) {
  issues.forEach(issue => {
    switch (issue.type) {
      case 'stuck_tasks':
        // Auto-cleanup stuck tasks after warning
        console.warn(`🧹 Auto-cleanup stuck tasks: ${issue.tasks.join(', ')}`);
        forceCleanup();
        break;

      case 'queue_overflow':
        // Force cleanup to reduce queue sizes
        console.warn(`🧹 Auto-cleanup due to queue overflow: ${issue.totalQueueSize} events`);
        forceCleanup();
        break;

      case 'high_error_rate':
        // Log critical error rate but don't auto-fix
        console.error(`🚨 Critical error rate: ${(issue.errorRate * 100).toFixed(1)}%`);
        break;

      case 'high_memory_usage':
        // Trigger garbage collection if available
        if (global.gc) {
          console.warn(`🗑️ Triggering garbage collection due to high memory usage: ${issue.memoryMB}MB`);
          global.gc();
        }
        break;
    }
  });
}

/**
 * Express middleware wrapper
 */
function createHealthCheckMiddleware(options = {}) {
  const {
    enabled = true,
    skipPaths = ['/health', '/api/v1.0/admin/debouncer/status'],
    logLevel = 'warn'
  } = options;

  return (req, res, next) => {
    // Skip health check for certain paths
    if (!enabled || skipPaths.some(path => req.path.includes(path))) {
      return next();
    }

    debouncerHealthCheck(req, res, next);
  };
}

/**
 * Standalone health check function for monitoring
 */
function performHealthCheck() {
  const status = getStatus();
  const issues = checkCriticalIssues(status);

  return {
    timestamp: new Date().toISOString(),
    isHealthy: issues.length === 0,
    issues: issues,
    status: {
      executingTasks: status.executingTasks.length,
      activeTimers: status.activeTimers,
      totalEvents: status.metrics.totalEvents,
      errorRate: status.metrics.totalEvents > 0
        ? (status.metrics.executionErrors / status.metrics.totalEvents)
        : 0
    }
  };
}

module.exports = {
  debouncerHealthCheck,
  createHealthCheckMiddleware,
  performHealthCheck,
  checkCriticalIssues,
  handleCriticalIssues
};
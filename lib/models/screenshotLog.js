const mongoose = require('mongoose');
const mongoConnections = require('../connections/mongo');
const Schema = mongoose.Schema;

/**
 * Model lưu log chụp màn hình của người dùng
 * Ghi lại thông tin khi người dùng thực hiện chụp màn hình ứng dụng
 */
const ScreenshotLogSchema = new mongoose.Schema(
  {
    // Người dùng thực hiện chụp màn hình
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },

    // Thông tin thiết bị
    device: {
      // Loại thiết bị (iOS, Android)
      type: {
        type: String,
        enum: ['ios', 'android'],
        required: true
      },
      // Model thiết bị
      model: {
        type: String
      },
      // Phiên bản hệ điều hành
      osVersion: {
        type: String
      }
    },

    // Thông tin ứng dụng
    app: {
      // Phiên bản ứng dụng
      version: {
        type: String
      },
      // Phiên bản build
      buildNumber: {
        type: String
      }
    },

    // Thông tin màn hình đang chụp
    screen: {
      // Tên màn hình/route
      name: {
        type: String
      },
      // Dữ liệu bổ sung về màn hình
      data: {
        type: Schema.Types.Mixed
      }
    },

    // Vị trí người dùng khi chụp màn hình (tùy chọn)
    location: {
      lat: {
        type: Number
      },
      lng: {
        type: Number
      },
      address: {
        type: String
      }
    },

    // Ghi chú bổ sung (tùy chọn)
    note: {
      type: String,
      trim: true,
      maxlength: 500
    },

    // Metadata
    createdAt: {
      type: Number,
      default: Date.now
    }
  },
  { id: false, versionKey: false }
);

// Indexes cho performance
ScreenshotLogSchema.index({ user: 1, createdAt: -1 });
ScreenshotLogSchema.index({ createdAt: -1 });

module.exports = mongoConnections('master').model('ScreenshotLog', ScreenshotLogSchema);

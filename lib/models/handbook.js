const mongoose = require('mongoose');
const mongoConnections = require('../connections/mongo')

var Handbook = new mongoose.Schema({
  image: { type: String},
  title: { type: String },
  idNumber: { type: String },
  content: { type: String },
  file: { type: String },
  seen: { type: Array, default: [] },
  views: { type: Number, default: 0 },
  status: { type: Number, default: 1 },
  units: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Unit'
  }],
  categories: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'HandbookCategory'
  }],
  updatedAt: { type: Number, default: Date.now },
  createdAt: { type: Number, default: Date.now }
}, { versionKey: false });

module.exports = mongoConnections('master').model('Handbook', Handbook);

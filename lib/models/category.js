const mongoConnections = require("../connections/mongo")
const Schema = mongoose.Schema
const CategorySchema = new mongoose.Schema(
 {
  name: {
   type: String,
  },
  icon: {
    type: String,
  },
  status: {
    type: Number,
    default: 1
  },
  order: {
    type: Number,
    default: 1
  },
  updatedAt: {
    type: Number,
    default: Date.now
  },
  createdAt: {
    type: Number,
    default: Date.now
  }
 },
 { id: false, versionKey: false },
)

module.exports = mongoConnections("master").model("Category", CategorySchema)

const mongoConnections = require("../connections/mongo")
const Schema = mongoose.Schema
const RankSchema = new mongoose.Schema(
 {
  name: {
   type: String,
  },
  image: {
    type: String
  },
  status: {
    type: Number,
    default: 1
  },
  order: {
    type: Number,
  },
  updatedAt: {
    type: Number,
    default: Date.now
  },
  createdAt: {
    type: Number,
    default: Date.now
  }
 },
 { id: false, versionKey: false },
)

module.exports = mongoConnections("master").model("Rank", RankSchema)

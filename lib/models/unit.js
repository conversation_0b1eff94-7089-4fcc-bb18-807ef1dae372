const mongoConnections = require("../connections/mongo")
const Schema = mongoose.Schema
const UnitSchema = new mongoose.Schema(
 {
  name: {
   type: String,
  },
  nameAlias: {
    type: String,
  },
  icon: {
    type: String,
    default: 'https://media-hb-dev.canbo.ai/uploads/avatar/2025-08-25-images%201.png'
  },
  parent: {
    type: Schema.Types.ObjectId,
    ref: 'Unit'
  },
  parentPath: [{
    type: Schema.Types.ObjectId,
    ref: 'Unit'
  }],
  status: {
    type: Number,
    default: 1
  },
  order: {
    type: Number,
  },
  createdAt: {
    type: Number,
    default: Date.now
  }
 },
 { id: false, versionKey: false },
)

module.exports = mongoConnections("master").model("Unit", UnitSchema)

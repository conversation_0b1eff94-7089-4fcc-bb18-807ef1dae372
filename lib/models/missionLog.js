const mongoose = require('mongoose');
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')
const UserModel = require('./user');
const savedNotification = require('./savedNotification');

const MissionLogSchema = new mongoose.Schema({
  user: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  mission: { type: mongoose.Schema.Types.ObjectId, ref: 'Mission' },
  message: {
    type: String,
    required: true
  },
  action: {
    type: Number,
    default: 0
  },
  data: { type: mongoose.Schema.Types.Mixed},
  createdAt: { type: Number, default: Date.now },
  updatedAt: { type: Number, default: Date.now },
}, { id: false, versionKey: false });

module.exports = mongoConnections('master').model('MissionLog', MissionLogSchema);

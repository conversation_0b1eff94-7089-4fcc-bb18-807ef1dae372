const mongoose = require('mongoose');
const mongoConnections = require("../connections/mongo")
const Schema = mongoose.Schema

/**
 * Model quản lý lịch nghỉ phép / công tác của cán bộ
 * Yêu cầu: chỉ hỗ trợ 2 loại: nghỉ phép (leave) và công tác (business_trip)
 */
const LeaveRequestSchema = new mongoose.Schema(
  {
    // Cán bộ tạo lịch
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },

    // Loại lịch (nghỉ/công tác)
    type: {
      type: String,
      enum: ['leave', 'business_trip'],
      required: true
    },

    // Ng<PERSON>y bắt đầu (format: DD-MM-YYYY)
    startDate: {
      type: String,
      required: true,
      validate: {
        validator: function(v) {
          // Kiểm tra định dạng DD-MM-YYYY
          return /^\d{2}-\d{2}-\d{4}$/.test(v);
        },
        message: '<PERSON><PERSON><PERSON> bắt đầu phải có định dạng DD-MM-YYYY'
      }
    },

    // Ngày kết thúc (format: DD-MM-YYYY)
    endDate: {
      type: String,
      required: true,
      validate: {
        validator: function(v) {
          // Kiểm tra định dạng DD-MM-YYYY
          return /^\d{2}-\d{2}-\d{4}$/.test(v);
        },
        message: 'Ngày kết thúc phải có định dạng DD-MM-YYYY'
      }
    },

    // Timestamp bắt đầu (0h00 ngày startDate)
    startTime: {
      type: Number,
      index: true // Index để tối ưu query
    },

    // Timestamp kết thúc (0h00 ngày endDate + 1)
    endTime: {
      type: Number,
      index: true // Index để tối ưu query
    },

    // Lý do xin nghỉ
    reason: {
      type: String
    },

    // Danh sách URL file đính kèm (sử dụng upload service hiện có)
    attachments: [{
      type: String // URL của file đã upload
    }],

    // Trạng thái duyệt
    status: {
      type: String,
      enum: ['pending', 'approved', 'rejected', 'deleted'],
      default: 'approved'
    },

    // Người duyệt lịch
    approvedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },

    // Thời gian duyệt
    approvedAt: {
      type: Number
    },

    // Ghi chú từ người duyệt
    approvalNote: {
      type: String
    },

    // Metadata
    createdAt: {
      type: Number,
      default: Date.now
    },
    updatedAt: {
      type: Number,
      default: Date.now
    }
  },
  { id: false, versionKey: false }
)

// Validate logic giữa startDate và endDate (endDate >= startDate)
LeaveRequestSchema.pre('validate', function(next) {
  try {
    const start = this.startDate && this.startDate.split('-').reverse().join('-'); // YYYY-MM-DD
    const end = this.endDate && this.endDate.split('-').reverse().join('-'); // YYYY-MM-DD
    if (start && end && new Date(end).getTime() < new Date(start).getTime()) {
      return next(new Error('Ngày kết thúc không được nhỏ hơn ngày bắt đầu'));
    }
    next();
  } catch (e) {
    next(e);
  }
});

// Cập nhật mốc thời gian và tính toán startTime/endTime
LeaveRequestSchema.pre('save', function(next) {
  this.updatedAt = Date.now();

  // Tự động tính toán startTime và endTime từ startDate và endDate
  if (this.startDate) {
    // startTime = 0h00 ngày startDate
    const [day, month, year] = this.startDate.split('-').map(Number);
    const startDateTime = new Date(year, month - 1, day, 0, 0, 0, 0);
    this.startTime = startDateTime.getTime();
  }

  if (this.endDate) {
    // endTime = 0h00 ngày endDate + 1 (tức là cuối ngày endDate)
    const [day, month, year] = this.endDate.split('-').map(Number);
    const endDateTime = new Date(year, month - 1, day + 1, 0, 0, 0, 0);
    this.endTime = endDateTime.getTime();
  }

  next();
});

// Indexes cho performance
LeaveRequestSchema.index({ user: 1, createdAt: -1 }) // Lịch sử đơn xin nghỉ của user
LeaveRequestSchema.index({ status: 1, createdAt: -1 }) // Đơn theo trạng thái duyệt
LeaveRequestSchema.index({ approvedBy: 1, createdAt: -1 }) // Đơn đã duyệt
LeaveRequestSchema.index({ startDate: 1, endDate: 1 }) // Tìm theo khoảng thời gian (legacy)
LeaveRequestSchema.index({ startTime: 1, endTime: 1 }) // Tìm theo timestamp (optimized)
LeaveRequestSchema.index({ user: 1, startTime: 1, endTime: 1, status: 1 }) // Compound index cho query chính
LeaveRequestSchema.index({ type: 1, status: 1 }) // Thống kê theo loại và trạng thái

module.exports = mongoConnections("master").model("LeaveRequest", LeaveRequestSchema)

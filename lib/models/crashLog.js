const mongoose = require('mongoose');
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')

const CrashLog = new mongoose.Schema({
  error: {
    type: mongoose.Schema.Types.Mixed
  },
  device: {
    type: mongoose.Schema.Types.Mixed
  },
  appState: {
    type: mongoose.Schema.Types.Mixed
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  appName: {
    type: String
  },
  member: {
    type: mongoose.Schema.Types.Object
  },
  versionCodePush: {
    type: String
  }
}, { id: false, versionKey: false });

module.exports = mongoConnections('master').model('CrashLog', CrashLog);

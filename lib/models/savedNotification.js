const mongoose = require('mongoose');
const mongoConnections = require('../connections/mongo')

var SavedNotification = new mongoose.Schema({
  image: { type: String, default: 'https://media-hb-dev.canbo.ai/uploads/avatar/2025-08-21-images%201.png' },
  title: { type: String },
  description: { type: String },
  titlePush: { type: String },
  messagePush: { type: String },
  data: { type: mongoose.Schema.Types.Mixed },
  seen: { type: Array, default: [] },
  status: { type: Number, default: 1 },
  statusPush: { type: Number, default: 0 },
  units: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Unit'
  }],
  users: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  type: { 
    type: String, 
    enum: ['unit', 'user', 'all'],
  },
  dutyShift: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'DutyShift'
  },
  updatedAt: { type: Number, default: Date.now },
  createdAt: { type: Number, default: Date.now }
}, { versionKey: false });

module.exports = mongoConnections('master').model('SavedNotification', SavedNotification);

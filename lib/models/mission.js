const mongoose = require('mongoose');
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')
const UserModel = require('./user');
const savedNotification = require('./savedNotification');
const generate = require('nanoid/generate')

const MissionSchema = new mongoose.Schema({
  code: {
    type: String,
    unique: true
  },
  name: {
    type: String,
    required: true
  },
  description: {
    type: String
  },
  status: {
    type: Number,
    default: 1 //1: INITIAL, 2: IN_PROGRESS, 3: COMPLETED, 4: CANCELLED
  },
  location: {
    address: String,
    coordinates: {
      type: [Number],
      index: '2dsphere'
    }, 
  },
  area: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Area'
  },
  type: {
    type: String,
    enum: ['self_assign', 'unit_assign'],
  },
  startTime: { type: Number },
  rejects: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
  assignInfo: [{
    unit: { type: mongoose.Schema.Types.ObjectId, ref: 'Unit' },
    numberOfUsers: Number,
    users: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }]
  }],
  users: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User', default: [] }],
  createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  createdAt: { type: Number, default: Date.now },
  updatedAt: { type: Number, default: Date.now },
  notifyAssign: { type: mongoose.Schema.Types.ObjectId, ref: 'SavedNotification' },
  notifyPush: { type: mongoose.Schema.Types.ObjectId, ref: 'SavedNotification' }
}, { id: false, versionKey: false });

MissionSchema.pre('save', function (next) {
  let model = this
  // Nếu đã có code được truyền vào thì không cần generate mới
  if (model.code) {
    next()
  } else {
    attempToGenerate(model, next)
  }
})

const attempToGenerate = (model, callback) => {
  let newCode = generate('0123456789', 6)
  model.constructor.findOne({
    'code': newCode
  }).then((course) => {
    if (course) {
      attempToGenerate(model, callback)
    } else {
      model.code = newCode
      callback();
    }
  }, (err) => {
    callback(err)
  })
}


module.exports = mongoConnections('master').model('Mission', MissionSchema);

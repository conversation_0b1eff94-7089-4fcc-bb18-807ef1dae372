const mongoConnections = require("../connections/mongo")
const Schema = mongoose.Schema
const CategoryPermissionSchema = new mongoose.Schema(
 {
  name: {
   type: String,
  },
  description: {
    type: String,
  },
  permissions: [{
    type: Schema.Types.ObjectId,
    ref:'Permission'
  }],
  status: {
    type: Number,
    default: 1
  },
  createdAt: {
    type: Number,
    default: Date.now
  }
 },
 { id: false, versionKey: false },
)

module.exports = mongoConnections("master").model("CategoryPermission", CategoryPermissionSchema)

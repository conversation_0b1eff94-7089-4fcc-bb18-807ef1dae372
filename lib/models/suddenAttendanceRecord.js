const mongoose = require('mongoose');
const mongoConnections = require("../connections/mongo")
const Schema = mongoose.Schema

/**
 * Model bản ghi chấm công đột xuất
 * Lưu trữ thông tin chấm công đột xuất thực tế của cán bộ
 */
const SuddenAttendanceRecordSchema = new mongoose.Schema(
  {
    // Phiên chấm công đột xuất
    session: {
      type: Schema.Types.ObjectId,
      ref: 'SuddenAttendanceSession',
      required: true
    },

    // Cán bộ chấm công
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },

    // Thời gian chấm công thực tế (timestamp)
    checkinTime: {
      type: Number,
      required: true
    },

    // Trạng thái chấm công
    status: {
      type: String,
      enum: ['on_time', 'late', 'absent'],
      required: true
    },

    // <PERSON><PERSON> trí chấm công (tùy chọn)
    location: {
      lat: {
        type: Number
      },
      lng: {
        type: Number
      },
      address: {
        type: String
      }
    },

    // <PERSON>hi chú (tùy chọn)
    note: {
      type: String,
      trim: true,
      maxlength: 500
    },

    // Metadata
    createdAt: {
      type: Number,
      default: Date.now
    },
    updatedAt: {
      type: Number,
      default: Date.now
    }
  },
  { id: false, versionKey: false }
)

// Indexes cho performance
SuddenAttendanceRecordSchema.index({ session: 1, user: 1 }, { unique: true }) // Tránh duplicate checkin
SuddenAttendanceRecordSchema.index({ user: 1, createdAt: -1 }) // Lịch sử chấm công của user
SuddenAttendanceRecordSchema.index({ session: 1, status: 1 }) // Thống kê theo phiên và trạng thái
SuddenAttendanceRecordSchema.index({ session: 1, checkinTime: 1 }) // Sắp xếp theo thời gian chấm công
SuddenAttendanceRecordSchema.index({ status: 1, createdAt: -1 }) // Thống kê theo trạng thái

// Middleware để cập nhật updatedAt
SuddenAttendanceRecordSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoConnections("master").model("SuddenAttendanceRecord", SuddenAttendanceRecordSchema)

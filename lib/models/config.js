const mongoose = require("mongoose")
const Schema = mongoose.Schema
const _ = require("lodash")
const mongoConnections = require("../connections/mongo")
const async = require('async');
const ms = require('ms');

const Config = new mongoose.Schema({
  type: {
    type: Number
  },
  region: {
    type: mongoose.Schema.Types.Mixed
  },
  config: {
    type: mongoose.Schema.Types.Mixed
  },
}, { id: false, versionKey: false, strict: false });

module.exports = mongoConnections('master').model('Config', Config);

const mongoose = require('mongoose');
const mongoConnections = require('../connections/mongo')

var HandbookCategory = new mongoose.Schema({
  icon: { type: String, default: 'https://media-hb-dev.canbo.ai/uploads/avatar/2025-08-21-images%201.png' },
  name: { type: String },
  status: { type: Number, default: 1 },
  updatedAt: { type: Number, default: Date.now },
  createdAt: { type: Number, default: Date.now }
}, { versionKey: false });

module.exports = mongoConnections('master').model('HandbookCategory', HandbookCategory);

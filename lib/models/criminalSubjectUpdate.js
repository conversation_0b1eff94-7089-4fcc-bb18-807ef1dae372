const mongoose = require('mongoose');
const mongoConnections = require('../connections/mongo');
const Schema = mongoose.Schema;

const CriminalSubjectUpdateSchema = new mongoose.Schema(
  {
    // Liên kết đến đối tượng hình sự
    subjectId: {
      type: Schema.Types.ObjectId,
      ref: 'CriminalSubject',
      required: true,
      index: true
    },

    // Thông tin cập nhật
    contactDate: {
      type: Number, // Timestamp ngày giờ tiếp xúc
      required: true,
      index: true
    },
    livingCondition: {
      type: String // Tình hình sinh hoạt
    },
    populationMovement: {
      type: String // Di biến động nhân khẩu
    },
    abnormalSigns: {
      type: String // Dấu hiệu bất thường
    },

    // Audit
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    createdAt: {
      type: Number,
      default: Date.now,
      index: true
    },
    status: {
      type: Number,
      default: 1, // 1: active, 0: inactive
      index: true
    }
  },
  { id: false, versionKey: false }
);

// Indexes for optimization
CriminalSubjectUpdateSchema.index({ subjectId: 1, createdAt: -1, status: 1 });
CriminalSubjectUpdateSchema.index({ updatedBy: 1, createdAt: -1, status: 1 });
CriminalSubjectUpdateSchema.index({ contactDate: -1, status: 1 });

module.exports = mongoConnections('master').model('CriminalSubjectUpdate', CriminalSubjectUpdateSchema);

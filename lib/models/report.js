const mongoose = require('mongoose');
const mongoConnections = require("../connections/mongo")
const Schema = mongoose.Schema

const ReportSchema = new mongoose.Schema(
  {
    // Thông tin cơ bản
    title: {
      type: String
    },
    description: {
      type: String
    },

    // Loại báo cáo: 'quick' (nhanh) hoặc 'detail' (chi tiết)
    reportType: {
      type: String,
      enum: ['quick', 'detail'],
      default: 'quick',
      required: true
    },

    // Liên kết với JobType (chính là loại báo cáo)
    jobType: {
      type: Schema.Types.ObjectId,
      ref: 'JobType',
      required: true
    },

    // Dữ liệu số liệu (chỉ lưu số lượng) - dùng cho báo cáo quick
    metrics: {
      type: Schema.Types.Mixed,
      default: {}
    },

    // Mã vụ việc - bắt buộc cho báo cáo detail
    caseCode: {
      type: String,
      sparse: true, // <PERSON> phép null/undefined, nhưng nếu có thì phải unique
      index: true
    },

    // Trạng thái công việc - dùng cho báo cáo detail
    workStatus: {
      type: String,
      enum: ['pending', 'in_progress', 'completed', 'cancelled', 'on_hold'],
      default: 'in_progress'
    },

    // Chi tiết báo cáo đã được migrate sang ReportDetail collection
    // Trường này đã được remove sau khi migration hoàn thành

    // Số lượng details (computed từ ReportDetail collection)
    detailsCount: {
      type: Number,
      default: 0,
      index: true
    },

    // Người tạo báo cáo
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },

    // Đơn vị
    units: [{
      type: Schema.Types.ObjectId,
      ref: 'Unit'
    }],

    // Trạng thái
    status: {
      type: String,
      enum: ['draft', 'submitted', 'approved', 'rejected'],
      default: 'draft'
    },

    date: {
      type: String,
      required: true,
      validate: {
        validator: function(v) {
          // Kiểm tra định dạng DD-MM-YYYY
          return /^\d{2}-\d{2}-\d{4}$/.test(v);
        },
        message: 'Ngày phải có định dạng DD-MM-YYYY'
      }
    },

    dateTimestamp: {
      type: Number,
      required: true
    },

    // Metadata
    createdAt: {
      type: Number,
      default: Date.now
    },
    updatedAt: {
      type: Number,
      default: Date.now
    },
    deletedAt: {
      type: Number
    },
    summary:[{
      area:{
        type: Schema.Types.ObjectId,
        ref: 'Area'
      },
      count: {
        type: Number,
        default: 0
      }
    }]
  },
  { id: false, versionKey: false }
)

// Indexes cho performance
ReportSchema.index({ jobType: 1, createdAt: 1 })
ReportSchema.index({ 'details.location.coordinates': '2dsphere' })
ReportSchema.index({ createdBy: 1, createdAt: -1 })
ReportSchema.index({ unit: 1, createdAt: -1 })

// Indexes mới cho các trường mở rộng
ReportSchema.index({ reportType: 1, createdAt: -1 })
ReportSchema.index({ caseCode: 1 }, { sparse: true }) // Sparse index cho caseCode
ReportSchema.index({ workStatus: 1, createdAt: -1 })
ReportSchema.index({ reportType: 1, workStatus: 1 }) // Compound index cho báo cáo detail
ReportSchema.index({ date: 1, createdAt: -1 })
ReportSchema.index({ dateTimestamp: 1, createdAt: -1 })
ReportSchema.index({ detailsCount: 1, createdAt: -1 }) // Index cho detailsCount

// Virtual field để populate details từ ReportDetail collection
ReportSchema.virtual('details', {
  ref: 'ReportDetail',
  localField: '_id',
  foreignField: 'reportId',
  options: { sort: { time: 1 } } // Sort by time ascending
})

// Đảm bảo virtual fields được serialize
ReportSchema.set('toJSON', {
  virtuals: true,
  transform: function(doc, ret) {
    // Xóa detailsLegacy khỏi JSON output để tránh confusion
    delete ret.detailsLegacy;
    return ret;
  }
})

ReportSchema.set('toObject', {
  virtuals: true,
  transform: function(doc, ret) {
    // Xóa detailsLegacy khỏi object output để tránh confusion
    delete ret.detailsLegacy;
    return ret;
  }
})

// Instance method để lấy details với populate
ReportSchema.methods.getDetailsPopulated = function(populateAreas = true) {
  const query = this.model('ReportDetail').find({ reportId: this._id }).sort({ time: 1 });

  if (populateAreas) {
    query.populate('areas', 'name level');
  }

  return query.lean();
}

// Static method để lấy reports với details
ReportSchema.statics.findWithDetails = function(query = {}, options = {}) {
  const reportQuery = this.find(query);

  if (options.populateDetails) {
    reportQuery.populate({
      path: 'details',
      populate: options.populateAreas ? { path: 'areas', select: 'name level' } : null
    });
  }

  return reportQuery;
}

module.exports = mongoConnections("master").model("Report", ReportSchema)
const mongoose = require('mongoose');
const mongoConnections = require("../connections/mongo")
const Schema = mongoose.Schema

/**
 * Model phiên chấm công đột xuất
 * Quản lý các phiên chấm công đột xuất do quản trị viên tạo
 */
const SuddenAttendanceSessionSchema = new mongoose.Schema(
  {
    // Tên/chức danh chấm công
    title: {
      type: String,
      required: true,
      trim: true,
      maxlength: 200
    },

    // <PERSON><PERSON> tả chi tiết (tùy chọn)
    description: {
      type: String,
      trim: true,
      maxlength: 500
    },

    // Thời gian bắt đầu chấm công (timestamp)
    startTime: {
      type: Number,
      required: true
    },

    // Khung thời gian chấm công hợp lệ (phút kể từ startTime)
    validDurationMinutes: {
      type: Number,
      required: true,
      min: 1,
      max: 60,
      default: 10 // Mặc định 10 phút
    },

    // Trụ sở áp dụng
    headquarters: {
      type: String,
      enum: ['main', 'sub'],
      default: 'main'
    },

    // Đơn vị áp dụng (tùy chọn - nếu không có thì áp dụng cho tất cả)
    targetUnits: [{
      type: Schema.Types.ObjectId,
      ref: 'Unit'
    }],

    // Danh sách cán bộ được áp dụng (tự động tính từ targetUnits hoặc tất cả)
    targetUsers: [{
      type: Schema.Types.ObjectId,
      ref: 'User'
    }],

    // Danh sách cán bộ được miễn điểm danh (công tác/nghỉ phép)
    exemptedUsers: [{
      user: {
        type: Schema.Types.ObjectId,
        ref: 'User'
      },
      reason: {
        type: String,
        enum: ['business_trip', 'excused'],
        required: true
      },
      note: {
        type: String,
        maxlength: 500
      }
    }],

    // Trạng thái phiên chấm công
    status: {
      type: String,
      enum: ['scheduled', 'active', 'completed', 'cancelled'],
      default: 'scheduled'
    },

    // Người tạo phiên chấm công
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },

    // Thống kê nhanh
    statistics: {
      totalTargetUsers: {
        type: Number,
        default: 0
      },
      totalCheckedIn: {
        type: Number,
        default: 0
      },
      totalUnchecked: {
        type: Number,
        default: 0
      },
      onTimeCount: {
        type: Number,
        default: 0
      },
      lateCount: {
        type: Number,
        default: 0
      },
      absentCount: {
        type: Number,
        default: 0
      },
      exemptedCount: {
        type: Number,
        default: 0
      }
    },

    savedNotification: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'SavedNotification'
    },

    // Metadata
    createdAt: {
      type: Number,
      default: Date.now
    },
    updatedAt: {
      type: Number,
      default: Date.now
    }
  },
  { id: false, versionKey: false }
)

// Indexes cho performance
SuddenAttendanceSessionSchema.index({ createdBy: 1, createdAt: -1 }) // Lịch sử tạo phiên
SuddenAttendanceSessionSchema.index({ status: 1, startTime: 1 }) // Tìm phiên theo trạng thái và thời gian
SuddenAttendanceSessionSchema.index({ startTime: 1 }) // Sắp xếp theo thời gian
SuddenAttendanceSessionSchema.index({ targetUsers: 1, status: 1 }) // Tìm phiên của user
SuddenAttendanceSessionSchema.index({ targetUnits: 1, status: 1 }) // Tìm phiên theo đơn vị
SuddenAttendanceSessionSchema.index({ headquarters: 1, status: 1 }) // Tìm phiên theo trụ sở

// Middleware để cập nhật updatedAt
SuddenAttendanceSessionSchema.pre('save', function (next) {
  this.updatedAt = Date.now();
  next();
});

// Virtual để kiểm tra có thể chỉnh sửa không
SuddenAttendanceSessionSchema.virtual('canEdit').get(function () {
  const now = Date.now();
  const oneMinuteBeforeStart = this.startTime - (1 * 60 * 1000); // 1 phút trước giờ bắt đầu
  return now < oneMinuteBeforeStart && this.status === 'scheduled';
});

// Virtual để kiểm tra phiên có đang active không
SuddenAttendanceSessionSchema.virtual('isActive').get(function () {
  const now = Date.now();
  const endTime = this.startTime + (this.validDurationMinutes * 60 * 1000);
  return now >= this.startTime && now <= endTime && this.status === 'active';
});

// Virtual để kiểm tra có thể chấm công muộn không
SuddenAttendanceSessionSchema.virtual('canCheckinLate').get(function () {
  const now = Date.now();
  const lateEndTime = this.startTime + (60 * 60 * 1000); // 1 giờ sau giờ bắt đầu
  const validEndTime = this.startTime + (this.validDurationMinutes * 60 * 1000);
  return now > validEndTime && now <= lateEndTime && this.status === 'active';
});

module.exports = mongoConnections("master").model("SuddenAttendanceSession", SuddenAttendanceSessionSchema)

/**
 * Attendance system error definitions và handling
 * Định nghĩa các lỗi cụ thể cho hệ thống điểm danh
 */

class AttendanceError extends Error {
  constructor(code, message, details = null) {
    super(message);
    this.name = 'AttendanceError';
    this.code = code;
    this.details = details;
    this.timestamp = new Date().toISOString();
  }
}

// Error codes cho hệ thống điểm danh
const ERROR_CODES = {
  // Schedule errors (1000-1099)
  SCHEDULE_NOT_FOUND: 1001,
  SCHEDULE_PERMISSION_DENIED: 1002,
  SCHEDULE_ALREADY_EXISTS: 1003,
  SCHEDULE_INVALID_DATE_RANGE: 1004,
  SCHEDULE_INVALID_SHIFT: 1005,

  // Attendance errors (1100-1199)
  ATTENDANCE_NOT_FOUND: 1101,
  ATTENDANCE_ALREADY_CHECKED_IN: 1102,
  ATTENDANCE_NO_SCHEDULE: 1103,
  ATTENDANCE_WRONG_TIME: 1104,
  ATTENDANCE_PERMISSION_DENIED: 1105,
  ATTENDANCE_INVALID_LOCATION: 1106,

  // Leave request errors (1200-1299)
  LEAVE_REQUEST_NOT_FOUND: 1201,
  LEAVE_REQUEST_ALREADY_PROCESSED: 1202,
  LEAVE_REQUEST_PERMISSION_DENIED: 1203,
  LEAVE_REQUEST_INVALID_DATE: 1204,
  LEAVE_REQUEST_INVALID_TYPE: 1205,
  LEAVE_REQUEST_MISSING_REASON: 1206,

  // Notification errors (1300-1399)
  NOTIFICATION_SEND_FAILED: 1301,
  NOTIFICATION_INVALID_USER: 1302,
  NOTIFICATION_SCHEDULE_FAILED: 1303,

  // Permission errors (1400-1499)
  PERMISSION_DENIED: 1401,
  PERMISSION_INVALID_UNIT: 1402,
  PERMISSION_USER_NOT_FOUND: 1403,

  // System errors (1500-1599)
  DATABASE_ERROR: 1501,
  CACHE_ERROR: 1502,
  EXTERNAL_SERVICE_ERROR: 1503,
  VALIDATION_ERROR: 1504
};

// Error messages
const ERROR_MESSAGES = {
  [ERROR_CODES.SCHEDULE_NOT_FOUND]: 'Không tìm thấy lịch làm việc',
  [ERROR_CODES.SCHEDULE_PERMISSION_DENIED]: 'Không có quyền tạo/sửa lịch làm việc',
  [ERROR_CODES.SCHEDULE_ALREADY_EXISTS]: 'Lịch làm việc đã tồn tại',
  [ERROR_CODES.SCHEDULE_INVALID_DATE_RANGE]: 'Khoảng thời gian không hợp lệ',
  [ERROR_CODES.SCHEDULE_INVALID_SHIFT]: 'Ca làm việc không hợp lệ',

  [ERROR_CODES.ATTENDANCE_NOT_FOUND]: 'Không tìm thấy bản ghi điểm danh',
  [ERROR_CODES.ATTENDANCE_ALREADY_CHECKED_IN]: 'Đã điểm danh cho ca làm việc này',
  [ERROR_CODES.ATTENDANCE_NO_SCHEDULE]: 'Không có lịch làm việc để điểm danh',
  [ERROR_CODES.ATTENDANCE_WRONG_TIME]: 'Không đúng thời gian điểm danh',
  [ERROR_CODES.ATTENDANCE_PERMISSION_DENIED]: 'Không có quyền điểm danh',
  [ERROR_CODES.ATTENDANCE_INVALID_LOCATION]: 'Vị trí điểm danh không hợp lệ',

  [ERROR_CODES.LEAVE_REQUEST_NOT_FOUND]: 'Không tìm thấy đơn xin nghỉ',
  [ERROR_CODES.LEAVE_REQUEST_ALREADY_PROCESSED]: 'Đơn xin nghỉ đã được xử lý',
  [ERROR_CODES.LEAVE_REQUEST_PERMISSION_DENIED]: 'Không có quyền duyệt đơn xin nghỉ',
  [ERROR_CODES.LEAVE_REQUEST_INVALID_DATE]: 'Ngày xin nghỉ không hợp lệ',
  [ERROR_CODES.LEAVE_REQUEST_INVALID_TYPE]: 'Loại đơn xin nghỉ không hợp lệ',
  [ERROR_CODES.LEAVE_REQUEST_MISSING_REASON]: 'Phải nhập lý do xin nghỉ',

  [ERROR_CODES.NOTIFICATION_SEND_FAILED]: 'Gửi thông báo thất bại',
  [ERROR_CODES.NOTIFICATION_INVALID_USER]: 'Người dùng không hợp lệ cho thông báo',
  [ERROR_CODES.NOTIFICATION_SCHEDULE_FAILED]: 'Lên lịch thông báo thất bại',

  [ERROR_CODES.PERMISSION_DENIED]: 'Không có quyền thực hiện thao tác này',
  [ERROR_CODES.PERMISSION_INVALID_UNIT]: 'Đơn vị không hợp lệ',
  [ERROR_CODES.PERMISSION_USER_NOT_FOUND]: 'Không tìm thấy thông tin người dùng',

  [ERROR_CODES.DATABASE_ERROR]: 'Lỗi cơ sở dữ liệu',
  [ERROR_CODES.CACHE_ERROR]: 'Lỗi cache',
  [ERROR_CODES.EXTERNAL_SERVICE_ERROR]: 'Lỗi dịch vụ bên ngoài',
  [ERROR_CODES.VALIDATION_ERROR]: 'Dữ liệu đầu vào không hợp lệ'
};

class AttendanceErrorHandler {
  /**
   * Tạo AttendanceError
   * @param {Number} code - Error code
   * @param {String} customMessage - Custom message (optional)
   * @param {Object} details - Error details (optional)
   * @returns {AttendanceError}
   */
  static createError(code, customMessage = null, details = null) {
    const message = customMessage || ERROR_MESSAGES[code] || 'Lỗi không xác định';
    return new AttendanceError(code, message, details);
  }

  /**
   * Handle và log error
   * @param {Error} error - Error object
   * @param {String} context - Context where error occurred
   * @param {Object} metadata - Additional metadata
   * @returns {Object} Formatted error response
   */
  static handleError(error, context = '', metadata = {}) {
    const errorInfo = {
      timestamp: new Date().toISOString(),
      context,
      metadata,
      error: {
        name: error.name,
        message: error.message,
        code: error.code || 'UNKNOWN',
        stack: error.stack
      }
    };

    // Log error
    if (global.logger) {
      global.logger.logError([`[${context}] ${error.message}`], errorInfo);
    } else {
      console.error(`[${context}] Error:`, errorInfo);
    }

    // Send email alert for critical errors
    if (this.isCriticalError(error)) {
      this.sendErrorAlert(errorInfo);
    }

    // Return formatted response
    return this.formatErrorResponse(error);
  }

  /**
   * Kiểm tra có phải critical error không
   * @param {Error} error - Error object
   * @returns {Boolean}
   */
  static isCriticalError(error) {
    const criticalCodes = [
      ERROR_CODES.DATABASE_ERROR,
      ERROR_CODES.EXTERNAL_SERVICE_ERROR
    ];

    return criticalCodes.includes(error.code) ||
           (error.name === 'MongoError') ||
           (error.message && error.message.includes('connection'));
  }

  /**
   * Gửi email cảnh báo lỗi
   * @param {Object} errorInfo - Error information
   */
  static sendErrorAlert(errorInfo) {
    try {
      if (global.MailUtil) {
        const subject = `[CRITICAL] Attendance System Error - ${errorInfo.context}`;
        const body = `
Critical error occurred in Attendance System:

Context: ${errorInfo.context}
Time: ${errorInfo.timestamp}
Error: ${errorInfo.error.message}
Code: ${errorInfo.error.code}

Metadata: ${JSON.stringify(errorInfo.metadata, null, 2)}

Stack Trace:
${errorInfo.error.stack}
        `;

        global.MailUtil.sendMail(body);
      }
    } catch (emailError) {
      console.error('Failed to send error alert email:', emailError);
    }
  }

  /**
   * Format error response cho API
   * @param {Error} error - Error object
   * @returns {Object} Formatted response
   */
  static formatErrorResponse(error) {
    if (error instanceof AttendanceError) {
      return {
        success: false,
        code: error.code,
        message: error.message,
        details: error.details,
        timestamp: error.timestamp
      };
    }

    // Handle MongoDB errors
    if (error.name === 'MongoError' || error.name === 'ValidationError') {
      return {
        success: false,
        code: ERROR_CODES.DATABASE_ERROR,
        message: ERROR_MESSAGES[ERROR_CODES.DATABASE_ERROR],
        details: process.env.NODE_ENV === 'development' ? error.message : null
      };
    }

    // Handle Joi validation errors
    if (error.name === 'ValidationError' && error.details) {
      return {
        success: false,
        code: ERROR_CODES.VALIDATION_ERROR,
        message: ERROR_MESSAGES[ERROR_CODES.VALIDATION_ERROR],
        details: error.details.map(d => d.message)
      };
    }

    // Generic error
    return {
      success: false,
      code: 'UNKNOWN_ERROR',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Đã xảy ra lỗi hệ thống',
      details: null
    };
  }

  /**
   * Wrap async function với error handling
   * @param {Function} fn - Async function to wrap
   * @param {String} context - Context name
   * @returns {Function} Wrapped function
   */
  static wrapAsync(fn, context) {
    return async (...args) => {
      try {
        return await fn(...args);
      } catch (error) {
        return this.handleError(error, context, { args });
      }
    };
  }

  /**
   * Middleware để handle errors trong routes
   * @param {String} context - Context name
   * @returns {Function} Express middleware
   */
  static middleware(context) {
    return (error, req, res, next) => {
      const errorResponse = this.handleError(error, context, {
        url: req.originalUrl,
        method: req.method,
        body: req.body,
        user: req.user?.id
      });

      res.status(500).json(errorResponse);
    };
  }
}

module.exports = {
  AttendanceError,
  ERROR_CODES,
  ERROR_MESSAGES,
  AttendanceErrorHandler
};
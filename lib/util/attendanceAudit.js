/**
 * Audit logging utility cho hệ thống điểm danh
 * <PERSON>hi lại tất cả các hoạt động quan trọng
 */

const moment = require('moment');

class AttendanceAudit {
  /**
   * Log hoạt động tạo lịch làm việc
   * @param {String} creatorId - ID người tạo
   * @param {Object} scheduleData - D<PERSON> liệu lịch làm việc
   * @param {Object} result - Kết quả tạo lịch
   */
  static logScheduleCreation(creatorId, scheduleData, result) {
    const logData = {
      action: 'CREATE_WORK_SCHEDULE',
      actor: creatorId,
      timestamp: new Date().toISOString(),
      data: {
        userIds: scheduleData.userIds,
        dateRange: scheduleData.dateRange,
        shifts: scheduleData.shifts,
        result: {
          success: result.success,
          created: result.data?.created || 0,
          errors: result.data?.errors?.length || 0
        }
      },
      ip: this.getClientIP(),
      userAgent: this.getUserAgent()
    };

    this.writeAuditLog(logData);
  }

  /**
   * Log hoạt động điểm danh
   * @param {String} userId - ID cán bộ
   * @param {Object} checkinData - Dữ liệu điểm danh
   * @param {Object} result - Kết quả điểm danh
   */
  static logCheckin(userId, checkinData, result) {
    const logData = {
      action: 'CHECKIN_ATTENDANCE',
      actor: userId,
      timestamp: new Date().toISOString(),
      data: {
        scheduleId: checkinData.scheduleId,
        location: checkinData.location,
        result: {
          success: result.success,
          status: result.data?.status,
          checkinTime: result.data?.checkinTime
        }
      },
      ip: this.getClientIP(),
      userAgent: this.getUserAgent()
    };

    this.writeAuditLog(logData);
  }

  /**
   * Log hoạt động tạo đơn xin nghỉ
   * @param {String} userId - ID cán bộ
   * @param {Object} requestData - Dữ liệu đơn xin nghỉ
   * @param {Object} result - Kết quả tạo đơn
   */
  static logLeaveRequestCreation(userId, requestData, result) {
    const logData = {
      action: 'CREATE_LEAVE_REQUEST',
      actor: userId,
      timestamp: new Date().toISOString(),
      data: {
        type: requestData.type,
        startDate: requestData.startDate,
        endDate: requestData.endDate,
        dayCount: requestData.dayCount,
        hasAttachments: requestData.attachments?.length > 0,
        result: {
          success: result.success,
          requestId: result.data?._id
        }
      },
      ip: this.getClientIP(),
      userAgent: this.getUserAgent()
    };

    this.writeAuditLog(logData);
  }

  /**
   * Log hoạt động duyệt đơn xin nghỉ
   * @param {String} approverId - ID người duyệt
   * @param {String} requestId - ID đơn xin nghỉ
   * @param {String} status - Trạng thái duyệt
   * @param {Object} result - Kết quả duyệt
   */
  static logLeaveRequestApproval(approverId, requestId, status, result) {
    const logData = {
      action: 'APPROVE_LEAVE_REQUEST',
      actor: approverId,
      timestamp: new Date().toISOString(),
      data: {
        requestId,
        approvalStatus: status,
        targetUser: result.data?.user,
        result: {
          success: result.success
        }
      },
      ip: this.getClientIP(),
      userAgent: this.getUserAgent()
    };

    this.writeAuditLog(logData);
  }

  /**
   * Log hoạt động xem thống kê
   * @param {String} viewerId - ID người xem
   * @param {Object} filters - Bộ lọc thống kê
   * @param {Object} result - Kết quả thống kê
   */
  static logStatisticsView(viewerId, filters, result) {
    const logData = {
      action: 'VIEW_ATTENDANCE_STATISTICS',
      actor: viewerId,
      timestamp: new Date().toISOString(),
      data: {
        filters: {
          targetUserId: filters.userId,
          unitId: filters.unitId,
          dateRange: {
            startDate: filters.startDate,
            endDate: filters.endDate
          }
        },
        result: {
          success: result.success,
          recordCount: result.data?.statistics?.length || 0,
          scope: result.data?.scope
        }
      },
      ip: this.getClientIP(),
      userAgent: this.getUserAgent()
    };

    this.writeAuditLog(logData);
  }

  /**
   * Log hoạt động gửi thông báo
   * @param {String} type - Loại thông báo
   * @param {Array} userIds - Danh sách user nhận thông báo
   * @param {Object} result - Kết quả gửi thông báo
   */
  static logNotificationSent(type, userIds, result) {
    const logData = {
      action: 'SEND_NOTIFICATION',
      actor: 'SYSTEM',
      timestamp: new Date().toISOString(),
      data: {
        notificationType: type,
        recipientCount: userIds.length,
        result: {
          success: result.success,
          sent: result.data?.sent || 0,
          failed: result.data?.failed || 0
        }
      }
    };

    this.writeAuditLog(logData);
  }

  /**
   * Log lỗi hệ thống
   * @param {Error} error - Error object
   * @param {String} context - Context where error occurred
   * @param {Object} metadata - Additional metadata
   */
  static logSystemError(error, context, metadata = {}) {
    const logData = {
      action: 'SYSTEM_ERROR',
      actor: 'SYSTEM',
      timestamp: new Date().toISOString(),
      data: {
        context,
        error: {
          name: error.name,
          message: error.message,
          code: error.code,
          stack: error.stack
        },
        metadata
      }
    };

    this.writeAuditLog(logData);
  }

  /**
   * Log hoạt động đăng nhập/đăng xuất liên quan đến attendance
   * @param {String} userId - ID user
   * @param {String} action - LOGIN hoặc LOGOUT
   * @param {Object} metadata - Additional data
   */
  static logUserSession(userId, action, metadata = {}) {
    const logData = {
      action: `USER_${action}`,
      actor: userId,
      timestamp: new Date().toISOString(),
      data: metadata,
      ip: this.getClientIP(),
      userAgent: this.getUserAgent()
    };

    this.writeAuditLog(logData);
  }

  /**
   * Ghi audit log vào database hoặc file
   * @param {Object} logData - Dữ liệu log
   */
  static writeAuditLog(logData) {
    try {
      // Ghi vào database nếu có SystemLogModel
      if (global.SystemLogModel) {
        global.SystemLogModel.create({
          user: logData.actor,
          action: logData.action,
          description: this.getActionDescription(logData.action),
          data: logData.data,
          ip: logData.ip,
          userAgent: logData.userAgent,
          timestamp: logData.timestamp
        });
      }

      // Ghi vào logger
      if (global.logger) {
        global.logger.logInfo(`[AUDIT] ${logData.action}`, logData);
      } else {
        console.log(`[AUDIT] ${logData.timestamp} - ${logData.action}:`, logData);
      }

      // Ghi vào file riêng cho audit (optional)
      this.writeToAuditFile(logData);

    } catch (error) {
      console.error('Failed to write audit log:', error);
    }
  }

  /**
   * Ghi audit log vào file riêng
   * @param {Object} logData - Dữ liệu log
   */
  static writeToAuditFile(logData) {
    try {
      const fs = require('fs');
      const path = require('path');

      const auditDir = path.join(process.cwd(), 'logs', 'audit');
      const auditFile = path.join(auditDir, `attendance-audit-${moment().format('YYYY-MM-DD')}.log`);

      // Tạo thư mục nếu chưa có
      if (!fs.existsSync(auditDir)) {
        fs.mkdirSync(auditDir, { recursive: true });
      }

      // Ghi log
      const logLine = JSON.stringify(logData) + '\n';
      fs.appendFileSync(auditFile, logLine);

    } catch (error) {
      console.error('Failed to write audit file:', error);
    }
  }

  /**
   * Lấy mô tả cho action
   * @param {String} action - Action name
   * @returns {String} Action description
   */
  static getActionDescription(action) {
    const descriptions = {
      'CREATE_WORK_SCHEDULE': 'Tạo lịch làm việc',
      'CHECKIN_ATTENDANCE': 'Điểm danh',
      'CREATE_LEAVE_REQUEST': 'Tạo đơn xin nghỉ',
      'APPROVE_LEAVE_REQUEST': 'Duyệt đơn xin nghỉ',
      'VIEW_ATTENDANCE_STATISTICS': 'Xem thống kê điểm danh',
      'SEND_NOTIFICATION': 'Gửi thông báo',
      'SYSTEM_ERROR': 'Lỗi hệ thống',
      'USER_LOGIN': 'Đăng nhập',
      'USER_LOGOUT': 'Đăng xuất'
    };

    return descriptions[action] || action;
  }

  /**
   * Lấy IP client (mock - cần implement thực tế)
   * @returns {String} Client IP
   */
  static getClientIP() {
    // Trong thực tế sẽ lấy từ request object
    return 'unknown';
  }

  /**
   * Lấy User Agent (mock - cần implement thực tế)
   * @returns {String} User Agent
   */
  static getUserAgent() {
    // Trong thực tế sẽ lấy từ request object
    return 'unknown';
  }

  /**
   * Tìm kiếm audit logs
   * @param {Object} filters - Bộ lọc
   * @returns {Array} Danh sách audit logs
   */
  static async searchAuditLogs(filters = {}) {
    try {
      if (!global.SystemLogModel) {
        return [];
      }

      const query = {};

      if (filters.userId) {
        query.user = filters.userId;
      }

      if (filters.action) {
        query.action = filters.action;
      }

      if (filters.startDate && filters.endDate) {
        query.timestamp = {
          $gte: filters.startDate,
          $lte: filters.endDate
        };
      }

      const logs = await global.SystemLogModel.find(query)
        .sort({ timestamp: -1 })
        .limit(filters.limit || 100)
        .lean();

      return logs;

    } catch (error) {
      console.error('Error searching audit logs:', error);
      return [];
    }
  }

  /**
   * Tạo báo cáo audit
   * @param {String} startDate - Ngày bắt đầu
   * @param {String} endDate - Ngày kết thúc
   * @returns {Object} Báo cáo audit
   */
  static async generateAuditReport(startDate, endDate) {
    try {
      const logs = await this.searchAuditLogs({ startDate, endDate });

      const report = {
        period: { startDate, endDate },
        totalLogs: logs.length,
        actionSummary: {},
        userActivity: {},
        errorCount: 0
      };

      logs.forEach(log => {
        // Thống kê theo action
        report.actionSummary[log.action] = (report.actionSummary[log.action] || 0) + 1;

        // Thống kê theo user
        if (log.user) {
          report.userActivity[log.user] = (report.userActivity[log.user] || 0) + 1;
        }

        // Đếm lỗi
        if (log.action === 'SYSTEM_ERROR') {
          report.errorCount++;
        }
      });

      return report;

    } catch (error) {
      console.error('Error generating audit report:', error);
      return null;
    }
  }
}

module.exports = AttendanceAudit;
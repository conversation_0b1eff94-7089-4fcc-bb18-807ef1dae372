/**
 * Redis caching utility cho hệ thống điểm danh
 * Tối ưu hóa performance cho các truy vấn thường xuyên
 */

const redisConnection = require('../connections/redis');

class AttendanceCache {
  constructor() {
    this.redis = redisConnection('master').getConnection();
    this.TTL = {
      USER_SCHEDULE: 3600, // 1 hour
      ATTENDANCE_STATS: 1800, // 30 minutes
      NOTIFICATION_SCHEDULE: 600, // 10 minutes
      PERMISSION_CHECK: 300 // 5 minutes
    };
  }

  /**
   * Tạo cache key
   * @param {String} prefix - Prefix của key
   * @param {Array} params - Tham số để tạo key
   * @returns {String} Cache key
   */
  generateKey(prefix, ...params) {
    return `attendance:${prefix}:${params.join(':')}`;
  }

  /**
   * Cache lịch làm việc của user
   * @param {String} userId - ID user
   * @param {String} startDate - <PERSON><PERSON><PERSON> bắt đầu
   * @param {String} endDate - <PERSON><PERSON><PERSON> kế<PERSON> thú<PERSON>
   * @param {Array} schedules - <PERSON><PERSON> li<PERSON><PERSON> lịch làm việc
   */
  cacheUserSchedule(userId, startDate, endDate, schedules) {
    return new Promise((resolve, reject) => {
      try {
        const key = this.generateKey('user_schedule', userId, startDate, endDate);
        this.redis.setex(key, this.TTL.USER_SCHEDULE, JSON.stringify(schedules), (err, result) => {
          if (err) {
            console.error('Error caching user schedule:', err);
            return reject(err);
          }
          resolve(result);
        });
      } catch (error) {
        console.error('Error caching user schedule:', error);
        reject(error);
      }
    });
  }

  /**
   * Lấy lịch làm việc từ cache
   * @param {String} userId - ID user
   * @param {String} startDate - Ngày bắt đầu
   * @param {String} endDate - Ngày kết thúc
   * @returns {Array|null} Dữ liệu lịch làm việc hoặc null
   */
  getUserSchedule(userId, startDate, endDate) {
    return new Promise((resolve, reject) => {
      try {
        const key = this.generateKey('user_schedule', userId, startDate, endDate);
        this.redis.get(key, (err, cached) => {
          if (err) {
            console.error('Error getting user schedule from cache:', err);
            reject(err);
          }
          resolve(cached ? JSON.parse(cached) : null);
        });
      } catch (error) {
        console.error('Error getting user schedule from cache:', error);
        reject(error);
      }
    });
  }

  /**
   * Xóa cache lịch làm việc của user
   * @param {String} userId - ID user
   */
  invalidateUserSchedule(userId) {
    return new Promise((resolve, reject) => {
      try {
        const pattern = this.generateKey('user_schedule', userId, '*');
        this.redis.keys(pattern, (err, keys) => {
          if (err) {
            console.error('Error getting keys for user schedule cache:', err);
            return reject(err);
          }

          if (keys && keys.length > 0) {
            this.redis.del(...keys, (delErr, result) => {
              if (delErr) {
                console.error('Error deleting user schedule cache keys:', delErr);
                return reject(delErr);
              }
              resolve(result);
            });
          } else {
            resolve(0);
          }
        });
      } catch (error) {
        console.error('Error invalidating user schedule cache:', error);
        reject(error);
      }
    });
  }

  /**
   * Cache thống kê điểm danh
   * @param {String} userId - ID user
   * @param {String} startDate - Ngày bắt đầu
   * @param {String} endDate - Ngày kết thúc
   * @param {Object} statistics - Dữ liệu thống kê
   */
  cacheAttendanceStats(userId, startDate, endDate, statistics) {
    return new Promise((resolve, reject) => {
      try {
        const key = this.generateKey('attendance_stats', userId, startDate, endDate);
        this.redis.setex(key, this.TTL.ATTENDANCE_STATS, JSON.stringify(statistics), (err, result) => {
          if (err) {
            console.error('Error caching attendance stats:', err);
            return reject(err);
          }
          resolve(result);
        });
      } catch (error) {
        console.error('Error caching attendance stats:', error);
        reject(error);
      }
    });
  }

  /**
   * Lấy thống kê điểm danh từ cache
   * @param {String} userId - ID user
   * @param {String} startDate - Ngày bắt đầu
   * @param {String} endDate - Ngày kết thúc
   * @returns {Object|null} Dữ liệu thống kê hoặc null
   */
  getAttendanceStats(userId, startDate, endDate) {
    return new Promise((resolve, reject) => {
      try {
        const key = this.generateKey('attendance_stats', userId, startDate, endDate);
        this.redis.get(key, (err, cached) => {
          if (err) {
            console.error('Error getting attendance stats from cache:', err);
            reject(err);
          }
          resolve(cached ? JSON.parse(cached) : null);
        });
      } catch (error) {
        console.error('Error getting attendance stats from cache:', error);
        reject(error);
      }
    });
  }

  /**
   * Xóa cache thống kê điểm danh
   * @param {String} userId - ID user (optional)
   */
  invalidateAttendanceStats(userId = '*') {
    return new Promise((resolve, reject) => {
      try {
        const pattern = this.generateKey('attendance_stats', userId, '*');
        this.redis.keys(pattern, (err, keys) => {
          if (err) {
            console.error('Error getting keys for attendance stats cache:', err);
            return reject(err);
          }

          if (keys && keys.length > 0) {
            this.redis.del(...keys, (delErr, result) => {
              if (delErr) {
                console.error('Error deleting attendance stats cache keys:', delErr);
                return reject(delErr);
              }
              resolve(result);
            });
          } else {
            resolve(0);
          }
        });
      } catch (error) {
        console.error('Error invalidating attendance stats cache:', error);
        reject(error);
      }
    });
  }

  /**
   * Cache kết quả kiểm tra quyền
   * @param {String} userId - ID user
   * @param {String} action - Hành động
   * @param {String} targetId - ID đối tượng
   * @param {Object} result - Kết quả kiểm tra quyền
   */
  async cachePermissionCheck(userId, action, targetId, result) {
    try {
      const key = this.generateKey('permission', userId, action, targetId);
      await this.redis.setex(key, this.TTL.PERMISSION_CHECK, JSON.stringify(result));
    } catch (error) {
      console.error('Error caching permission check:', error);
    }
  }

  /**
   * Lấy kết quả kiểm tra quyền từ cache
   * @param {String} userId - ID user
   * @param {String} action - Hành động
   * @param {String} targetId - ID đối tượng
   * @returns {Object|null} Kết quả kiểm tra quyền hoặc null
   */
  getPermissionCheck(userId, action, targetId) {
    return new Promise((resolve, reject) => {
      try {
        const key = this.generateKey('permission', userId, action, targetId);
        this.redis.get(key, (err, cached) => {
          if (err) {
            console.error('Error getting permission check from cache:', err);
            reject(err);
          }
          resolve(cached ? JSON.parse(cached) : null);
        });
      } catch (error) {
        console.error('Error getting permission check from cache:', error);
        reject(error);
      }
    });
  }

  /**
   * Cache danh sách user được quản lý
   * @param {String} userId - ID user
   * @param {Array} managedUsers - Danh sách user được quản lý
   */
  async cacheManagedUsers(userId, managedUsers) {
    try {
      const key = this.generateKey('managed_users', userId);
      await this.redis.setex(key, this.TTL.PERMISSION_CHECK, JSON.stringify(managedUsers));
    } catch (error) {
      console.error('Error caching managed users:', error);
    }
  }

  /**
   * Lấy danh sách user được quản lý từ cache
   * @param {String} userId - ID user
   * @returns {Array|null} Danh sách user hoặc null
   */
  getManagedUsers(userId) {
    return new Promise((resolve, reject) => {
      try {
        const key = this.generateKey('managed_users', userId);
        this.redis.get(key, (err, cached) => {
          if (err) {
            console.error('Error getting managed users from cache:', err);
            reject(err);
          }
          resolve(cached ? JSON.parse(cached) : null);
        });
      } catch (error) {
        console.error('Error getting managed users from cache:', error);
        reject(error);
      }
    });
  }

  /**
   * Cache trạng thái điểm danh hôm nay
   * @param {String} userId - ID user
   * @param {String} date - Ngày
   * @param {Object} status - Trạng thái điểm danh
   */
  async cacheTodayStatus(userId, date, status) {
    try {
      const key = this.generateKey('today_status', userId, date);
      await this.redis.setex(key, 3600, JSON.stringify(status)); // Cache 1 hour
    } catch (error) {
      console.error('Error caching today status:', error);
    }
  }

  /**
   * Lấy trạng thái điểm danh hôm nay từ cache
   * @param {String} userId - ID user
   * @param {String} date - Ngày
   * @returns {Object|null} Trạng thái điểm danh hoặc null
   */
  getTodayStatus(userId, date) {
    return new Promise((resolve, reject) => {
      try {
        const key = this.generateKey('today_status', userId, date);
        this.redis.get(key, (err, cached) => {
          if (err) {
            console.error('Error getting today status from cache:', err);
            reject(err);
          }
          resolve(cached ? JSON.parse(cached) : null);
        });
      } catch (error) {
        console.error('Error getting today status from cache:', error);
        reject(error);
      }
    });
  }

  /**
   * Xóa cache trạng thái hôm nay khi có điểm danh mới
   * @param {String} userId - ID user
   * @param {String} date - Ngày
   */
  invalidateTodayStatus(userId, date) {
    return new Promise((resolve, reject) => {
      try {
        const key = this.generateKey('today_status', userId, date);
        this.redis.del(key, (err, result) => {
          if (err) {
            console.error('Error invalidating today status cache:', err);
            return reject(err);
          }
          resolve(result);
        });
      } catch (error) {
        console.error('Error invalidating today status cache:', error);
        reject(error);
      }
    });
  }

  /**
   * Xóa tất cả cache liên quan đến user
   * @param {String} userId - ID user
   */
  invalidateUserCache(userId) {
    return new Promise((resolve, reject) => {
      try {
        const patterns = [
          this.generateKey('user_schedule', userId, '*'),
          this.generateKey('attendance_stats', userId, '*'),
          this.generateKey('permission', userId, '*'),
          this.generateKey('managed_users', userId),
          this.generateKey('today_status', userId, '*')
        ];

        let completedPatterns = 0;
        let totalDeleted = 0;

        if (patterns.length === 0) {
          return resolve(0);
        }

        patterns.forEach((pattern) => {
          this.redis.keys(pattern, (err, keys) => {
            if (err) {
              console.error('Error getting keys for pattern:', pattern, err);
              completedPatterns++;
              if (completedPatterns === patterns.length) {
                resolve(totalDeleted);
              }
              return;
            }

            if (keys && keys.length > 0) {
              this.redis.del(...keys, (delErr, result) => {
                if (delErr) {
                  console.error('Error deleting keys for pattern:', pattern, delErr);
                } else {
                  totalDeleted += result || 0;
                }
                completedPatterns++;
                if (completedPatterns === patterns.length) {
                  resolve(totalDeleted);
                }
              });
            } else {
              completedPatterns++;
              if (completedPatterns === patterns.length) {
                resolve(totalDeleted);
              }
            }
          });
        });
      } catch (error) {
        console.error('Error invalidating user cache:', error);
        reject(error);
      }
    });
  }

  /**
   * Lấy thống kê cache
   * @returns {Object} Thống kê cache
   */
  async getCacheStats() {
    try {
      const info = await this.redis.info('memory');
      const keyCount = await this.redis.dbsize();

      return {
        keyCount,
        memoryInfo: info
      };
    } catch (error) {
      console.error('Error getting cache stats:', error);
      return null;
    }
  }

  /**
   * Xóa tất cả cache của attendance system
   */
  clearAllCache() {
    return new Promise((resolve, reject) => {
      try {
        const pattern = 'attendance:*';
        this.redis.keys(pattern, (err, keys) => {
          if (err) {
            console.error('Error getting keys for clearing all cache:', err);
            return resolve(0);
          }

          if (keys && keys.length > 0) {
            this.redis.del(...keys, (delErr, result) => {
              if (delErr) {
                console.error('Error deleting all cache keys:', delErr);
                return resolve(0);
              }
              resolve(keys.length);
            });
          } else {
            resolve(0);
          }
        });
      } catch (error) {
        console.error('Error clearing all cache:', error);
        resolve(0);
      }
    });
  }
}

module.exports = new AttendanceCache();
/**
 * Utility functions để xử lý cấu trúc phân cấp areas
 *
 * Chức năng:
 * - Chuyển đổi mảng areas phẳng thành cấu trúc phân cấp với areaChilds
 * - Hỗ trợ cho các User APIs trả về cấu trúc phân cấp
 */

/**
 * Xử lý cấu trúc phân cấp areas cho user
 * Chuyển đổi mảng areas phẳng thành cấu trúc phân cấp với areaChilds
 *
 * Input: Mảng areas phẳng với cả level 1 và level 2
 * Output: Mảng chỉ chứa areas level 1, mỗi area có thêm trường areaChilds
 *
 * @param {Array} areas - Mảng areas đã được populate từ database
 * @returns {Array} - Mảng areas với cấu trúc phân cấp
 *
 * @example
 * // Input
 * const areas = [
 *   { _id: '1', name: '<PERSON><PERSON> vực A', level: 1, parent: null },
 *   { _id: '2', name: 'Tổ 1', level: 2, parent: '1' },
 *   { _id: '3', name: 'Tổ 2', level: 2, parent: '1' }
 * ];
 *
 * // Output
 * const result = processAreasHierarchy(areas);
 * // [
 * //   {
 * //     _id: '1', name: 'Khu vực A', level: 1, parent: null,
 * //     areaChilds: [
 * //       { _id: '2', name: 'Tổ 1', level: 2, parent: '1' },
 * //       { _id: '3', name: 'Tổ 2', level: 2, parent: '1' }
 * //     ]
 * //   }
 * // ]
 */
const processAreasHierarchy = (areas) => {
  // Kiểm tra input hợp lệ
  if (!areas || !Array.isArray(areas) || areas.length === 0) {
    return [];
  }

  // Tách khu vực lớn (level 1) và tổ dân phố (level 2)
  const parentAreas = areas.filter(area => area.level === 1);
  const childAreas = areas.filter(area => area.level === 2);

  // Tạo map để nhóm tổ dân phố theo parent
  // Key: parentId (string), Value: array of child areas
  const childAreasByParent = {};

  childAreas.forEach(childArea => {
    // Lấy parent ID từ child area
    // parent có thể là ObjectId hoặc object đã được populate
    const parentId = childArea.parent ?
      (childArea.parent._id || childArea.parent) : null;

    if (parentId) {
      const parentIdStr = parentId.toString();

      // Khởi tạo array nếu chưa có
      if (!childAreasByParent[parentIdStr]) {
        childAreasByParent[parentIdStr] = [];
      }

      // Thêm child area vào group của parent
      childAreasByParent[parentIdStr].push(childArea);
    }
  });

  // Thêm areaChilds vào các khu vực lớn
  const processedAreas = parentAreas.map(parentArea => {
    const parentIdStr = parentArea._id.toString();
    const areaChilds = childAreasByParent[parentIdStr] || [];

    // Trả về parent area với thêm trường areaChilds
    return {
      ...parentArea,
      areaChilds: areaChilds
    };
  });

  return processedAreas;
};

/**
 * Xử lý cấu trúc phân cấp areas cho một user object
 * Wrapper function để xử lý areas của một user cụ thể
 *
 * @param {Object} user - User object có trường areas
 * @returns {Object} - User object với areas đã được xử lý thành cấu trúc phân cấp
 */
const processUserAreasHierarchy = (user) => {
  if (!user || !user.areas || !Array.isArray(user.areas)) {
    return user;
  }

  return {
    ...user,
    areas: processAreasHierarchy(user.areas)
  };
};

/**
 * Xử lý cấu trúc phân cấp areas cho mảng users
 * Áp dụng processUserAreasHierarchy cho từng user trong mảng
 *
 * @param {Array} users - Mảng user objects
 * @returns {Array} - Mảng users với areas đã được xử lý thành cấu trúc phân cấp
 */
const processUsersAreasHierarchy = (users) => {
  if (!users || !Array.isArray(users)) {
    return users;
  }

  return users.map(user => processUserAreasHierarchy(user));
};

/**
 * Validate cấu trúc areas hierarchy
 * Kiểm tra xem cấu trúc areas có đúng format không
 *
 * @param {Array} areas - Mảng areas đã được xử lý
 * @returns {Object} - { isValid: boolean, errors: Array }
 */
const validateAreasHierarchy = (areas) => {
  const errors = [];

  // Kiểm tra areas là array
  if (!Array.isArray(areas)) {
    errors.push('Areas phải là array');
    return { isValid: false, errors };
  }

  // Kiểm tra từng parent area
  areas.forEach((area, index) => {
    // Kiểm tra level
    if (area.level !== 1) {
      errors.push(`Area ${index} phải có level = 1 (khu vực lớn)`);
    }

    // Kiểm tra areaChilds
    if (!Array.isArray(area.areaChilds)) {
      errors.push(`Area ${index} phải có areaChilds là array`);
    } else {
      // Kiểm tra từng child area
      area.areaChilds.forEach((childArea, childIndex) => {
        if (childArea.level !== 2) {
          errors.push(`Child area ${childIndex} của area ${index} phải có level = 2 (tổ dân phố)`);
        }
      });
    }

    // Kiểm tra parent phải là null
    if (area.parent !== null && area.parent !== undefined) {
      errors.push(`Area ${index} (level 1) không được có parent`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors: errors
  };
};

/**
 * Đếm tổng số areas trong cấu trúc phân cấp
 *
 * @param {Array} areas - Mảng areas đã được xử lý
 * @returns {Object} - { parentCount: number, childCount: number, totalCount: number }
 */
const countAreasInHierarchy = (areas) => {
  if (!Array.isArray(areas)) {
    return { parentCount: 0, childCount: 0, totalCount: 0 };
  }

  const parentCount = areas.length;
  const childCount = areas.reduce((sum, area) => {
    return sum + (Array.isArray(area.areaChilds) ? area.areaChilds.length : 0);
  }, 0);

  return {
    parentCount,
    childCount,
    totalCount: parentCount + childCount
  };
};

/**
 * Tìm kiếm area trong cấu trúc phân cấp
 *
 * @param {Array} areas - Mảng areas đã được xử lý
 * @param {String} areaId - ID của area cần tìm
 * @returns {Object|null} - Area object nếu tìm thấy, null nếu không tìm thấy
 */
const findAreaInHierarchy = (areas, areaId) => {
  if (!Array.isArray(areas) || !areaId) {
    return null;
  }

  const areaIdStr = areaId.toString();

  // Tìm trong parent areas
  for (const parentArea of areas) {
    if (parentArea._id.toString() === areaIdStr) {
      return parentArea;
    }

    // Tìm trong child areas
    if (Array.isArray(parentArea.areaChilds)) {
      for (const childArea of parentArea.areaChilds) {
        if (childArea._id.toString() === areaIdStr) {
          return childArea;
        }
      }
    }
  }

  return null;
};

/**
 * Lấy danh sách tất cả area IDs từ cấu trúc phân cấp
 *
 * @param {Array} areas - Mảng areas đã được xử lý
 * @returns {Array} - Mảng area IDs
 */
const getAllAreaIdsFromHierarchy = (areas) => {
  if (!Array.isArray(areas)) {
    return [];
  }

  const areaIds = [];

  areas.forEach(parentArea => {
    // Thêm parent area ID
    areaIds.push(parentArea._id);

    // Thêm child area IDs
    if (Array.isArray(parentArea.areaChilds)) {
      parentArea.areaChilds.forEach(childArea => {
        areaIds.push(childArea._id);
      });
    }
  });

  return areaIds;
};

module.exports = {
  processAreasHierarchy,
  processUserAreasHierarchy,
  processUsersAreasHierarchy,
  validateAreasHierarchy,
  countAreasInHierarchy,
  findAreaInHierarchy,
  getAllAreaIdsFromHierarchy
};

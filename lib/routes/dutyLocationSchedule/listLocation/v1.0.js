const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  const { template, source } = req.body;

  const listLocation = (next) => {

    next(null,{
      code: CONSTANTS.CODE.SUCCESS,
      data: [
        'Cột cờ',
        'Chân cầu'
      ]
    });
  };

  async.waterfall([listLocation], (err, data) => {
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};

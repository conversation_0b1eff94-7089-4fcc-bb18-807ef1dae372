const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const DutyLocationScheduleModel = require('../../../models/dutyLocationSchedule');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  const { _id, templateId, data } = req.body; // _id: ID của schedule, templateId: ID của phần tử trong weeklyScheduleTemplate, data: array ca trực mới (đã bao gồm unit trong mỗi phần tử)

  const checkParams = (next) => {
    if (!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    if (!_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp ID lịch trực chốt điểm'
        }
      });
    }

    if (!templateId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp ID của phần tử template'
        }
      });
    }

    // Validate data
    if (!Array.isArray(data)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'data phải là một array'
        }
      });
    }

    // Validate từng ca trực trong data
    const validateData = (dataList) => {
      return dataList.every(shift => {
        if(!shift.hasEquipment){
          shift.hasEquipment = false; 
        }
        // Validate unit field
        if (!shift.unit || typeof shift.unit !== 'string' || shift.unit.length !== 24) {
          return false;
        }
        
        return typeof shift.startTime === 'number' && 
               typeof shift.endTime === 'number' &&
               shift.startTime > 0 &&
               shift.endTime > 0 &&
               shift.startTime < shift.endTime &&
               typeof shift.hasEquipment === 'boolean'
      });
    };

    if (!validateData(data)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Cấu trúc data không hợp lệ. Mỗi ca trực phải có unit (ObjectId hợp lệ), startTime, endTime và hasEquipment hợp lệ'
        }
      });
    }

    next();
  };

  const validateTimeConflicts = (next) => {
    // Kiểm tra xung đột thời gian trong data
    const conflicts = [];
    
    // Kiểm tra xung đột trong cùng một ngày với cùng unit
    for (let i = 0; i < data.length; i++) {
      for (let j = i + 1; j < data.length; j++) {
        const shift1 = data[i];
        const shift2 = data[j];
        
        // Chỉ kiểm tra xung đột nếu cùng unit
        if (shift1.unit === shift2.unit) {
          // Kiểm tra trùng lặp thời gian (trong cùng ngày, chỉ cần so sánh số milliseconds)
          const isConflict = (
            (shift1.startTime < shift2.endTime && shift1.endTime > shift2.startTime)
          );
          
          if (isConflict) {
            const formatTime = (timestamp) => {
              const date = new Date(timestamp);
              const days = ['Chủ nhật', 'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7'];
              const dayName = days[date.getDay()];
              const hours = date.getHours().toString().padStart(2, '0');
              const minutes = date.getMinutes().toString().padStart(2, '0');
              const day = date.getDate().toString().padStart(2, '0');
              const month = (date.getMonth() + 1).toString().padStart(2, '0');
              return `${dayName} ${hours}:${minutes}`;
            };
            
            conflicts.push({
              shift1: `${formatTime(shift1.startTime)} - ${formatTime(shift1.endTime)}`,
              shift2: `${formatTime(shift2.startTime)} - ${formatTime(shift2.endTime)}`,
              unit: shift1.unit
            });
          }
        }
      }
    }
    
    if (conflicts.length > 0) {
      const conflictMessages = conflicts.map(conflict => 
        `${conflict.shift1} và ${conflict.shift2} (Unit: ${conflict.unit})`
      ).join('; ');
      
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: `Có xung đột thời gian trong ca trực: ${conflictMessages}`
        }
      });
    }
    
    next();
  };

  const updateWeeklyScheduleTemplate = (next) => {
    // Lấy dữ liệu hiện tại
    DutyLocationScheduleModel.findOne({ _id: _id, status: 1 })
      .lean()
      .exec((err, schedule) => {
        if (err) {
          return next(err);
        }

        if (!schedule) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Lịch trực chốt điểm không tồn tại hoặc đã bị xóa'
            }
          });
        }

        // Tạo bản sao của weeklyScheduleTemplate hiện tại
        let updatedTemplate = [...(schedule.weeklyScheduleTemplate || [])];

        // Tìm và cập nhật phần tử có templateId khớp
        const index = updatedTemplate.findIndex(item => item._id.toString() === templateId);
        if (index === -1) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Không tìm thấy phần tử template với ID đã cung cấp'
            }
          });
        }

        // Cập nhật data, giữ nguyên _id, name và dayStartTime
        updatedTemplate[index] = {
          ...updatedTemplate[index],
          data: data     // Cập nhật array data (đã bao gồm unit trong mỗi phần tử)
        };

        // Cập nhật vào database
        DutyLocationScheduleModel.findOneAndUpdate(
          { _id: _id, status: 1 },
          { 
            $set: { 
              weeklyScheduleTemplate: updatedTemplate,
              updatedAt: Date.now()
            }
          },
          { new: true }
        )
        .populate({
          path: 'weeklyScheduleTemplate.data.unit',
          select: 'name'
        })
        .lean()
        .exec((err, updatedSchedule) => {
          if (err) {
            return next(err);
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            message: {
              head: 'Thông báo',
              body: 'Cập nhật lịch trực chốt điểm thành công'
            },
            data: updatedSchedule
          });
        });
      });
  };

  async.waterfall([checkParams, validateTimeConflicts, updateWeeklyScheduleTemplate], (err, data) => {
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};

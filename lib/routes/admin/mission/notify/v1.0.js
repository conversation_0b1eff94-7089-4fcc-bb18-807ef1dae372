const _ = require('lodash')
const async = require('async')
const MissionModel = require('../../../../models/mission')
const MissionLogModel = require('../../../../models/missionLog')
const UserModel = require('../../../../models/user')
const SavedNotificationModel = require('../../../../models/savedNotification')
const PushNotifyManager = require('../../../../jobs/pushNotify')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const logger = require('../../../../logger')
const MailUtil = require('../../../../util/mail')
const DutyShiftModel = require('../../../../models/dutyShift')
const LeaveRequestModel = require('../../../../models/leaveRequest')

module.exports = (req, res) => {
  const {
   _id,
   type = 'all' // 'all' hoặc 'free'
  } = req.body
  const role = req.user?.role || 'officer';
  let unitIds = [];
  const userId = req.user.id;
  let mission; // Declare mission variable to be accessible in all functions

  const validateParams = (next) => {
    if (!_id || !_id.trim()) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'ID nhiệm vụ là bắt buộc'
        }
      });
    }
    if(role === 'officer') {
      return next({
        code: CONSTANTS.CODE.FAIL,
        message: {
          head:'Thông báo',
          body:'Cán bộ không có quyền gửi thông báo huy động'
        }
      });
    }
    next();
  }

  const checkMissionStatus = (next) => {
    MissionModel.findById(_id.trim())
      .populate('assignInfo.unit', 'name')
      .populate('users', '_id')
      .then(missionData => {
        if (!missionData) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: {
              head: 'Thông báo',
              body: 'Không tìm thấy nhiệm vụ'
            }
          });
        }

        if (missionData.status !== 2) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Chỉ có thể gửi thông báo huy động khi nhiệm vụ ở trạng thái Đang huy động lực lượng'
            }
          });
        }

        mission = missionData; // Assign to the outer scope variable
        next(null, mission);
      })
      .catch(err => next(err));
  }

  const getUnitUser = (mission, next) => {
    if(role !== 'team_leader') {
      if(role == 'leader') {
        unitIds = mission.assignInfo.map(info => info.unit._id || info.unit);
      }
      return next(null, mission);
    }

    UserModel
      .findById(userId)
      .select('units')
      .lean()
      .exec((err, user) => {
        if (err) {
          return next(err);
        }
        if (!user) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          });
        }
        if (!user.units || user.units.length !== 2) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: {
              head: 'Thông báo',
              body: 'Bạn không thuộc tổ nào, không thể gửi thông báo huy động'
            }
          });
        }
        unitIds = [user.units[1]]

        next(null, mission);
      })
  }

  const findEligibleStaff = (mission, next) => {
    // Lấy danh sách các unit từ assignInfo
    if (unitIds.length === 0) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Không có tổ nào được phân công trong nhiệm vụ này'
        }
      });
    }

    // Lấy danh sách user đã được assign (loại trừ)
    const excludedUserIds = mission.users.map(user => user._id || user);

    // Tìm tất cả cán bộ thuộc các tổ trong assignInfo, loại trừ những người đã được assign
    UserModel.find({
      units: { $in: unitIds },
      _id: { $nin: excludedUserIds },
      status: 1 
    })
    .populate('units', 'name')
    .populate('positions', 'name')
    .select('_id name units positions')
    .lean()
    .then(eligibleStaff => {
      if (eligibleStaff.length === 0) {
        return next({
          code: CONSTANTS.CODE.FAIL,
          message: {
            head: 'Thông báo',
            body: 'Không có cán bộ nào đủ điều kiện để thông báo huy động'
          }
        });
      }

      next(null, mission, eligibleStaff);
    })
    .catch(err => next(err));
  }

  const filterBusyStaffOnDuty = (mission, eligibleStaff, next) => {
    if (type !== 'free') {
      return next(null, mission, eligibleStaff);
    }

    if (!mission.startTime) {
      return next(null, mission, eligibleStaff);
    }

    // Tìm những cán bộ đang có ca trực trùng với thời gian bắt đầu nhiệm vụ
    const eligibleStaffIds = eligibleStaff.map(staff => staff._id);
    
    DutyShiftModel.find({
      officer: { $in: eligibleStaffIds },
      status: { $in: [0, 1] }, // Chưa xác nhận hoặc đã xác nhận (không bao gồm hủy bỏ)
      startTime: { $lte: mission.startTime },
      endTime: { $gte: mission.startTime }
    })
    .select('officer')
    .lean()
    .then(busyDutyStaff => {
      const busyDutyStaffIds = busyDutyStaff.map(duty => duty.officer.toString());
      const availableStaff = eligibleStaff.filter(staff => 
        !busyDutyStaffIds.includes(staff._id.toString())
      );
      
      console.log(`Filtered out ${busyDutyStaffIds.length} staff members who have duty shifts`);
      next(null, mission, availableStaff);
    })
    .catch(err => next(err));
  }

  const filterBusyStaffOnLeave = (mission, eligibleStaff, next) => {

    if (!mission.startTime) {
      return next(null, mission, eligibleStaff);
    }

    // Tìm những cán bộ đang có lịch nghỉ phép/công tác trùng với thời gian bắt đầu nhiệm vụ
    const eligibleStaffIds = eligibleStaff.map(staff => staff._id);
    
    LeaveRequestModel.find({
      user: { $in: eligibleStaffIds },
      status: 'approved', // Chỉ lấy những lịch đã được duyệt
      startTime: { $lte: mission.startTime },
      endTime: { $gte: mission.startTime }
    })
    .select('user')
    .lean()
    .then(busyLeaveStaff => {
      const busyLeaveStaffIds = busyLeaveStaff.map(leave => leave.user.toString());
      const availableStaff = eligibleStaff.filter(staff => 
        !busyLeaveStaffIds.includes(staff._id.toString())
      );
      
      console.log(`Filtered out ${busyLeaveStaffIds.length} staff members who are on leave/business trip`);
      next(null, mission, availableStaff);
    })
    .catch(err => next(err));
  }

  const createMissionLog = (mission, eligibleStaff, next) => {
    const logData = {
      user: req.user?.id || null,
      mission: mission._id,
      message: `Gửi thông báo huy động đến ${eligibleStaff.length} cán bộ`,
      action: 3, // Action cho việc gửi thông báo huy động
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    const missionLog = new MissionLogModel(logData);
    missionLog.save()
      .then(() => {
        next(null, mission, eligibleStaff);
      })
      .catch(err => {
        // Nếu ghi log thất bại, vẫn tiếp tục vì không ảnh hưởng đến chức năng chính
        logger.logError([err], 'createMissionLog', logData);
        next(null, mission, eligibleStaff);
      });
  }


  const sendNotificationToStaff = (mission, eligibleStaff, next) => {
    // Kiểm tra xem còn cán bộ nào sau khi lọc không
    if (eligibleStaff.length === 0) {
      return next({
        code: CONSTANTS.CODE.FAIL,
        message: {
          head: 'Thông báo',
          body: type === 'free' 
            ? 'Không có cán bộ rảnh nào để thông báo huy động' 
            : 'Không có cán bộ nào đủ điều kiện để thông báo huy động'
        }
      });
    }

    // Kiểm tra xem mission có notifyPush không
    if (!mission.notifyPush) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Không tìm thấy thông báo huy động cho nhiệm vụ này'
        }
      });
    }

    // Lấy thông báo notifyPush và cập nhật users
    SavedNotificationModel.findByIdAndUpdate(
      mission.notifyPush,
      { 
        users: eligibleStaff.map(staff => staff._id),
        updatedAt: Date.now()
      },
      { new: true }
    )
      .then(savedNotification => {
        if (!savedNotification) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: {
              head: 'Thông báo',
              body: 'Không tìm thấy thông báo huy động'
            }
          });
        }

        // Gửi push notification cho từng cán bộ sử dụng thông tin từ savedNotification
        const notificationPromises = eligibleStaff.map(staff => {
          return PushNotifyManager.sendToMember(
            staff._id,
            savedNotification.title,
            savedNotification.description,
            savedNotification.data || {
              link: 'MissionPopup',
              extras: {
                _id: mission._id.toString()
              },
              linkWeb: ``
            },
            'new_push_mission',
          )
          .then((res) => {
            console.log(`Sent notification to staff ${staff.name}`, res);
          })
          .catch(error => {
            console.error(`Failed to send notification to staff ${staff._id}:`, error);
          });
        });

        Promise.allSettled(notificationPromises)
          .then(() => {
            console.log(`Sent mission mobilization notifications to ${eligibleStaff.length} ${type === 'free' ? 'available ' : ''}staff members`);
            next(null, {
              code: CONSTANTS.CODE.SUCCESS,
              message: {
                head: 'Thông báo',
                body: `Đã gửi thông báo huy động đến ${eligibleStaff.length} cán bộ thành công`
              },
              data: {
                missionId: mission._id,
                missionName: mission.name,
                notifiedStaffCount: eligibleStaff.length,
                filterType: type,
                notifiedStaff: eligibleStaff.map(staff => ({
                  id: staff._id,
                  name: staff.name,
                  units: staff.units?.map(unit => unit.name) || [],
                  positions: staff.positions?.map(pos => pos.name) || []
                }))
              }
            });
          });
      })
      .catch(err => {
        console.error('Error updating notifyPush notification:', err);
        next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: {
            head: 'Thông báo',
            body: 'Có lỗi xảy ra khi cập nhật thông báo huy động'
          }
        });
      });
  }

  async.waterfall([
    validateParams,
    checkMissionStatus,
    getUnitUser,
    findEligibleStaff,
    filterBusyStaffOnDuty,
    filterBusyStaffOnLeave,
    createMissionLog,
    sendNotificationToStaff
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body)
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    })

    res.json(data || err)
  })
}
const _ = require('lodash')
const async = require('async')
const MissionModel = require('../../../../models/mission')
const MissionLogModel = require('../../../../models/missionLog')
const UserModel = require('../../../../models/user')
const SavedNotificationModel = require('../../../../models/savedNotification')
const PushNotifyManager = require('../../../../jobs/pushNotify')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const logger = require('../../../../logger')
const MailUtil = require('../../../../util/mail')

module.exports = (req, res) => {
  const {
   _id
  } = req.body

  const validateParams = (next) => {
    if (!_id || !_id.trim()) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'ID nhiệm vụ là bắt buộc'
        }
      });
    }

    next();
  }

  const checkMissionStatus = (next) => {
    MissionModel.findById(_id.trim())
      .populate('assignInfo.unit', 'name')
      .populate('users', '_id')
      .then(mission => {
        if (!mission) {
          return next({
            code: CONSTANTS.CODE.NOT_FOUND,
            message: {
              head: 'Thông báo',
              body: 'Không tìm thấy nhiệm vụ'
            }
          });
        }

        if (mission.status !== 2) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Chỉ có thể gửi thông báo huy động khi nhiệm vụ ở trạng thái Đang huy động lực lượng'
            }
          });
        }

        next(null, mission);
      })
      .catch(err => next(err));
  }

  const findEligibleStaff = (mission, next) => {
    // Lấy danh sách các unit từ assignInfo
    const unitIds = mission.assignInfo.map(info => info.unit._id || info.unit);

    if (unitIds.length === 0) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Không có tổ nào được phân công trong nhiệm vụ này'
        }
      });
    }

    // Lấy danh sách user đã được assign (loại trừ)
    const excludedUserIds = mission.users.map(user => user._id || user);

    // Tìm tất cả cán bộ thuộc các tổ trong assignInfo, loại trừ những người đã được assign
    UserModel.find({
      units: { $in: unitIds },
      _id: { $nin: excludedUserIds },
      status: 1 // Chỉ lấy user đang hoạt động
    })
    .populate('units', 'name')
    .populate('positions', 'name')
    .select('_id name units positions')
    .lean()
    .then(eligibleStaff => {
      if (eligibleStaff.length === 0) {
        return next({
          code: CONSTANTS.CODE.NOT_FOUND,
          message: {
            head: 'Thông báo',
            body: 'Không có cán bộ nào đủ điều kiện để thông báo huy động'
          }
        });
      }

      next(null, mission, eligibleStaff);
    })
    .catch(err => next(err));
  }

  const createMissionLog = (mission, eligibleStaff, next) => {
    const logData = {
      user: req.user?.id || null,
      mission: mission._id,
      message: `Gửi thông báo huy động đến ${eligibleStaff.length} cán bộ`,
      action: 3, // Action cho việc gửi thông báo huy động
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    const missionLog = new MissionLogModel(logData);
    missionLog.save()
      .then(() => {
        next(null, mission, eligibleStaff);
      })
      .catch(err => {
        // Nếu ghi log thất bại, vẫn tiếp tục vì không ảnh hưởng đến chức năng chính
        logger.logError([err], 'createMissionLog', logData);
        next(null, mission, eligibleStaff);
      });
  }

  const sendNotificationToAssignedUsers = (mission, next) => {
    if (!mission.users || mission.users.length === 0) {
      return next(null, mission);
    }

    // Kiểm tra xem mission có notifyAssign không
    if (!mission.notifyAssign) {
      console.log('No notifyAssign found for mission, skipping notification to assigned users');
      return next(null, mission);
    }

    // Lấy thông tin chi tiết của các user đã được assign
    UserModel.find({
      _id: { $in: mission.users.map(user => user._id || user) }
    })
    .populate('units', 'name')
    .populate('positions', 'name')
    .select('_id name units positions')
    .lean()
    .then(assignedUsers => {
      if (assignedUsers.length === 0) {
        return next(null, mission);
      }

      // Lấy thông báo notifyAssign và cập nhật users
      SavedNotificationModel.findByIdAndUpdate(
        mission.notifyAssign,
        { 
          users: assignedUsers.map(user => user._id),
          updatedAt: Date.now()
        },
        { new: true }
      )
        .then(savedNotification => {
          if (!savedNotification) {
            console.log('NotifyAssign notification not found, skipping notification to assigned users');
            return next(null, mission);
          }

          // Gửi push notification cho từng user đã được assign sử dụng thông tin từ savedNotification
          const assignedNotificationPromises = assignedUsers.map(user => {
            return PushNotifyManager.sendToMember(
              user._id,
              savedNotification.title,
              savedNotification.content,
              savedNotification.data || {
                link: 'MissionPopup',
                extras: {
                  _id: mission._id.toString()
                },
                linkWeb: ``
              },
              'mission_assigned',
              'ioc'
            ).catch(error => {
              console.error(`Failed to send notification to assigned user ${user._id}:`, error);
            });
          });

          Promise.allSettled(assignedNotificationPromises)
            .then(() => {
              console.log(`Sent mission assignment notifications to ${assignedUsers.length} assigned users`);
              next(null, mission);
            });
        })
        .catch(err => {
          console.error('Error updating notifyAssign notification:', err);
          // Không dừng process nếu gửi thông báo cho assigned users thất bại
          next(null, mission);
        });
    })
    .catch(err => {
      console.error('Error finding assigned users:', err);
      next(null, mission);
    });
  }

  const sendNotificationToStaff = (mission, eligibleStaff, next) => {
    // Kiểm tra xem mission có notifyPush không
    if (!mission.notifyPush) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Không tìm thấy thông báo huy động cho nhiệm vụ này'
        }
      });
    }

    // Lấy thông báo notifyPush và cập nhật users
    SavedNotificationModel.findByIdAndUpdate(
      mission.notifyPush,
      { 
        users: eligibleStaff.map(staff => staff._id),
        updatedAt: Date.now()
      },
      { new: true }
    )
      .then(savedNotification => {
        if (!savedNotification) {
          return next({
            code: CONSTANTS.CODE.NOT_FOUND,
            message: {
              head: 'Thông báo',
              body: 'Không tìm thấy thông báo huy động'
            }
          });
        }

        // Gửi push notification cho từng cán bộ sử dụng thông tin từ savedNotification
        const notificationPromises = eligibleStaff.map(staff => {
          return PushNotifyManager.sendToMember(
            staff._id,
            savedNotification.title,
            savedNotification.content,
            savedNotification.data || {
              link: 'MissionPopup',
              extras: {
                _id: mission._id.toString()
              },
              linkWeb: ``
            },
            'mission_mobilization',
            'ioc'
          ).catch(error => {
            console.error(`Failed to send notification to staff ${staff._id}:`, error);
          });
        });

        Promise.allSettled(notificationPromises)
          .then(() => {
            console.log(`Sent mission mobilization notifications to ${eligibleStaff.length} staff members`);
            next(null, {
              code: CONSTANTS.CODE.SUCCESS,
              message: {
                head: 'Thông báo',
                body: `Đã gửi thông báo huy động đến ${eligibleStaff.length} cán bộ thành công`
              },
              data: {
                missionId: mission._id,
                missionName: mission.name,
                notifiedStaffCount: eligibleStaff.length,
                notifiedStaff: eligibleStaff.map(staff => ({
                  id: staff._id,
                  name: staff.name,
                  units: staff.units?.map(unit => unit.name) || [],
                  positions: staff.positions?.map(pos => pos.name) || []
                }))
              }
            });
          });
      })
      .catch(err => {
        console.error('Error updating notifyPush notification:', err);
        next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: {
            head: 'Thông báo',
            body: 'Có lỗi xảy ra khi cập nhật thông báo huy động'
          }
        });
      });
  }

  async.waterfall([
    validateParams,
    checkMissionStatus,
    sendNotificationToAssignedUsers,
    findEligibleStaff,
    createMissionLog,
    sendNotificationToStaff
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body)
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    })

    res.json(data || err)
  })
}
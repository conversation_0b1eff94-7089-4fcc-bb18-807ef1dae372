const _ = require('lodash')
const async = require('async')
const MissionModel = require('../../../../models/mission')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')

module.exports = (req, res) => {
  const {
   _id
  } = req.body

  const validateParams = (next) => {
    if (!_id || !_id.trim()) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'ID nhiệm vụ là bắt buộc'
        }
      });
    }

    next();
  }

  const getMission = (next) => {
    MissionModel.findById(_id.trim())
      .populate('createdBy', 'name')
      .populate('assignInfo.unit', 'name')
      .populate('assignInfo.users', 'name phones idNumber avatar')
      .then(mission => {
        if (!mission) {
          return next({
            code: CONSTANTS.CODE.NOT_FOUND,
            message: {
              head: 'Thông báo',
              body: 'Không tìm thấy nhiệm vụ'
            }
          });
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: mission
        });
      })
      .catch(err => next(err));
  }

  async.waterfall([
    validateParams,
    getMission
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body)
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    })

    res.json(data || err)
  })
}
const _ = require('lodash')
const async = require('async')
const MissionModel = require('../../../../models/mission')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')

module.exports = (req, res) => {
  const {
   _id
  } = req.body
  const { role } = req.user || 'officer';
  const userId = req.user.id;
  const fromPush = req.body.fromPush || false;
  let userInf
  const validateParams = (next) => {
    if (!_id || !_id.trim()) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'ID nhiệm vụ là bắt buộc'
        }
      });
    }

    next();
  }

  const getUserInf = (next) => {
    UserModel
      .findById(userId)
      .select('positions units')
      .populate('positions', 'isTeamLeader')
      .lean()
      .exec((err, user) => {
        if (err) {
          return next(err);
        }
        if (!user) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: NESSAGES.SYSTEM.ERROR
          });
        }
        userInf = user;
        next(null);
      })
  }

  const getMission = (next) => {
    let query = { _id: _id.trim() };
    if(!fromPush) {
      if(role === 'officer') {
        query['assignInfo.users'] = userId;
      } else if(role === 'team_leader') {
        query['$or'] = [
          {
            'assignInfo.unit': { $in: userInf.units },
            type: 'unit_assign'
          },{
            'assignInfo.users': userId,
            type: 'self_assign'
          }
        ]
      }
    }
    MissionModel.findOne(query)
      .populate('createdBy', 'name')
      .populate('assignInfo.unit', 'name')
      .populate('assignInfo.users', 'name phones idNumber avatar')
      .then(mission => {
        if (!mission) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: {
              head: 'Thông báo',
              body: 'Nhiệm vụ không còn khả dụng'
            }
          });
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: mission
        });
      })
      .catch(err => next(err));
  }

  async.waterfall([
    validateParams,
    getUserInf,
    getMission
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body)
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    })

    res.json(data || err)
  })
}
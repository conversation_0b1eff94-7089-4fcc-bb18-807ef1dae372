const _ = require('lodash')
const async = require('async')
const MissionModel = require('../../../../models/mission')
const MissionLogModel = require('../../../../models/missionLog')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const logger = require('../../../../logger')
const MailUtil = require('../../../../util/mail')

module.exports = (req, res) => {
  const { _id } = req.body; // mission ID

  async.waterfall([
    // Validate input
    (next) => {
      if (!_id) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGES.SYSTEM.WRONG_PARAMS
        });
      }
      next();
    },

    (next) => {
      MissionLogModel
        .find({ mission: _id })
        .sort({ createdAt: 1 }) 
        .populate({
          path: 'user',
          select: 'idNumber name phones units positions',
          populate: [
            { path: 'units', select: 'name' },
            { path: 'positions', select: 'name' }
          ]
        })
        .lean()
        .exec((err, logs) => {
          if (err) return next(err);
          
          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: logs
          });
        });
    }
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body)
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    })

    res.json(data || err)
  })
}
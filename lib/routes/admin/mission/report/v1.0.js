const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
const mongoose = require('mongoose');
const JobTypeModel = require('../../../../models/jobType');
const ReportModel = require('../../../../models/report');
const ToolUtil = require('../../../../util/tool');

const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

const AttendanceRecordModel = require('../../../../models/attendanceRecord');
const WorkScheduleModel = require('../../../../models/workSchedule');
const UserModel = require('../../../../models/user');
const MissionModel = require('../../../../models/mission');


const ExcelJS = require('exceljs');
const path = require('path');
const fs = require('fs');
const UPLOAD_DIR = path.join(__dirname, '../../../../../public/uploads/excel');
if (!fs.existsSync(UPLOAD_DIR)) fs.mkdirSync(UPLOAD_DIR, { recursive: true });

function validateParams({ type, endTime, unit, options }, setUnitUserIds, next) {
  if(type && !options.includes(type)) {
    return next({
      code: CONSTANTS.CODE.WRONG_PARAMS,
      message: {
        head: 'Lỗi tham số',
        body: `Tham số type không hợp lệ. Các giá trị hợp lệ là: ${options.join(', ')}`
      }
    });
  }
  if(!endTime) {
    return next({
      code: CONSTANTS.CODE.WRONG_PARAMS,
      message: {
        head: 'Lỗi tham số',
        body: 'Tham số endTime là bắt buộc'
      }
    });
  }
  if(endTime > Date.now()) {
    endTime = Date.now();
  }
  if (unit) {
    UserModel.find({ units: unit, status: 1 }, '_id').lean().exec((err, users) => {
      if (err) return next(err);
      // Trả về unit ID thay vì user IDs vì query sẽ filter theo assignInfo.unit
      setUnitUserIds([unit]);
      next();
    });
  } else {
    next();
  }
}

function calculateTimeRange(type, endTime) {
  const endDate = new Date(endTime);
  let startTime;
  switch (type) {
    case 'today':
      startTime = new Date(endDate);
      startTime.setHours(0, 0, 0, 0);
      break;
    case '3days':
      startTime = new Date(endDate);
      startTime.setDate(endDate.getDate() - 2);
      break;
    case '7days':
      startTime = new Date(endDate);
      startTime.setDate(endDate.getDate() - 6);
      break;
    case 'week':
      startTime = new Date(endDate);
      const dayOfWeek = endDate.getDay();
      const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
      startTime.setDate(endDate.getDate() - daysToMonday);
      break;
    case 'month':
      startTime = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
      break;
    case 'year':
      startTime = new Date(endDate.getFullYear(), 0, 1);
      break;
    default:
      throw {
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: `Tham số type không hợp lệ. Các giá trị hợp lệ là: today, 3days, 7days, week, month, year`
        }
      };
  }
  startTime.setHours(0, 0, 0, 0);
  return startTime;
}

function getMissions({ startTime, endTime, unitUserIds }, next) {
  const query = {
    startTime: { $gte: startTime, $lte: new Date(endTime) },
  };
  
  // Xử lý filter
  if (unitUserIds) {
    if (unitUserIds.units) {
      // Filter theo units
      query['assignInfo.unit'] = { $in: unitUserIds.units };
    }
    
    if (unitUserIds.users) {
      // Filter theo users
      query['assignInfo.users'] = { $in: unitUserIds.users };
    }
  }
  
  MissionModel.find(query)
    .populate({
      path: 'assignInfo.unit',
      select: 'name'
    })
    .populate({
      path: 'assignInfo.users',
      select: 'name idNumber'
    })
    .populate('createdBy', 'name')
    .lean()
    .exec((err, missions) => {
      if (err) return next(err);
      next(null, missions);
    });
}

function formatAndExportExcel(missions, next) {
  const workbook = new ExcelJS.Workbook();
  const sheet = workbook.addWorksheet('Mission Report');

  // Thiết lập tiêu đề
  const title = 'Báo cáo nhiệm vụ';
  sheet.addRow([title]);
  sheet.mergeCells(1, 1, 1, 8); // Merge 8 cột
  const titleRow = sheet.getRow(1);
  titleRow.height = 35;
  titleRow.getCell(1).font = { bold: true, size: 18 };
  titleRow.getCell(1).alignment = { vertical: 'middle', horizontal: 'center' };
  titleRow.getCell(1).fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'FFFFFFFF' }
  };

  // Tạo header cho bảng
  const headerRow1 = ['STT', 'Mã nhiệm vụ', 'Tên nhiệm vụ', 'Địa điểm', 'Điều động lực lượng', '', '', 'Thời gian', 'Trạng thái'];
  const headerRow2 = ['', '', '', '', 'Tổ tham gia', 'Số cán bộ điều động', 'Cán bộ đã nhận NV', '', ''];
  
  sheet.addRow(headerRow1);
  sheet.addRow(headerRow2);

  // Merge cells cho header
  sheet.mergeCells(2, 1, 3, 1); // STT
  sheet.mergeCells(2, 2, 3, 2); // Mã nhiệm vụ  
  sheet.mergeCells(2, 3, 3, 3); // Tên nhiệm vụ
  sheet.mergeCells(2, 4, 3, 4); // Địa điểm
  sheet.mergeCells(2, 5, 2, 7); // Điều động lực lượng
  sheet.mergeCells(2, 8, 3, 8); // Thời gian
  sheet.mergeCells(2, 9, 3, 9); // Trạng thái

  // Style cho header
  [2, 3].forEach(rowNum => {
    const row = sheet.getRow(rowNum);
    row.eachCell((cell, colNumber) => {
      cell.font = { bold: true };
      cell.alignment = { vertical: 'middle', horizontal: 'center' };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFD9D9D9' }
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
    });
  });

  // Thiết lập độ rộng cột
  sheet.getColumn(1).width = 6;   // STT
  sheet.getColumn(2).width = 15;  // Mã nhiệm vụ
  sheet.getColumn(3).width = 30;  // Tên nhiệm vụ
  sheet.getColumn(4).width = 25;  // Địa điểm
  sheet.getColumn(5).width = 15;  // Tổ
  sheet.getColumn(6).width = 18;  // Số cán bộ điều động
  sheet.getColumn(7).width = 18;  // Cán bộ đã nhận NV
  sheet.getColumn(8).width = 20;  // Thời gian
  sheet.getColumn(9).width = 15;  // Trạng thái

  // Thêm dữ liệu missions
  const statusMap = {
    1: { text: 'Khởi tạo', color: '0066CC' },
    2: { text: 'Đang điều động', color: 'FFB000' },
    3: { text: 'Hoàn thành', color: '00B050' },
    4: { text: 'Đã hủy', color: 'FF0000' },
    // Fallback cho các trạng thái cũ
    pending: { text: 'Khởi tạo', color: '0066CC' },
    in_progress: { text: 'Đang điều động', color: 'FFB000' },
    completed: { text: 'Hoàn thành', color: '00B050' },
    cancelled: { text: 'Đã hủy', color: 'FF0000' }
  };

  missions.forEach((mission, index) => {
    // Tính toán thông tin điều động lực lượng từ assignInfo
    const assignInfo = mission.assignInfo || [];
    let totalAssigned = 0;
    let totalAccepted = 0;
    const unitNames = [];
    
    assignInfo.forEach(info => {
      // Tính tổng số cán bộ điều động
      totalAssigned += info.numberOfUsers || 0;
      
      // Tính số cán bộ đã nhận nhiệm vụ (số users thực tế trong mảng users)
      totalAccepted += (info.users ? info.users.length : 0);
      
      // Lấy tên đơn vị
      if (info.unit && info.unit.name) {
        unitNames.push(info.unit.name);
      }
    });

    const unitNamesStr = unitNames.length > 0 ? unitNames.join(', ') : 'Chưa phân công';

    // Format thời gian
    const timeRange = mission.startTime && mission.endTime ? 
      `${new Date(mission.startTime).toLocaleString('vi-VN')} - ${new Date(mission.endTime).toLocaleString('vi-VN')}` :
      (mission.startTime ? new Date(mission.startTime).toLocaleString('vi-VN') : 'Chưa xác định');

    // Get status info
    const statusInfo = statusMap[mission.status] || { text: 'Chưa xác định', color: '000000' };

    const row = [
      index + 1,
      mission.code || `NV${String(index + 1).padStart(3, '0')}`,
      mission.title || mission.name || '',
      mission.location?.address || '',
      unitNamesStr,
      totalAssigned,
      totalAccepted,
      timeRange,
      statusInfo.text
    ];

    const addedRow = sheet.addRow(row);
    
    // Áp dụng màu cho cột trạng thái (cột 9)
    const statusCell = addedRow.getCell(9);
    statusCell.font = {
      color: { argb: statusInfo.color },
      bold: true
    };
  });

  // Tính toán thống kê trạng thái
  const statusCount = {
    1: 0, // Khởi tạo
    2: 0, // Đang điều động
    3: 0, // Hoàn thành
    4: 0  // Đã hủy
  };

  missions.forEach(mission => {
    const status = mission.status;
    if (statusCount.hasOwnProperty(status)) {
      statusCount[status]++;
    } else {
      // Xử lý các trạng thái cũ
      if (status === 'pending') statusCount[1]++;
      else if (status === 'in_progress') statusCount[2]++;
      else if (status === 'completed') statusCount[3]++;
      else if (status === 'cancelled') statusCount[4]++;
    }
  });

  // Thêm vùng thống kê ở bên phải
  const statsStartCol = 11; // Cột K (bắt đầu từ cột 11)
  const statsStartRow = 2;  // Bắt đầu từ hàng 2

  // Tiêu đề thống kê
  sheet.getCell(statsStartRow, statsStartCol).value = 'THỐNG KÊ';
  sheet.mergeCells(statsStartRow, statsStartCol, statsStartRow, statsStartCol + 1);
  const statsTitle = sheet.getCell(statsStartRow, statsStartCol);
  statsTitle.font = { bold: true, size: 14 };
  statsTitle.alignment = { vertical: 'middle', horizontal: 'center' };
  statsTitle.fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'FFE6E6FA' }
  };
  statsTitle.border = {
    top: { style: 'thin' },
    left: { style: 'thin' },
    bottom: { style: 'thin' },
    right: { style: 'thin' }
  };

  // Header cho bảng thống kê
  const statsHeaderRow = statsStartRow + 1;
  sheet.getCell(statsHeaderRow, statsStartCol).value = 'Trạng thái';
  sheet.getCell(statsHeaderRow, statsStartCol + 1).value = 'Số lượng';
  
  [statsStartCol, statsStartCol + 1].forEach(col => {
    const cell = sheet.getCell(statsHeaderRow, col);
    cell.font = { bold: true };
    cell.alignment = { vertical: 'middle', horizontal: 'center' };
    cell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFD9D9D9' }
    };
    cell.border = {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' }
    };
  });

  // Dữ liệu thống kê
  const statusStats = [
    { status: 1, text: 'Khởi tạo', color: '0066CC', count: statusCount[1] },
    { status: 2, text: 'Đang điều động', color: 'FFB000', count: statusCount[2] },
    { status: 3, text: 'Hoàn thành', color: '00B050', count: statusCount[3] },
    { status: 4, text: 'Đã hủy', color: 'FF0000', count: statusCount[4] }
  ];

  statusStats.forEach((stat, index) => {
    const row = statsHeaderRow + 1 + index;
    
    // Cột trạng thái
    const statusCell = sheet.getCell(row, statsStartCol);
    statusCell.value = stat.text;
    statusCell.font = { 
      color: { argb: stat.color },
      bold: true
    };
    statusCell.alignment = { vertical: 'middle', horizontal: 'left' };
    statusCell.border = {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' }
    };

    // Cột số lượng
    const countCell = sheet.getCell(row, statsStartCol + 1);
    countCell.value = stat.count;
    countCell.font = { bold: true };
    countCell.alignment = { vertical: 'middle', horizontal: 'center' };
    countCell.border = {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' }
    };
  });

  // Tổng cộng
  const totalRow = statsHeaderRow + 1 + statusStats.length;
  const totalCell = sheet.getCell(totalRow, statsStartCol);
  totalCell.value = 'Tổng cộng';
  totalCell.font = { bold: true };
  totalCell.alignment = { vertical: 'middle', horizontal: 'left' };
  totalCell.fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'FFFFA500' }
  };
  totalCell.border = {
    top: { style: 'thin' },
    left: { style: 'thin' },
    bottom: { style: 'thin' },
    right: { style: 'thin' }
  };

  const totalCountCell = sheet.getCell(totalRow, statsStartCol + 1);
  totalCountCell.value = missions.length;
  totalCountCell.font = { bold: true };
  totalCountCell.alignment = { vertical: 'middle', horizontal: 'center' };
  totalCountCell.fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'FFFFA500' }
  };
  totalCountCell.border = {
    top: { style: 'thin' },
    left: { style: 'thin' },
    bottom: { style: 'thin' },
    right: { style: 'thin' }
  };

  // Thiết lập độ rộng cho cột thống kê
  sheet.getColumn(statsStartCol).width = 20;     // Cột trạng thái
  sheet.getColumn(statsStartCol + 1).width = 12; // Cột số lượng

  // Thêm border cho tất cả các ô dữ liệu
  const totalRows = sheet.rowCount;
  for (let r = 4; r <= totalRows; r++) {
    const row = sheet.getRow(r);
    for (let c = 1; c <= 9; c++) {
      const cell = row.getCell(c);
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
      
      // Căn giữa cột STT
      if (c === 1) {
        cell.alignment = { vertical: 'middle', horizontal: 'center' };
      }
      
      // Căn giữa các cột số
      if (c === 6 || c === 7) {
        cell.alignment = { vertical: 'middle', horizontal: 'center' };
      }
      
      // Cột trạng thái đã được set màu trong forEach loop ở trên
    }
  }

  // Xuất file
  const fileName = `mission_report_${Date.now()}.xlsx`;
  const filePath = path.join(UPLOAD_DIR, fileName);
  
  workbook.xlsx.writeFile(filePath)
    .then(() => {
      const downloadLink = `/uploads/excel/${fileName}`;
      next(null, { code: CONSTANTS.CODE.SUCCESS, link: downloadLink });
    })
    .catch(next);
}

module.exports = (req, res) => {
  const userId = req.user.id;
  const options = [
    'today', '3days','7days','week','month','year',
  ];
  let { type = 'week', endTime, unit, startTime, users } = req.body;
  let unitUserIds = null;
  let filteredUserIds = null;

  async.waterfall([
    function stepValidateParams(next) {
      validateParams({ type, endTime, unit, options }, ids => { unitUserIds = ids; }, next);
    },
    function stepMergeUserIds(next) {
      // Tạo đối tượng filter rõ ràng
      const filter = {};
      
      // Filter theo unit
      if (unitUserIds && unitUserIds.length > 0) {
        filter.units = unitUserIds;
      }
      
      // Filter theo users (override unit filter nếu có)
      if (users && Array.isArray(users) && users.length > 0) {
        filter.users = users;
        delete filter.units; // Ưu tiên filter theo users
      }
      
      filteredUserIds = filter;
      next();
    },
    function stepCalculateTimeRange(next) {
      if (startTime) {
        // Nếu đã có startTime từ body thì dùng luôn
        next(null, new Date(startTime));
      } else {
        try {
          const calculatedStartTime = calculateTimeRange(type, endTime);
          next(null, calculatedStartTime);
        } catch (err) {
          next(err);
        }
      }
    },
    function stepGetMissions(startTime, next) {
      getMissions({ startTime, endTime, unitUserIds: filteredUserIds }, next);
    },
    formatAndExportExcel
  ], (err, data) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });
    res.json(data || err);
  });
}

const _ = require('lodash')
const async = require('async')
const MissionModel = require('../../../../models/mission')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')

module.exports = (req, res) => {
  const {
    page = 1,
    limit = 10,
    textSearch,
    status,
    unit
  } = req.body;

  const validateParams = (next) => {
    const pageNumber = parseInt(page);
    const limitNumber = parseInt(limit);

    if (pageNumber < 1) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Số trang phải lớn hơn 0'
        }
      });
    }

    if (limitNumber < 1 || limitNumber > 100) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: '<PERSON><PERSON> lượng bản ghi phải từ 1 đến 100'
        }
      });
    }

    if (status !== undefined && ![0, 1, 2, 3, 4].includes(parseInt(status))) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Trạng thái không hợp lệ'
        }
      });
    }

    next();
  }

  const buildQuery = (next) => {
    let query = {};

    // Tìm kiếm theo text trong name và code
    if (textSearch && textSearch.trim()) {
      const searchRegex = new RegExp(textSearch.trim(), 'i');
      query.$or = [
        { name: searchRegex },
        { code: searchRegex }
      ];
    }

    // Lọc theo status
    if (status !== undefined) {
      query.status = parseInt(status);
    }

    // Lọc theo unit
    if (unit && unit.trim()) {
      query['assignInfo.unit'] = unit.trim();
    }

    next(null, query);
  }

  const getMissions = (query, next) => {
    const pageNumber = parseInt(page);
    const limitNumber = parseInt(limit);
    const skip = (pageNumber - 1) * limitNumber;
    console.log(limitNumber);
    // Đếm tổng số bản ghi
    MissionModel.countDocuments(query)
      .then(total => {
        // Lấy danh sách mission
        return MissionModel.find(query)
          .populate('createdBy', 'name')
          .populate('assignInfo.unit', 'name')
          .populate('assignInfo.users', 'name phones idNumber avatar') 
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limitNumber)
          .then(missions => {
            next(null, {
              code: CONSTANTS.CODE.SUCCESS,
              data: missions,
              pagination: {
                page: pageNumber,
                limit: limitNumber,
                total,
                totalPages: Math.ceil(total / limitNumber)
              }
            });
          });
      })
      .catch(err => next(err));
  }

  async.waterfall([
    validateParams,
    buildQuery,
    getMissions
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body)
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    })

    res.json(data || err)
  })
}
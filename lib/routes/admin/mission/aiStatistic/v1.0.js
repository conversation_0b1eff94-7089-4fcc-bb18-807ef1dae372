const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const config = require('config');
const fetch = require('node-fetch');
const UnitModel = require('../../../../models/unit');
const { Configuration, OpenAIApi } = require("openai");

const configuration = new Configuration({
  apiKey: config.ai.apiKey,
  basePath: config.ai.url,
  baseOptions: {
    headers: {
      'Authorization': `Bearer ${config.ai.apiKey}`,
    },
  },
});

const client = new OpenAIApi(configuration);


module.exports = (req, res) => {
  let {
    type = 'week',
    endTime,
    unit = '',
    unitIds = [],
    data={}
  } = req.body;

  let statisticsData = {
  };
  let aiResponse = {};

  const validateParams = (next) => {
    const options = ['3days', '7days', 'week', 'month', 'year'];
    if (type && !options.includes(type)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: `Tham số type không hợp lệ. Các giá trị hợp lệ là: ${options.join(', ')}`
        }
      });
    }
    if (!endTime) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,    
        message: {
          head: 'Lỗi tham số',
          body: 'Tham số endTime là bắt buộc'
        }
      });
    }

    

    // Tính toán startTime dựa theo endTime và type
    const endDate = new Date(endTime);
    
    switch (type) {
      case '3days':
        strategyText = '3 ngày gần nhất';
        startTime = new Date(endDate);
        startTime.setDate(endDate.getDate() - 2);
        break;
      case '7days':
        strategyText = '7 ngày gần nhất';
        startTime = new Date(endDate);
        startTime.setDate(endDate.getDate() - 6);
        break;
      case 'week':
        strategyText = 'tuần hiện tại';
        startTime = new Date(endDate);
        // Tính ngày Thứ 2 của tuần (1 = Monday)
        const dayOfWeek = endDate.getDay(); // 0=Sunday, 1=Monday, ..., 6=Saturday
        const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Nếu là CN thì lùi 6 ngày, ngược lại lùi (dayOfWeek - 1) ngày
        startTime.setDate(endDate.getDate() - daysToMonday);
        break;
      case 'month':
        strategyText = 'của các tuần trong tháng hiện tại';
        startTime = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
        break;
      case 'year':
        strategyText = 'của các tháng trong năm nay';
        startTime = new Date(endDate.getFullYear(), 0, 1);
        break;
      default:
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Lỗi tham số',
            body: `Tham số type không hợp lệ`
          }
        });
    }

    // Tạo timeText từ startTime và endTime
    const formatDate = (date) => {
      const day = String(date.getDate()).padStart(2, '0');
      const month = String(date.getMonth() + 1).padStart(2, '0');
      return `${day}/${month}`;
    };
    
    timeText = `Từ ${formatDate(startTime)} đến ${formatDate(endDate)}`;
    
    next();
  };
  const getStatisticsData = (next) => {
    // Chuẩn hóa dữ liệu nhiệm vụ sang tiếng Việt
    if (data && typeof data === 'object') {
      // Tổng hợp trạng thái nhiệm vụ
      const viSummary = {
        'Tổng số nhiệm vụ': data.summary?.totalMissions || 0,
        'Khởi tạo': data.summary?.totalInitial || 0,
        'Đang điều động': data.summary?.totalInProgress || 0,
        'Hoàn thành': data.summary?.totalCompleted || 0,
        'Đã hủy': data.summary?.totalCancelled || 0
      };

      // Thống kê theo thời gian
      const viMetrics = Array.isArray(data.metrics) ? data.metrics.map(item => ({
        'Nhãn thời gian': item.timeLabel,
        'Ngày': `${item.period?.day || ''}/${item.period?.month || ''}/${item.period?.year || ''}`,
        'Tổng số nhiệm vụ': item.totalMissions || 0
      })) : [];

      // Thống kê khu vực
      const viAreaStatistics = {
        'Tổng số nhiệm vụ': data.areaStatistics?.summary?.totalMissions || 0,
        'Chi tiết theo khu vực': Array.isArray(data.areaStatistics?.areas) ? data.areaStatistics.areas.map(area => ({
          'Tên khu vực': area.areaName,
          'Số nhiệm vụ': area.totalMissions
        })) : []
      };

      // Top cán bộ thực hiện nhiều nhiệm vụ
      const viTopUsers = Array.isArray(data.userStatistics?.topUsers) ? data.userStatistics.topUsers.map(user => ({
        'Tên cán bộ': user.userName,
        'Số hiệu': user.idNumber,
        'Số nhiệm vụ thực hiện': user.totalMissions || user.missionsCount || 0
      })) : [];

      // Top cán bộ từ chối nhiệm vụ
      const viTopRejecters = Array.isArray(data.rejectStatistics?.topRejecters) ? data.rejectStatistics.topRejecters.map(user => ({
        'Tên cán bộ': user.userName,
        'Số hiệu': user.idNumber,
        'Số lần từ chối': user.totalRejects || user.rejectedMissionsCount || 0
      })) : [];

      statisticsData = {
        'Tổng hợp trạng thái': viSummary,
        'Thống kê theo ngày': viMetrics,
        'Thống kê khu vực': viAreaStatistics,
        'Top cán bộ thực hiện nhiều nhiệm vụ': viTopUsers,
        'Top cán bộ từ chối nhiệm vụ': viTopRejecters
      };
    }
    next();
  };


  const callAIStream = (next) => {
    const contextPrompt = `

Dữ liệu:
${JSON.stringify(statisticsData, null, 2)}

Nhiệm vụ của bạn: dựa trên Dữ liệu đầu vào là số liệu thống kê nhiệm vụ ${strategyText} ${timeText}, hãy phân tích và đưa ra nhận định theo các tiêu chí sau:
1. **Tổng quan xu hướng**
   - Tóm tắt số lượng nhiệm vụ/sự kiện theo thời gian (ngày, tuần, tháng).
   - Phát hiện các ngày/tuần cao điểm hoặc thấp bất thường.
   - Đưa ra dự báo xu hướng nếu có thể.

2. **Hiệu quả nhiệm vụ**
   - Thống kê theo trạng thái (hoàn tất, đang xử lý, hủy bỏ...).
   - Nhận xét về tỷ lệ % từng trạng thái.
   - Đưa ra cảnh báo nếu tỷ lệ hủy bỏ hoặc chưa hoàn tất quá cao.

3. **Phân tích khu vực**
   - So sánh các khu vực theo số lượng nhiệm vụ/huy động.
   - Xác định khu vực nào là điểm nóng, khu vực nào ít nhiệm vụ.
   - Gợi ý phân bổ nhân lực phù hợp.

4. **Phân tích cán bộ**
   - Ai thực hiện nhiều nhiệm vụ nhất, ít nhất.
   - Ai có tỷ lệ từ chối/hủy nhiệm vụ cao.
   - Nhận định về hiệu quả, khuyến nghị điều chỉnh phân công.

5. **Cảnh báo & Đề xuất**
   - Chỉ ra các rủi ro tiềm ẩn (cán bộ quá tải, khu vực quá nóng, tỷ lệ hủy cao).
   - Đề xuất hành động: tăng cường nhân lực, huấn luyện cán bộ, cải thiện quy trình điều động.

Trình bày ngắn gọn, rõ ràng, có số liệu minh chứng và gạch đầu dòng
Trả lời hoàn toan bằng tiếng Việt.`;

    const url = `${config.ai.url}/chat/completions`;
    const headers = {
      'Authorization': `Bearer ${config.ai.apiKey}`,
      'Content-Type': 'application/json'
    };
    const body = JSON.stringify({
      model: config.ai.model,
      messages: [
        { role: "system", content: "Bạn là chuyên gia phân tích kỷ luật điểm danh cho hệ thống IOC Công an." },
        { role: "user", content: contextPrompt }
      ],
      max_tokens: 60000,
      temperature: 0.2,
      stream: true
    });

    fetch(url, { method: 'POST', headers, body })
      .then(response => {
        if (!response.ok || !response.body) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: 'Không thể stream từ OpenAI'
          });
        }

        res.setHeader('Content-Type', 'text/event-stream; charset=utf-8');
        res.setHeader('Cache-Control', 'no-cache');
        res.setHeader('Connection', 'keep-alive');

        let fullContent = '';
        response.body.on('data', (chunk) => {
          const lines = chunk.toString().split('\n');
          lines.forEach(line => {
            if (line.startsWith('data:')) {
              const data = line.replace('data: ', '').trim();
              if (data === '[DONE]') {
                res.end();
              } else {
                try {
                  const json = JSON.parse(data);
                  const delta = json.choices?.[0]?.delta?.content || '';
                  console.log('Delta received:', delta);
                  if (delta) {
                    fullContent += delta;
                    res.write(delta);
                  }
                } catch (e) {}
              }
            }
          });
        });
        response.body.on('end', () => {
          console.log('Full AI Response:', fullContent);
          aiResponse = { content: fullContent };
          next();
        });
        response.body.on('error', (err) => {
          next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: 'Lỗi stream OpenAI',
            error: err
          });
        });
      })
      .catch(error => {
        next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR,
          error
        });
      });
  };

  const returnResult = (next) => {  
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        report: aiResponse.content,
        metadata: {
          type,
          unitIds,
          statisticsData,
          aiModel: aiResponse.model,
          usage: aiResponse.usage,
          generatedAt: Date.now()
        }
      }
    });
  };

  async.waterfall([
    validateParams,
    getStatisticsData,
    callAIStream,
    returnResult
  ], (err, data) => {
    if (_.isError(err)) {
      global.logger && global.logger.logError([err], req.originalUrl, req.body);
      global.MailUtil && global.MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Nếu đã trả về qua stream thì không cần res.json nữa
    if (!res.headersSent) {
      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });
      res.json(data || err);
    }
  });
};

const _ = require('lodash');
const async = require('async');
const mongoose = require('mongoose');
const MissionModel = require('../../../../models/mission');
const AreaModel = require('../../../../models/area');
const UserModel = require('../../../../models/user');

const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const ToolUtil = require('../../../../util/tool');

/**
 * API thống kê số lượng nhiệm vụ theo trạng thái
 * POST /api/v1.0/statistics/missions-summary
 *
 * Trả về thống kê số lượng nhiệm vụ theo từng trạng thái
 * Sử dụng MissionModel để đếm số lượng nhiệm vụ
 * <PERSON>ân chia theo thời gian
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const options = [
    '3days','7days','week','month','year',
  ]

  const {
    type = 'week',
    endTime
  } = req.body;

  let startTime;
  let result;

  /**
   * Validate tham số đầu vào
   */
  const validateParams = (next) => {
    
    if(type && !options.includes(type)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: `Tham số type không hợp lệ. Các giá trị hợp lệ là: ${options.join(', ')}`
        }
      });
    }
    if(!endTime) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: 'Tham số endTime là bắt buộc'
        }
      });
    }
    next();
  };

  const calculateTimeRange = (next) => {
    const endDate = new Date(endTime);
    
    switch (type) {
      case '3days':
        startTime = new Date(endDate);
        startTime.setDate(endDate.getDate() - 2);
        break;
      case '7days':
        startTime = new Date(endDate);
        startTime.setDate(endDate.getDate() - 6);
        break;
      case 'week':
        startTime = new Date(endDate);
        // Tính ngày Thứ 2 của tuần (1 = Monday)
        const dayOfWeek = endDate.getDay(); // 0=Sunday, 1=Monday, ..., 6=Saturday
        const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Nếu là CN thì lùi 6 ngày, ngược lại lùi (dayOfWeek - 1) ngày
        startTime.setDate(endDate.getDate() - daysToMonday);
        break;
      case 'month':
        startTime = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
        break;
      case 'year':
        startTime = new Date(endDate.getFullYear(), 0, 1);
        break;
      default:
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Lỗi tham số',
            body: `Tham số type không hợp lệ. Các giá trị hợp lệ là: ${options.join(', ')}`
          }
        });
    }
    startTime.setHours(0, 0, 0, 0);

    next();
  };

  const getMissionStatistics = (next) => {
    // Query MissionModel để lấy thống kê theo thời gian
    const missionQuery = {
      startTime: {
        $gte: startTime.getTime(),
        $lte: new Date(endTime).getTime()
      }
    };
    
    // Tạo pipeline aggregate dựa vào type để phân chia thời gian
    let dateGrouping;
    let sortField;
    
    switch (type) {
      case '3days':
      case '7days':
        // Chia theo từng ngày
        dateGrouping = {
          year: { $year: { $add: [{ $toDate: '$startTime' }, 7 * 60 * 60 * 1000] } },
          month: { $month: { $add: [{ $toDate: '$startTime' }, 7 * 60 * 60 * 1000] } },
          day: { $dayOfMonth: { $add: [{ $toDate: '$startTime' }, 7 * 60 * 60 * 1000] } }
        };
        sortField = { '_id.year': 1, '_id.month': 1, '_id.day': 1 };
        break;
      case 'week':
        // Chia theo từng thứ trong tuần (1=Chủ nhật, 2=Thứ 2, ...)
        dateGrouping = {
          year: { $year: { $add: [{ $toDate: '$startTime' }, 7 * 60 * 60 * 1000] } },
          week: { $week: { $add: [{ $toDate: '$startTime' }, 7 * 60 * 60 * 1000] } },
          dayOfWeek: { $dayOfWeek: { $add: [{ $toDate: '$startTime' }, 7 * 60 * 60 * 1000] } }
        };
        sortField = { '_id.year': 1, '_id.week': 1, '_id.dayOfWeek': 1 };
        break;
      case 'month':
        // Chia theo từng tuần trong tháng
        dateGrouping = {
          year: { $year: { $add: [{ $toDate: '$startTime' }, 7 * 60 * 60 * 1000] } },
          month: { $month: { $add: [{ $toDate: '$startTime' }, 7 * 60 * 60 * 1000] } },
          week: { $week: { $add: [{ $toDate: '$startTime' }, 7 * 60 * 60 * 1000] } }
        };
        sortField = { '_id.year': 1, '_id.month': 1, '_id.week': 1 };
        break;
      case 'year':
        // Chia theo từng tháng trong năm
        dateGrouping = {
          year: { $year: { $add: [{ $toDate: '$startTime' }, 7 * 60 * 60 * 1000] } },
          month: { $month: { $add: [{ $toDate: '$startTime' }, 7 * 60 * 60 * 1000] } }
        };
        sortField = { '_id.year': 1, '_id.month': 1 };
        break;
    }

    MissionModel.aggregate([
      { $match: missionQuery },
      {
        $group: {
          _id: dateGrouping,
          totalMissions: { $sum: 1 },
          initialMissions: { 
            $sum: { $cond: [{ $eq: ['$status', 1] }, 1, 0] } 
          },
          inProgressMissions: { 
            $sum: { $cond: [{ $eq: ['$status', 2] }, 1, 0] } 
          },
          completedMissions: { 
            $sum: { $cond: [{ $eq: ['$status', 3] }, 1, 0] } 
          },
          cancelledMissions: { 
            $sum: { $cond: [{ $eq: ['$status', 4] }, 1, 0] } 
          }
        }
      },
      { $sort: sortField }
    ])
      .then((missionStats) => {
        // Helper function để tạo tất cả time periods cho type
        const generateAllTimePeriods = () => {
          const periods = [];
          const start = new Date(startTime);
          const end = new Date(endTime);
          
          switch (type) {
            case '3days':
            case '7days':
              // Tạo tất cả các ngày trong khoảng thời gian
              for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
                periods.push({
                  timeLabel: `${d.getDate().toString().padStart(2, '0')}/${(d.getMonth() + 1).toString().padStart(2, '0')}`,
                  period: {
                    year: d.getFullYear(),
                    month: d.getMonth() + 1,
                    day: d.getDate()
                  }
                });
              }
              break;
            case 'week':
              // Tạo tất cả các thứ trong tuần (T2 -> CN)
              const dayNames = ['T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'CN'];
              for (let i = 0; i < 7; i++) {
                periods.push({
                  timeLabel: dayNames[i],
                  period: {
                    dayOfWeek: i === 6 ? 1 : i + 2 // T2=2, T3=3, T4=4, T5=5, T6=6, T7=7, CN=1
                  }
                });
              }
              break;
            case 'month':
              // Tạo tất cả các tuần trong tháng
              const startWeek = Math.floor((start.getTime() - new Date(start.getFullYear(), 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000)) + 1;
              const endWeek = Math.floor((end.getTime() - new Date(end.getFullYear(), 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000)) + 1;
              for (let w = startWeek; w <= endWeek; w++) {
                periods.push({
                  timeLabel: `Tuần ${w - startWeek + 1}`,
                  period: {
                    week: w
                  }
                });
              }
              break;
            case 'year':
              // Tạo tất cả các tháng trong năm
              for (let m = start.getMonth() + 1; m <= end.getMonth() + 1; m++) {
                periods.push({
                  timeLabel: `T${m}`,
                  period: {
                    month: m
                  }
                });
              }
              break;
          }
          return periods;
        };

        // Helper function để format label thời gian
        const formatTimeLabel = (timeGroup) => {
          switch (type) {
            case '3days':
            case '7days':
              return `${timeGroup.day.toString().padStart(2, '0')}/${timeGroup.month.toString().padStart(2, '0')}`;
            case 'week':
              const dayNames = ['', 'CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'];
              return dayNames[timeGroup.dayOfWeek];
            case 'month':
              return `Tuần ${timeGroup.week - Math.floor((new Date(timeGroup.year, timeGroup.month - 1, 1).getTime() - new Date(timeGroup.year, 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000)) + 1}`;
            case 'year':
              return `T${timeGroup.month}`;
            default:
              return '';
          }
        };

        // Tạo tất cả time periods
        const allTimePeriods = generateAllTimePeriods();

        // Tạo một object để lưu thống kê nhiệm vụ cho mỗi time period
        const timePeriodsData = {};
        
        // Khởi tạo tất cả time periods với giá trị 0
        allTimePeriods.forEach(period => {
          timePeriodsData[period.timeLabel] = {
            timeLabel: period.timeLabel,
            period: period.period,
            totalMissions: 0
          };
        });
        
        // Cập nhật dữ liệu thực tế từ missionStats
        missionStats.forEach(stat => {
          const timeLabel = formatTimeLabel(stat._id);
          if (timePeriodsData[timeLabel]) {
            timePeriodsData[timeLabel].totalMissions = stat.totalMissions;
          }
        });

        // Tính tổng số nhiệm vụ theo từng trạng thái
        const totalMissions = missionStats.reduce((sum, stat) => sum + stat.totalMissions, 0);
        const totalInitial = missionStats.reduce((sum, stat) => sum + stat.initialMissions, 0);
        const totalInProgress = missionStats.reduce((sum, stat) => sum + stat.inProgressMissions, 0);
        const totalCompleted = missionStats.reduce((sum, stat) => sum + stat.completedMissions, 0);
        const totalCancelled = missionStats.reduce((sum, stat) => sum + stat.cancelledMissions, 0);

        // Convert thành array
        const metrics = Object.values(timePeriodsData);

        if(type === 'month') {
          metrics.forEach(item => {
            if(item.period.week) {
                const { weekStart, weekEnd } = ToolUtil.getDateRangeOfISOWeek(item.period.week, startTime.getFullYear());
                // Đảm bảo tuần nằm trong tháng đang xét
                const month = startTime.getMonth() + 1;
                let labelStart = weekStart;
                let labelEnd = weekEnd;
                if (weekStart.getMonth() + 1 < month) labelStart = new Date(startTime.getFullYear(), month - 1, 1);
                if (weekEnd.getMonth() + 1 > month) labelEnd = new Date(startTime.getFullYear(), month, 0);
                item.timeLabel = `${labelStart.getDate().toString().padStart(2, '0')}/${month.toString().padStart(2, '0')} - ${labelEnd.getDate().toString().padStart(2, '0')}/${month.toString().padStart(2, '0')}`;
            }
          });
        }
        
        result = {
          code: CONSTANTS.CODE.SUCCESS,
          data: {
            title: `Thống kê nhiệm vụ theo trạng thái`,
            summary: {
              totalMissions: totalMissions,
              totalInitial: totalInitial,
              totalInProgress: totalInProgress,
              totalCompleted: totalCompleted,
              totalCancelled: totalCancelled
            },
            chartConfig: {
              colors: {
                totalMissions: '#007CFE',
                initialMissions: '#FFA500',
                inProgressMissions: '#FFD700', 
                completedMissions: '#32CD32',
                cancelledMissions: '#DC143C'
              },
              labels: {
                totalMissions: 'Tổng số nhiệm vụ',
                initialMissions: 'Khởi tạo',
                inProgressMissions: 'Đang điều động',
                completedMissions: 'Hoàn thành',
                cancelledMissions: 'Đã hủy'
              }
            },
            metrics,
            timeRange: {
              startTime: startTime.getTime(),
              endTime: new Date(endTime).getTime(),
              type
            }
          }
        };

        next(null, result);
      })
      .catch((err) => {
        next(err);
      });
  };

  const getMissionByArea = (previousResult, next) => {
    // Query MissionModel để lấy thống kê theo khu vực
    const missionQuery = {
      startTime: {
        $gte: startTime.getTime(),
        $lte: new Date(endTime).getTime()
      },
      area: { $exists: true, $ne: null } // Chỉ lấy những mission có khu vực
    };

    MissionModel.aggregate([
      { $match: missionQuery },
      {
        $group: {
          _id: '$area',
          totalMissions: { $sum: 1 }
        }
      },
      {
        $lookup: {
          from: 'areas',
          localField: '_id',
          foreignField: '_id',
          as: 'areaInfo'
        }
      },
      {
        $unwind: '$areaInfo'
      },
      {
        $project: {
          _id: 1,
          areaId: '$_id',
          areaName: '$areaInfo.name',
          totalMissions: 1
        }
      },
      {
        $sort: { 'totalMissions': -1, 'areaName': 1 }
      }
    ])
      .then((areaStats) => {
        // Tính tổng số nhiệm vụ cho tất cả khu vực
        const totalByArea = areaStats.reduce((sum, stat) => sum + stat.totalMissions, 0);

        // Thêm thông tin thống kê theo khu vực vào result từ function trước
        previousResult.data.areaStatistics = {
          summary: {
            totalMissions: totalByArea
          },
          areas: areaStats
        };

        next(null, previousResult);
      })
      .catch((err) => {
        next(err);
      });
  };

  const getMissionByUser = (previousResult, next) => {
    // Query MissionModel để lấy thống kê theo user
    const missionQuery = {
      startTime: {
        $gte: startTime.getTime(),
        $lte: new Date(endTime).getTime()
      },
      users: { $exists: true, $ne: [], $not: { $size: 0 } } // Chỉ lấy những mission có users
    };

    MissionModel.aggregate([
      { $match: missionQuery },
      {
        $unwind: '$users' // Tách array users thành các document riêng biệt
      },
      {
        $group: {
          _id: '$users',
          totalMissions: { $sum: 1 },
          missions: { $addToSet: '$_id' } // Lưu lại danh sách mission IDs
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'userInfo'
        }
      },
      {
        $unwind: '$userInfo'
      },
      {
        $project: {
          _id: 1,
          userId: '$_id',
          userName: '$userInfo.name',
          idNumber: '$userInfo.idNumber',
          totalMissions: 1,
          missionsCount: { $size: '$missions' }
        }
      },
      {
        $sort: { 'totalMissions': -1, 'userName': 1 }
      },
      {
        $limit: 10 // Giới hạn top 10 cán bộ có nhiều nhiệm vụ nhất
      }
    ])
      .then((userStats) => {
        // Tính tổng số nhiệm vụ cho tất cả users
        const totalByUser = userStats.reduce((sum, stat) => sum + stat.totalMissions, 0);

        // Thêm thông tin thống kê theo user vào result
        previousResult.data.userStatistics = {
          summary: {
            totalMissions: totalByUser,
            totalUsers: userStats.length
          },
          topUsers: userStats
        };

        next(null, previousResult);
      })
      .catch((err) => {
        next(err);
      });
  };

  const getMissionRejectsByUser = (previousResult, next) => {
    // Query MissionModel để lấy thống kê theo user từ chối
    const missionQuery = {
      startTime: {
        $gte: startTime.getTime(),
        $lte: new Date(endTime).getTime()
      },
      rejects: { $exists: true, $ne: [], $not: { $size: 0 } } // Chỉ lấy những mission có rejects
    };

    MissionModel.aggregate([
      { $match: missionQuery },
      {
        $unwind: '$rejects' // Tách array rejects thành các document riêng biệt
      },
      {
        $group: {
          _id: '$rejects',
          totalRejects: { $sum: 1 },
          rejectedMissions: { $addToSet: '$_id' } // Lưu lại danh sách mission IDs bị từ chối
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'userInfo'
        }
      },
      {
        $unwind: '$userInfo'
      },
      {
        $project: {
          _id: 1,
          userId: '$_id',
          userName: '$userInfo.name',
          idNumber: '$userInfo.idNumber',
          totalRejects: 1,
          rejectedMissionsCount: { $size: '$rejectedMissions' }
        }
      },
      {
        $sort: { 'totalRejects': -1, 'userName': 1 }
      },
      {
        $limit: 10 // Giới hạn top 10 cán bộ từ chối nhiều nhất
      }
    ])
      .then((rejectStats) => {
        // Tính tổng số lần từ chối cho tất cả users
        const totalRejects = rejectStats.reduce((sum, stat) => sum + stat.totalRejects, 0);

        // Thêm thông tin thống kê từ chối vào result
        previousResult.data.rejectStatistics = {
          summary: {
            totalRejects: totalRejects,
            totalUsersWithRejects: rejectStats.length
          },
          topRejecters: rejectStats
        };

        next(null, previousResult);
      })
      .catch((err) => {
        next(err);
      });
  };

  // Thực thi waterfall
  async.waterfall([
    validateParams,
    calculateTimeRange,
    getMissionStatistics,
    getMissionByArea,
    getMissionByUser,
    getMissionRejectsByUser,
  ], (err, data) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Xử lý lỗi hệ thống
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};

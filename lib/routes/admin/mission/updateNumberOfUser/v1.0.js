const _ = require('lodash')
const async = require('async')
const MissionModel = require('../../../../models/mission')
const MissionLogModel = require('../../../../models/missionLog')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')

module.exports = (req, res) => {
  const {
   _id,
   count,
   unit
  } = req.body

  const validateParams = (next) => {
    if (!_id || !_id.trim()) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'ID nhiệm vụ là bắt buộc'
        }
      });
    }

    if (count === undefined || count === null || !Number.isInteger(count) || count < 0) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: '<PERSON><PERSON> lượng cán bộ phải là số nguyên không âm'
        }
      });
    }

    if (!unit || !unit.trim()) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'ID đơn vị là bắt buộc'
        }
      });
    }

    next();
  }

  const checkMissionStatus = (next) => {
    MissionModel.findById(_id.trim())
      .then(mission => {
        if (!mission) {
          return next({
            code: CONSTANTS.CODE.NOT_FOUND,
            message: {
              head: 'Thông báo',
              body: 'Không tìm thấy nhiệm vụ'
            }
          });
        }

        if (mission.status !== 2) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Chỉ có thể cập nhật số lượng cán bộ khi nhiệm vụ ở trạng thái Đang Huy động lực lượng'
            }
          });
        }

        next(null, mission);
      })
      .catch(err => next(err));
  }

  const updateMission = (mission, next) => {
    // Tìm assignInfo cho unit tương ứng
    let unitAssignInfo = mission.assignInfo.find(info => info.unit.toString() === unit.trim());
    
    if (!unitAssignInfo) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Không tìm thấy thông tin phân công cho đơn vị này'
        }
      });
    }

    // Kiểm tra nếu count < số users hiện tại thì yêu cầu xóa cán bộ trước
    const currentUsersCount = unitAssignInfo.users ? unitAssignInfo.users.length : 0;
    if (count < currentUsersCount) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: `Vui lòng xóa cán bộ trước khi giảm số lượng. Hiện tại có ${currentUsersCount} cán bộ đang được phân công`
        }
      });
    }

    // Cập nhật numberOfUsers
    unitAssignInfo.numberOfUsers = count;
    mission.updatedAt = Date.now();

    mission.save()
      .then(() => {
        return MissionModel.findById(mission._id)
          .populate('createdBy', 'name')
          .populate('assignInfo.unit', 'name')
          .populate('assignInfo.users', 'name phones idNumber avatar')
          .populate('users', 'name phones idNumber avatar');
      })
      .then(updatedMission => {
        next(null, updatedMission);
      })
      .catch(err => next(err));
  }

  const createMissionLog = (updatedMission, next) => {
    const logData = {
      user: req.user?.id || null,
      mission: updatedMission._id,
      message: `Cập nhật số lượng cán bộ cho đơn vị (${count} cán bộ)`,
      action: 1, 
      data: {
        count,
        unit
      },
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    const missionLog = new MissionLogModel(logData);
    missionLog.save()
      .then(() => {
        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          message: {
            head: 'Thông báo',
            body: 'Cập nhật số lượng cán bộ thành công'
          },
          data: updatedMission
        });
      })
      .catch(err => {
        // Nếu ghi log thất bại, vẫn trả về thành công vì mission đã được cập nhật
        logger.logError([err], 'createMissionLog', logData);
        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          message: {
            head: 'Thông báo',
            body: 'Cập nhật số lượng cán bộ thành công'
          },
          data: updatedMission
        });
      });
  }

  async.waterfall([
    validateParams,
    checkMissionStatus,
    updateMission,
    createMissionLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body)
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    })

    res.json(data || err)
  })
}
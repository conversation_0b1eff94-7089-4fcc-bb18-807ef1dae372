const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Position = require('../../../../models/position')
const Permission = require('../../../../models/permission')
const GroupPermission = require('../../../../models/groupPermission')
const HandbookModel = require('../../../../models/handbook')


module.exports = (req, res) => {
  const {
    _id,
    image,
    title,
    idNumber,
    content,
    units,
    categories,
    file
  } = req.body;
  let updatedData = {};

  const checkParams = (next) => {
    if (!_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Thiếu ID cẩm nang nghiệp vụ!'
        }
      });
    }
    if (!title || !title.trim()) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Thiếu tiêu đề cẩm nang!'
        }
      });
    }
    if ((!content || !content.trim()) && !file) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Thiếu nội dung hoặc văn bản!'
        }
      });
    }
    next();
  };

  const checkHandbookExists = (next) => {
    HandbookModel.findById(_id).lean().exec((err, result) => {
      if (err) {
        return next(err);
      }
      if (!result) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Cẩm nang không tồn tại!'
          }
        });
      }
      next();
    });
  };

  const checkIdNumberNotExists = (next) => {
    if (idNumber && idNumber.trim()) {
      HandbookModel.findOne({ idNumber: idNumber.trim(), _id: { $ne: _id }, status: 1 }).lean().exec((err, result) => {
        if (err) {
          return next(err);
        }
        if (result) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Số hiệu văn bản đã tồn tại!'
            }
          });
        }
        next();
      });
    } else {
      next();
    }
  };

  const updateHandbook = (next) => {
    let obj = {
      updatedAt: Date.now(),
      title: title.trim(),
      content: content ? content.trim() : '',
      file: file || '',
      image: image || '',
      idNumber: idNumber || '',
      units: Array.isArray(units) ? units : [],
      categories: Array.isArray(categories) ? categories : []
    };
    HandbookModel.updateOne({ _id }, obj, (err, result) => {
      if (err) {
        return next(err);
      }
      updatedData = result;
      next(null);
    });
  };

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: {
        head: 'Thông báo',
        body: 'Cập nhật cẩm nang thành công!'
      }
    });
    // Ghi log cập nhật cẩm nang nếu cần
    // SystemLogModel.create({ ... })
  };

  async.waterfall([
    checkParams,
    checkHandbookExists,
    checkIdNumberNotExists,
    updateHandbook,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });
    res.json(data || err);
  });
};

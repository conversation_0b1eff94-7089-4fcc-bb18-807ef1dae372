const _ = require('lodash');
const async = require('async');
const ms = require('ms');
const { v4: uuidv4 } = require('uuid');
const config = require('config');
const util = require('util');
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis');
const User = require('../../../../models/user');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const HandbookModel = require('../../../../models/handbook');
const escapeStringRegexp = require('escape-string-regexp');

module.exports = (req, res) => {
  let title = req.body.title || '';
  let unit = req.body.unit || '';
  let category = req.body.category || '';
  let idNumber = req.body.idNumber || '';
  let content = req.body.content || '';
  let page = parseInt(req.body.page, 10) || 1;
  let pageSize = parseInt(req.body.limit, 10) || 20;

  const listHandbooks = (next) => {
    let objSearch = {
      status: 1,
    };
    if (title && title.trim()) {
      const $regex = escapeStringRegexp(title.trim());
      objSearch.title = {
        $regex,
        $options: 'i',
      };
    }
    if (idNumber && idNumber.trim()) {
      const $regex = escapeStringRegexp(idNumber.trim());
      objSearch.idNumber = {
        $regex,
        $options: 'i',
      };
    }
    if (content && content.trim()) {
      const $regex = escapeStringRegexp(content.trim());
      objSearch.content = {
        $regex,
        $options: 'i',
      };
    }
    if (unit && unit.trim()) {
      objSearch.units = unit;
    }
    if (category && category.trim()) {
      objSearch.categories = category;
    }
    HandbookModel.find(objSearch)
      .populate('categories', 'name icon')
      .select('-content -seen')
      .sort({ updatedAt: -1 })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }
        HandbookModel.countDocuments(objSearch, (err, total) => {
          if (err) {
            return next(err);
          }
          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: results,
            page,
            pageSize,
            total,
            totalPages: Math.ceil(total / pageSize)
          });
        });
      });
  };

  async.waterfall([listHandbooks], (err, data) => {
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR,
    });
    res.json(data || err);
  });
};

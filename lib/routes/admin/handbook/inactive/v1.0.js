const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Position = require('../../../../models/position')
const HandbookModel = require('../../../../models/handbook')


module.exports = (req, res) => {
  const idHandbook = req.body._id || '';
  let updatedData = {};

  const checkParams = (next) => {
    if(!idHandbook){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    next();
  }

  const inactiveHandbook = (next) => {
    HandbookModel.findOneAndUpdate({
      _id: idHandbook
    },{
      status: 0
    },{
      new: true
    }).lean().exec((err, result) => {
      if(err) {
        return next(err);
      }
      if(!result) {
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: 'Cẩm nang không tồn tại!'
        })
      }
      updatedData = result;
      next(null)
    })
  }

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
    });
  };

  async.waterfall([
    checkParams,
    inactiveHandbook,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
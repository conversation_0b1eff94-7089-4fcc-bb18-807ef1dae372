const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const HandbookCategoryModel = require('../../../../models/handbookCategory')
const HandbookModel = require('../../../../models/handbook')


module.exports = (req, res) => {
  let { image, title, idNumber, content, units, categories, file } = req.body;
  let updatedData = {};

  const checkParams = (next) => {
    if (!title || !title.trim()) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Thiếu tiêu đề cẩm nang!'
        }
      });
    }
    if ((!content || !content.trim()) && !file) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Thiếu nội dung hoặc văn bản!'
        }
      });
    }
    next();
  };

  const checkIdNumberNotExists = (next) => {
    if (idNumber && idNumber.trim()) {
      HandbookModel.findOne({ idNumber: idNumber.trim(), status: 1 }).lean().exec((err, result) => {
        if (err) {
          return next(err);
        }
        if (result) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Số hiệu văn bản đã tồn tại!'
            }
          });
        }
        next();
      });
    } else {
      next();
    }
  };

  const createHandbook = (next) => {
    const newHandbook = new HandbookModel({
      image: image || '',
      title: title.trim(),
      idNumber: idNumber || '',
      content: content ? content.trim() : '',
      file: file || '',
      units: Array.isArray(units) ? units : [],
      categories: Array.isArray(categories) ? categories : [],
      status: 1,
      createdAt: Date.now(),
      updatedAt: Date.now()
    });
    newHandbook.save((err, handbook) => {
      if (err) {
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR
        });
      }
      updatedData = handbook;
      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        message: {
          head: 'Thông báo',
          body: 'Tạo cẩm nang thành công!'
        },
        data: handbook
      });
    });
  };

  async.waterfall([
    checkParams,
    checkIdNumberNotExists,
    createHandbook
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });
    res.json(data || err);
  });
};
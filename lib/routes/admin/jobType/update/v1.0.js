const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const { change_alias } = require('../../../../util/tool');

const JobTypeModel = require('../../../../models/jobType');
const UnitModel = require('../../../../models/unit');

module.exports = (req, res) => {
  const userId = req.user._id;

  let updateData;
  let newUnitInfo;

  const checkParams = (next) => {
    const { jobTypeId, name = '', description = '', unitId, quickReport, detailReport } = req.body;

    // Kiểm tra ID công việc
    if (!jobTypeId || !_.isString(jobTypeId) || jobTypeId.trim() === '') {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: '<PERSON>ui lòng cung cấp ID công việc'
        }
      });
    }

    updateData = {
      jobTypeId: jobTypeId.trim()
    };

    // Kiểm tra tên nếu có cập nhật
    if (name) {
      if (!_.isString(name) || name.trim() === '') {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Tên công việc không hợp lệ'
          }
        });
      }

      const trimmedName = name.trim();
      if (trimmedName.length < 3 || trimmedName.length > 100) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Tên công việc phải từ 3-100 ký tự'
          }
        });
      }

      updateData.name = trimmedName;
    }

    // Kiểm tra mô tả nếu có cập nhật
    if (description) {
      if (!_.isString(description) || description.trim() === '') {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Mô tả công việc không hợp lệ'
          }
        });
      }

      updateData.description = description.trim();
    }

    // Kiểm tra đơn vị nếu có cập nhật
    if (unitId) {
      if (!_.isString(unitId) || unitId.trim() === '') {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'ID đơn vị không hợp lệ'
          }
        });
      }

      updateData.unitId = unitId.trim();
    }

    if (quickReport !== undefined) {
      updateData.quickReport = quickReport;
    }

    if (detailReport !== undefined) {
      updateData.detailReport = detailReport;
    }

    next();
  };

  const checkNewUnit = (next) => {
    if (!updateData.unitId) {
      return next();
    }

    UnitModel.findById(updateData.unitId, (err, unit) => {
      if (err) {
        logger.logError('Lỗi khi tìm đơn vị mới:', err);
        return next(err);
      }

      if (!unit || unit.status !== 1) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Đơn vị không tồn tại. Vui lòng kiểm tra lại.'
          }
        });
      }

      newUnitInfo = unit;
      next();
    });
  };

  const checkDuplicateName = (next) => {
    // Chỉ kiểm tra duplicate nếu có thay đổi name hoặc unit
    if (!updateData.name) {
      return next();
    }

    JobTypeModel.findOne({
      _id: { $ne: updateData.jobTypeId }, // Loại trừ chính nó
      name: updateData.name,
      status: 1
    })
    .populate('unit', 'name')
    .lean()
    .exec((err, duplicateJobType) => {
      if (err) {
        logger.logError('Lỗi khi kiểm tra tên trùng lặp:', err);
        return next(err);
      }

      if (duplicateJobType) {
        const unitName = newUnitInfo ? newUnitInfo.name : duplicateJobType.unit.name;
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: `Công việc "${updateData.name}" đã tồn tại trong đơn vị "${unitName}"`
          }
        });
      }

      next();
    });
  };

  const updateJobType = (next) => {
    const updateFields = {
      updatedAt: Date.now()
    };

    // Cập nhật name và nameAlias nếu có
    if (updateData.name) {
      updateFields.name = updateData.name;
      updateFields.nameAlias = change_alias(updateData.name);
    }

    // Cập nhật description nếu có
    if (updateData.description) {
      updateFields.description = updateData.description;
    }

    // Cập nhật unit nếu có
    if (updateData.unitId) {
      updateFields.unit = updateData.unitId;
    }

    if (updateData.quickReport !== undefined) {
      updateFields.quickReport = updateData.quickReport;
    }

    if (updateData.detailReport !== undefined) {
      updateFields.detailReport = updateData.detailReport;
    }

    JobTypeModel.findOneAndUpdate(
      { _id: updateData.jobTypeId, status: 1 },
      { $set: updateFields },
      { new: true }
    )
      .populate('unit', 'name')
      .exec((err, updatedJobType) => {
        if (err) {
          logger.logError('Lỗi khi cập nhật công việc:', err);
          return next(err);
        }

        if (!updatedJobType) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Không tìm thấy công việc. Vui lòng kiểm tra lại.'
            }
          });
        }

        logger.logInfo(`Cập nhật công việc thành công - ID: ${updateData.jobTypeId}, User: ${userId}`);

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          message: {
            head: 'Thông báo',
            body: 'Cập nhật công việc thành công'
          },
          data: updatedJobType
        });
      });
  };

  // Thực thi các bước
  async.waterfall([
    checkParams,
    checkNewUnit,
    checkDuplicateName,
    updateJobType
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR,
    });

    res.json(data || err);
  });
};

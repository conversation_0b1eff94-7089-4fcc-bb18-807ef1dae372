const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

const JobTypeModel = require('../../../../models/jobType');

module.exports = (req, res) => {
  const userId = req.user._id;
  const { jobTypeId } = req.body;

  const checkParams = (next) => {
    if (!jobTypeId || !_.isString(jobTypeId) || jobTypeId.trim() === '') {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp ID công việc'
        }
      });
    }

    next();
  };

  const inactiveJobType = (next) => {
    // Thực hiện soft delete bằng cách thêm trường deletedAt
    const updateFields = {
      updatedAt: Date.now(),
      deletedAt: Date.now(),
      status: 0 // Đ<PERSON>h dấu không hoạt động
    };

    JobTypeModel.findOneAndUpdate(
      {_id: jobTypeId.trim(), status: 1},
      { $set: updateFields },
      { new: true }
    )
      .exec((err, inactivatedJobType) => {
        if (err) {
          logger.logError('Lỗi khi vô hiệu hóa công việc:', err);
          return next(err);
        }

        if (!inactivatedJobType) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Không tìm thấy công việc. Vui lòng kiểm tra lại.'
            }
          });
        }

        logger.logInfo(`Vô hiệu hóa công việc thành công - ID: ${jobTypeId.trim()}, Name: ${inactivatedJobType.name}, User: ${userId}`);

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          message: {
            head: 'Thông báo',
            body: 'Xoá công việc thành công'
          }
        });
      });
  };

  // Thực thi các bước
  async.waterfall([
    checkParams,
    inactiveJobType
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR,
    });

    res.json(data || err);
  });
};

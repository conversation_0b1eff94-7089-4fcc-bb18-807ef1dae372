const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Jo<PERSON>);

const attendanceService = require('../../../../services/attendanceService');
const AttendanceRecord = require('../../../../models/attendanceRecord');
const WorkSchedule = require('../../../../models/workSchedule');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const DateUtils = require('../../../../utils/dateUtils');

/**
 * API lấy danh sách ca làm việc với filter và sắp xếp
 * POST /api/v1.0/attendance/statistics
 *
 * Tham số đầu vào:
 * - unitId: ID đơn vị (optional)
 * - userIds: <PERSON><PERSON><PERSON> các userId để filter (optional)
 * - shift: <PERSON><PERSON> làm việc ('morning', 'afternoon', optional)
 * - status: Trạng thái ca làm việc (optional)
 *   Các giá trị hợp lệ: 'on_time', 'late', 'absent', 'excused', 'business_trip', 'nonattendance', 'no_schedule'
 * - startDate: Ngày bắt đầu (DD-MM-YYYY, optional, mặc định là ngày hiện tại)
 * - endDate: Ngày kết thúc (DD-MM-YYYY, optional, mặc định là ngày hiện tại)
 *
 * Response:
 * - workShifts: Mảng các ca làm việc với thông tin cán bộ và ca (sắp xếp theo chữ cuối cùng trong tên)
 * - summary: Thống kê tổng quan theo ca (morning/afternoon) với số lượng từng trạng thái
 */
module.exports = (req, res) => {
  const {
    unitId,
    userIds,
    shift,
    status,
    startDate = DateUtils.getCurrentDateDDMMYYYY(),
    endDate = DateUtils.getCurrentDateDDMMYYYY()
  } = req.body;

  const validateParams = (next) => {
    const validStatuses = ['on_time', 'late', 'absent', 'excused', 'business_trip', 'nonattendance', 'scheduled', 'no_schedule'];
    const validShifts = ['morning', 'afternoon'];

    const schema = Joi.object({
      unitId: Joi.objectId().allow('').optional(),
      userIds: Joi.array().items(Joi.objectId()).optional(),
      shift: Joi.string().valid(...validShifts).allow('').optional(),
      startDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).optional(),
      endDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).optional(),
      status: Joi.alternatives().try(
        Joi.string().valid(...validStatuses),
        Joi.array().items(Joi.string().valid(...validStatuses)),
        Joi.string().custom((value, helpers) => {
          const statuses = value.split(',').map(s => s.trim());
          const invalidStatuses = statuses.filter(s => !validStatuses.includes(s));
          if (invalidStatuses.length > 0) {
            return helpers.error('any.invalid');
          }
          return statuses;
        })
      ).optional()
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    // Kiểm tra startDate <= endDate
    if (DateUtils.compareDDMMYYYY(startDate, endDate) > 0) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.ATTENDANCE.WRONG_DATE
      });
    }

    next();
  };

  const getWorkShifts = (next) => {
    getFilteredWorkShifts(unitId, userIds, shift, status, startDate, endDate)
      .then(result => {
        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: {
            workShifts: result.workShifts,
            summary: result.summary
          }
        });
      })
      .catch(error => {
        next(error);
      });
  }

  async.waterfall([
    validateParams,
    getWorkShifts
  ], (err, data) => {
    if (_.isError(err)) {
      console.error('Error in attendance statistics API:', err);
      // Sử dụng global logger nếu có
      if (global.logger && global.logger.logError) {
        global.logger.logError([err], req.originalUrl, req.body);
      }
      if (global.MailUtil && global.MailUtil.sendMail) {
        global.MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
      }
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};

/**
 * Lấy danh sách ca làm việc với filter và summary
 * @param {String} unitId - ID đơn vị
 * @param {Array} userIds - Mảng các userId để filter
 * @param {String} shift - Ca làm việc
 * @param {String|Array} status - Trạng thái filter
 * @param {String} startDate - Ngày bắt đầu
 * @param {String} endDate - Ngày kết thúc
 * @returns {Object} Object chứa workShifts và summary
 */
const getFilteredWorkShifts = async (unitId, userIds, shift, status, startDate, endDate) => {
  // Build query cho WorkSchedule
  const scheduleQuery = {
    date: { $gte: startDate, $lte: endDate },
    status: 1
  };

  // Filter theo userIds nếu có
  if (userIds && userIds.length > 0) {
    scheduleQuery.user = { $in: userIds };
  }

  // Build query cho AttendanceRecord
  const attendanceQuery = {
    date: { $gte: startDate, $lte: endDate }
  };

  // Filter theo userIds nếu có
  if (userIds && userIds.length > 0) {
    attendanceQuery.user = { $in: userIds };
  }

  // Filter theo shift nếu có
  if (shift) {
    scheduleQuery['shifts.type'] = shift;
    attendanceQuery.shift = shift;
  }

  // Lấy dữ liệu
  const [workSchedules, attendanceRecords] = await Promise.all([
    WorkSchedule.find(scheduleQuery)
      .populate({
        path: 'user',
        select: 'name idNumber units avatar',
        populate: {
          path: 'units',
          select: 'name'
        }
      })
      .lean(),
    AttendanceRecord.find(attendanceQuery)
      .populate('user', 'name idNumber')
      .lean()
  ]);

  // Tạo map để tra cứu nhanh
  const attendanceMap = new Map();
  attendanceRecords.forEach(record => {
    const key = `${record.user._id}-${record.date}-${record.shift}`;
    attendanceMap.set(key, record);
  });

  // Tạo danh sách tất cả ca làm việc (không filter status để tính summary)
  const allWorkShifts = [];
  // Tạo danh sách ca làm việc đã filter (để trả về)
  const filteredWorkShifts = [];

  for (const schedule of workSchedules) {
    const user = schedule.user;

    // Filter theo đơn vị nếu có
    if (unitId && !user.units.some(unit => unit._id.toString() === unitId)) {
      continue;
    }

    for (const shiftInfo of schedule.shifts) {
      // Filter theo shift nếu có
      if (shift && shiftInfo.type !== shift) {
        continue;
      }

      // Xác định trạng thái ca làm việc
      const attendanceKey = `${user._id}-${schedule.date}-${shiftInfo.type}`;
      const attendanceRecord = attendanceMap.get(attendanceKey);

      let shiftStatus;
      let checkinTime = null;
      let note = '';

      if (attendanceRecord) {
        // Có điểm danh
        shiftStatus = attendanceRecord.status;
        checkinTime = attendanceRecord.checkinTime;
        note = attendanceRecord.note || '';
      } else {
        if (shiftInfo.status === 'business_trip') {
          shiftStatus = 'business_trip';
        } else if (shiftInfo.status === 'excused') {
          shiftStatus = 'excused';
        } else if (attendanceService.isShiftCompleted(schedule.date, shiftInfo.type)) {
          shiftStatus = 'absent';
        } else {
          shiftStatus = 'nonattendance';
        }
      }

      const workShiftItem = {
        user,
        shift: shiftInfo.type,
        status: shiftStatus,
        checkinTime: checkinTime,
        note: note,
        date: schedule.date
      };

      // Thêm vào danh sách tất cả (để tính summary)
      allWorkShifts.push(workShiftItem);

      // Filter theo status nếu có (chỉ cho danh sách trả về)
      if (status) {
        const statusArray = normalizeStatusFilter(status);
        if (statusArray.includes(shiftStatus)) {
          filteredWorkShifts.push(workShiftItem);
        }
      } else {
        // Không có filter status thì thêm vào danh sách trả về
        filteredWorkShifts.push(workShiftItem);
      }
    }
  }

  // Sắp xếp theo chữ cuối cùng trong tên
  filteredWorkShifts.sort((a, b) => {
    const lastWordA = getLastWord(a.user.name);
    const lastWordB = getLastWord(b.user.name);
    return lastWordA.localeCompare(lastWordB, 'vi');
  });

  // Tính summary theo ca từ tất cả workShifts (không bị filter status)
  const summary = calculateSummaryByShift(allWorkShifts);

  return {
    workShifts: filteredWorkShifts,
    summary
  };
};

/**
 * Lấy chữ cuối cùng trong tên
 * @param {String} fullName - Tên đầy đủ
 * @returns {String} Chữ cuối cùng
 */
const getLastWord = (fullName) => {
  if (!fullName) return '';
  const words = fullName.trim().split(/\s+/);
  return words[words.length - 1];
};

/**
 * Chuẩn hóa status filter thành array
 * @param {String|Array} status - Status filter
 * @returns {Array} Array các status
 */
const normalizeStatusFilter = (status) => {
  if (Array.isArray(status)) {
    return status;
  }
  if (typeof status === 'string') {
    if (status.includes(',')) {
      return status.split(',').map(s => s.trim());
    }
    return [status];
  }
  return [];
};

/**
 * Tính summary theo ca
 * @param {Array} workShifts - Danh sách ca làm việc
 * @returns {Object} Summary theo ca
 */
const calculateSummaryByShift = (workShifts) => {
  const summary = {
    morning: {
      total: 0,
      on_time: 0,
      late: 0,
      absent: 0,
      excused: 0,
      business_trip: 0,
      nonattendance: 0
    },
    afternoon: {
      total: 0,
      on_time: 0,
      late: 0,
      absent: 0,
      excused: 0,
      business_trip: 0,
      nonattendance: 0
    }
  };

  workShifts.forEach(workShift => {
    const { shift, status } = workShift;

    if (summary[shift]) {
      summary[shift].total++;
      if (summary[shift][status] !== undefined) {
        summary[shift][status]++;
      }
    }
  });

  return summary;
};

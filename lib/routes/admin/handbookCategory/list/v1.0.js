const _ = require('lodash');
const async = require('async');
const ms = require('ms');
const { v4: uuidv4 } = require('uuid');
const config = require('config');
const util = require('util');
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis');
const User = require('../../../../models/user');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const HandbookCategoryModel = require('../../../../models/handbookCategory');
const escapeStringRegexp = require('escape-string-regexp');

module.exports = (req, res) => {
  let name = req.body.name || '';
  let page = parseInt(req.body.page, 10) || 1;
  let pageSize = parseInt(req.body.pageSize, 10) || 20;

  const listCategories = (next) => {
    let objSearch = {
      status: 1,
    };
    if (name && name.trim()) {
      const $regex = escapeStringRegexp(name.trim());
      objSearch.name = {
        $regex,
        $options: 'i',
      };
    }
    HandbookCategoryModel.find(objSearch)
      .sort({ updatedAt: -1 })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }
        HandbookCategoryModel.countDocuments(objSearch, (err, total) => {
          if (err) {
            return next(err);
          }
          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: results,
            page,
            pageSize,
            total,
            totalPages: Math.ceil(total / pageSize)
          });
        });
      });
  };

  async.waterfall([listCategories], (err, data) => {
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR,
    });
    res.json(data || err);
  });
};

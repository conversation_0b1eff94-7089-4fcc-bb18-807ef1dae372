const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Position = require('../../../../models/position')
const Permission = require('../../../../models/permission')
const GroupPermission = require('../../../../models/groupPermission')
const HandbookCategoryModel = require('../../../../models/handbookCategory')


module.exports = (req, res) => {
  let name = req.body.name || '';
  const idCategory = req.body._id || '';
  const icon = req.body.icon || '';
  let updatedData = {};

  const checkParams = (next) => {
    if(!idCategory){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    if(!name){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.CATEGORY.NOT_FOUND_NAME
      })
    }
    next();
  }

  const checkCategoryExists = (next) => {
    HandbookCategoryModel.findById(idCategory).lean().exec((err, result) => {
      if(err) {
        return next(err);
      }
      if(!result) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Danh mục không tồn tại!'
          }
        })
      }
      next()
    })
  }

  const checkNameNotExists = (next) => {
    HandbookCategoryModel.findOne({
      name: name.trim(),
      _id: { $ne: idCategory },
      status: 1
    }).lean().exec((err, result) => {
      if(err) {
        return next(err)
      }
      if(result) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Tên danh mục đã tồn tại!'
          }
        })
      }
      next()
    })
  }

  const updateCategory = (next) => {
    let obj = {
      updatedAt: Date.now(),
      name: name.trim()
    }
    if(icon) obj.icon = icon;
    HandbookCategoryModel.updateOne({ _id: idCategory }, obj, (err, result) => {
      if(err) {
        return next(err);
      }
      updatedData = result;
      next(null)
    })
  }

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: {
        head: 'Thông báo',
        body: 'Cập nhật danh mục thành công!'
      }
    });
    // Ghi log cập nhật danh mục nếu cần
    // SystemLogModel.create({ ... })
  };

  async.waterfall([
    checkParams,
    checkCategoryExists,
    checkNameNotExists,
    updateCategory,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}

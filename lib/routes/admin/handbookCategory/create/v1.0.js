const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const HandbookCategoryModel = require('../../../../models/handbookCategory')


module.exports = (req, res) => {

  let name = req.body.name || '';
  let icon = req.body.icon || '';
  let updatedData = {};

  const checkParams = (next) => {
    if(!name || !name.trim() ){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.CATEGORY.NOT_FOUND_NAME
      })
    }
    next();
  }

  const checkHandbookCategoryExists = (next) => {
    HandbookCategoryModel.findOne({ name: name.trim() }, (err, category) => {
      if (err) {
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR
        });
      }
      if (category) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message:{
            head: 'Thông báo',
            body: 'Danh mục đã tồn tại!'
          } 
        });
      }
      next();
    });
  }
  const createHandbookCategory = (next) => {
    const newCategory = new HandbookCategoryModel({
      name: name.trim(),
      icon: icon ? icon : undefined,
      status: 1,
      createdAt: Date.now(),
      updatedAt: Date.now()
    });
    newCategory.save((err, category) => {
      if (err) {
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR
        });
      }
      updatedData = category;
      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        message: {
          head:'Thông báo',
          body: 'Tạo danh mục thành công!'
        },
        data: category
      });
    });
  }


  async.waterfall([
    checkParams,
    checkHandbookCategoryExists,
    createHandbookCategory
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
/**
 * API lấy trạng thái Statistics Debouncer System
 * GET /api/v1.0/admin/debouncer/status
 *
 * Tr<PERSON> về thông tin chi tiết về:
 * - Trạng thái hệ thống
 * - Metrics performance
 * - Tasks đang chạy
 * - Queue sizes
 * - Health status
 */

const { getStatus, getAllTaskKeys } = require('../../../../utils/debouncer');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

module.exports = (req, res) => {
  try {
    // Get comprehensive system status
    const status = getStatus();

    // Add additional system info
    const systemInfo = {
      nodeVersion: process.version,
      platform: process.platform,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage(),
      timestamp: new Date().toISOString()
    };

    // Calculate performance metrics
    const performanceMetrics = calculatePerformanceMetrics(status.metrics);

    // Get configuration info
    const configInfo = {
      availableTaskTypes: getAllTaskKeys(),
      environment: process.env.NODE_ENV || 'development'
    };

    res.json({
      code: CONSTANTS.CODE.SUCCESS,
      message: MESSAGES.SYSTEM.SUCCESS,
      data: {
        // Core debouncer status
        debouncer: {
          ...status,
          performance: performanceMetrics
        },

        // System information
        system: systemInfo,

        // Configuration
        config: configInfo,

        // Health summary
        healthSummary: {
          overall: status.health.isHealthy ? 'healthy' : 'unhealthy',
          issues: getHealthIssues(status),
          recommendations: getRecommendations(status)
        }
      }
    });

  } catch (error) {
    console.error('Error getting debouncer status:', error);

    res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: 'Lỗi khi lấy trạng thái debouncer',
      error: error.message
    });
  }
};

/**
 * Calculate performance metrics từ raw metrics
 */
function calculatePerformanceMetrics(metrics) {
  const totalTriggers = metrics.batchTriggers + metrics.delayTriggers + metrics.debounceTriggers;

  return {
    // Efficiency metrics
    eventReductionRatio: metrics.totalEvents > 0
      ? ((metrics.totalEvents - totalTriggers) / metrics.totalEvents * 100).toFixed(1) + '%'
      : '0%',

    eventsPerExecution: totalTriggers > 0
      ? (metrics.totalEvents / totalTriggers).toFixed(1)
      : '0',

    // Trigger distribution
    triggerDistribution: {
      batch: totalTriggers > 0 ? (metrics.batchTriggers / totalTriggers * 100).toFixed(1) + '%' : '0%',
      maxDelay: totalTriggers > 0 ? (metrics.delayTriggers / totalTriggers * 100).toFixed(1) + '%' : '0%',
      debounce: totalTriggers > 0 ? (metrics.debounceTriggers / totalTriggers * 100).toFixed(1) + '%' : '0%'
    },

    // Error rate
    errorRate: metrics.totalEvents > 0
      ? (metrics.executionErrors / metrics.totalEvents * 100).toFixed(2) + '%'
      : '0%',

    // Queue efficiency
    queueUtilization: metrics.totalEvents > 0
      ? (metrics.queuedEvents / metrics.totalEvents * 100).toFixed(1) + '%'
      : '0%'
  };
}

/**
 * Identify health issues
 */
function getHealthIssues(status) {
  const issues = [];

  // Check for stuck tasks
  if (status.executingTasks.length > 0) {
    issues.push({
      type: 'stuck_tasks',
      severity: 'warning',
      message: `${status.executingTasks.length} task(s) đang execute`,
      tasks: status.executingTasks
    });
  }

  // Check error rate
  const errorRate = status.metrics.totalEvents > 0
    ? (status.metrics.executionErrors / status.metrics.totalEvents)
    : 0;

  if (errorRate > 0.1) { // > 10% error rate
    issues.push({
      type: 'high_error_rate',
      severity: 'critical',
      message: `Tỷ lệ lỗi cao: ${(errorRate * 100).toFixed(1)}%`,
      errorCount: status.metrics.executionErrors
    });
  }

  // Check queue sizes
  const totalQueueSize = Object.values(status.queueSizes).reduce((sum, size) => sum + size, 0);
  if (totalQueueSize > 50) {
    issues.push({
      type: 'large_queues',
      severity: 'warning',
      message: `Queue size lớn: ${totalQueueSize} events`,
      queues: status.queueSizes
    });
  }

  // Check memory usage
  const memoryUsage = process.memoryUsage();
  const memoryMB = memoryUsage.rss / 1024 / 1024;
  if (memoryMB > 500) { // > 500MB
    issues.push({
      type: 'high_memory_usage',
      severity: 'warning',
      message: `Memory usage cao: ${memoryMB.toFixed(1)}MB`,
      memoryUsage: memoryUsage
    });
  }

  return issues;
}

/**
 * Generate recommendations based on status
 */
function getRecommendations(status) {
  const recommendations = [];

  // Performance recommendations
  const totalTriggers = status.metrics.batchTriggers + status.metrics.delayTriggers + status.metrics.debounceTriggers;

  if (status.metrics.delayTriggers > totalTriggers * 0.5) {
    recommendations.push({
      type: 'config_optimization',
      priority: 'medium',
      message: 'Nhiều max delay triggers - consider giảm maxDelay hoặc tăng maxBatchSize',
      suggestion: 'Giảm maxDelay từ 15s xuống 10s hoặc tăng maxBatchSize'
    });
  }

  if (status.metrics.batchTriggers < totalTriggers * 0.2) {
    recommendations.push({
      type: 'batch_optimization',
      priority: 'low',
      message: 'Ít batch triggers - có thể giảm maxBatchSize để responsive hơn',
      suggestion: 'Giảm maxBatchSize để trigger sớm hơn'
    });
  }

  // Queue recommendations
  const avgQueueSize = Object.values(status.queueSizes).reduce((sum, size) => sum + size, 0) / Math.max(Object.keys(status.queueSizes).length, 1);
  if (avgQueueSize > 10) {
    recommendations.push({
      type: 'performance_tuning',
      priority: 'high',
      message: 'Queue size lớn - cần tối ưu performance callback',
      suggestion: 'Optimize statistics calculation hoặc tăng server resources'
    });
  }

  return recommendations;
}
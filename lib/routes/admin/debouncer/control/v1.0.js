/**
 * API điều k<PERSON>ển Statistics Debouncer System
 * POST /api/v1.0/admin/debouncer/control
 *
 * Các actions hỗ trợ:
 * - force_cleanup: Cleanup tất cả tasks
 * - force_execute: Force execute một task cụ thể
 * - pause_task: <PERSON><PERSON><PERSON> dừng một task type
 * - resume_task: <PERSON><PERSON><PERSON><PERSON> tục một task type
 * - get_metrics: Lấy metrics chi tiết
 */

const Joi = require('joi');
const { forceCleanup, debounceStatisticsUpdate, getStatus } = require('../../../../utils/debouncer');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

module.exports = (req, res) => {
  const { action, taskKey, options = {} } = req.body;
  const userId = req.user.id;

  // Validation
  const schema = Joi.object({
    action: Joi.string().valid(
      'force_cleanup',
      'force_execute',
      'pause_task',
      'resume_task',
      'get_metrics',
      'simulate_load'
    ).required(),

    taskKey: Joi.string().when('action', {
      is: Joi.string().valid('force_execute', 'pause_task', 'resume_task'),
      then: Joi.required(),
      otherwise: Joi.optional()
    }),

    options: Joi.object().optional()
  });

  const { error } = schema.validate(req.body);
  if (error) {
    return res.status(400).json({
      code: CONSTANTS.CODE.FAIL,
      message: error.details[0].message
    });
  }

  try {
    let result;

    switch (action) {
      case 'force_cleanup':
        result = handleForceCleanup(taskKey, userId);
        break;

      case 'force_execute':
        result = handleForceExecute(taskKey, options, userId);
        break;

      case 'pause_task':
        result = handlePauseTask(taskKey, userId);
        break;

      case 'resume_task':
        result = handleResumeTask(taskKey, userId);
        break;

      case 'get_metrics':
        result = handleGetMetrics(options);
        break;

      case 'simulate_load':
        result = handleSimulateLoad(options, userId);
        break;

      default:
        throw new Error(`Unknown action: ${action}`);
    }

    // Log admin action
    if (global.SystemLogModel) {
      SystemLogModel.create({
        user: userId,
        action: `debouncer_${action}`,
        description: `Debouncer control: ${action}`,
        data: req.body,
        updatedData: result
      }, () => {});
    }

    res.json({
      code: CONSTANTS.CODE.SUCCESS,
      message: MESSAGES.SYSTEM.SUCCESS,
      data: result
    });

  } catch (error) {
    console.error(`Error executing debouncer action ${action}:`, error);

    res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: `Lỗi khi thực hiện action: ${action}`,
      error: error.message
    });
  }
};

/**
 * Force cleanup tasks
 */
function handleForceCleanup(taskKey, userId) {
  console.log(`🧹 Admin ${userId} force cleanup${taskKey ? ` for ${taskKey}` : ' all tasks'}`);

  const statusBefore = getStatus();

  forceCleanup(taskKey);

  const statusAfter = getStatus();

  return {
    action: 'force_cleanup',
    taskKey: taskKey || 'all',
    before: {
      executingTasks: statusBefore.executingTasks.length,
      activeTimers: statusBefore.activeTimers,
      queueSizes: statusBefore.queueSizes
    },
    after: {
      executingTasks: statusAfter.executingTasks.length,
      activeTimers: statusAfter.activeTimers,
      queueSizes: statusAfter.queueSizes
    },
    timestamp: new Date().toISOString()
  };
}

/**
 * Force execute a specific task
 */
function handleForceExecute(taskKey, options, userId) {
  console.log(`🚀 Admin ${userId} force execute ${taskKey}`);

  // Create a synthetic event to trigger execution
  const syntheticEvent = {
    type: 'admin_force_execute',
    triggeredBy: userId,
    timestamp: Date.now(),
    ...options
  };

  // Trigger immediate execution by sending enough events to hit batch size
  const batchSize = 10; // Default batch size
  for (let i = 0; i < batchSize; i++) {
    debounceStatisticsUpdate(taskKey, {
      ...syntheticEvent,
      batchIndex: i
    });
  }

  return {
    action: 'force_execute',
    taskKey,
    eventsSent: batchSize,
    syntheticEvent,
    timestamp: new Date().toISOString()
  };
}

/**
 * Pause a task type (mock implementation)
 */
function handlePauseTask(taskKey, userId) {
  console.log(`⏸️ Admin ${userId} pause task ${taskKey}`);

  // Note: This is a mock implementation
  // In a real system, you'd need to modify the debouncer to support pausing

  return {
    action: 'pause_task',
    taskKey,
    status: 'paused',
    note: 'Mock implementation - task pausing not fully implemented',
    timestamp: new Date().toISOString()
  };
}

/**
 * Resume a task type (mock implementation)
 */
function handleResumeTask(taskKey, userId) {
  console.log(`▶️ Admin ${userId} resume task ${taskKey}`);

  return {
    action: 'resume_task',
    taskKey,
    status: 'resumed',
    note: 'Mock implementation - task resuming not fully implemented',
    timestamp: new Date().toISOString()
  };
}

/**
 * Get detailed metrics
 */
function handleGetMetrics(options) {
  const status = getStatus();
  const { includeHistory = false, includeSystemInfo = true } = options;

  const metrics = {
    current: status.metrics,
    performance: calculateDetailedPerformance(status.metrics),
    timestamp: new Date().toISOString()
  };

  if (includeSystemInfo) {
    metrics.system = {
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage(),
      uptime: process.uptime(),
      nodeVersion: process.version
    };
  }

  if (includeHistory) {
    // Mock historical data - in real implementation, you'd store this
    metrics.history = {
      last24Hours: generateMockHistoricalData(24),
      note: 'Mock historical data - implement proper storage for production'
    };
  }

  return metrics;
}

/**
 * Simulate load for testing
 */
function handleSimulateLoad(options, userId) {
  const {
    taskType = 'reports_summary',
    eventCount = 100,
    duration = 10000,
    eventType = 'load_test'
  } = options;

  console.log(`🧪 Admin ${userId} simulate load: ${eventCount} events over ${duration}ms`);

  const interval = duration / eventCount;
  let sentEvents = 0;

  const loadInterval = setInterval(() => {
    if (sentEvents >= eventCount) {
      clearInterval(loadInterval);
      return;
    }

    debounceStatisticsUpdate(taskType, {
      type: eventType,
      loadTestId: `${userId}_${Date.now()}`,
      eventIndex: sentEvents,
      timestamp: Date.now()
    });

    sentEvents++;
  }, interval);

  return {
    action: 'simulate_load',
    taskType,
    eventCount,
    duration,
    interval,
    estimatedCompletion: new Date(Date.now() + duration).toISOString(),
    timestamp: new Date().toISOString()
  };
}

/**
 * Calculate detailed performance metrics
 */
function calculateDetailedPerformance(metrics) {
  const totalTriggers = metrics.batchTriggers + metrics.delayTriggers + metrics.debounceTriggers;

  return {
    efficiency: {
      eventReduction: metrics.totalEvents > 0
        ? ((metrics.totalEvents - totalTriggers) / metrics.totalEvents)
        : 0,
      eventsPerExecution: totalTriggers > 0 ? (metrics.totalEvents / totalTriggers) : 0,
      queueUtilization: metrics.totalEvents > 0 ? (metrics.queuedEvents / metrics.totalEvents) : 0
    },

    reliability: {
      errorRate: metrics.totalEvents > 0 ? (metrics.executionErrors / metrics.totalEvents) : 0,
      successRate: metrics.totalEvents > 0 ? (1 - (metrics.executionErrors / metrics.totalEvents)) : 1
    },

    distribution: {
      batchTriggerRatio: totalTriggers > 0 ? (metrics.batchTriggers / totalTriggers) : 0,
      delayTriggerRatio: totalTriggers > 0 ? (metrics.delayTriggers / totalTriggers) : 0,
      debounceTriggerRatio: totalTriggers > 0 ? (metrics.debounceTriggers / totalTriggers) : 0
    }
  };
}

/**
 * Generate mock historical data
 */
function generateMockHistoricalData(hours) {
  const data = [];
  const now = Date.now();

  for (let i = hours; i >= 0; i--) {
    const timestamp = now - (i * 60 * 60 * 1000);
    data.push({
      timestamp: new Date(timestamp).toISOString(),
      totalEvents: Math.floor(Math.random() * 1000) + 100,
      executions: Math.floor(Math.random() * 100) + 10,
      errors: Math.floor(Math.random() * 5)
    });
  }

  return data;
}
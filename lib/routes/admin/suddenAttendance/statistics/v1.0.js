const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Jo<PERSON>);

const SuddenAttendanceSession = require('../../../../models/suddenAttendanceSession');
const SuddenAttendanceRecord = require('../../../../models/suddenAttendanceRecord');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

/**
 * API lấy danh sách bản ghi chấm công đột xuất với filter và sắp xếp
 * POST /api/v1.0/admin/sudden-attendance/statistics
 *
 * Tham số đầu vào:
 * - sessionId: ID phiên chấm công (optional)
 * - status: Trạng thái chấm công (optional)
 *   <PERSON><PERSON><PERSON> giá trị hợp lệ: 'on_time', 'late', 'absent', 'pending', 'exempted'
 *
 * Response:
 * - <PERSON><PERSON><PERSON> các bản ghi chấm công đột xuất với thông tin cán bộ
 * - Sắp xếp theo chữ cuối cùng trong tên cán bộ
 */
module.exports = (req, res) => {
  const {
    userId,
    sessionId,
    status,
  } = req.body;

  const validateParams = (next) => {
    const validStatuses = ['on_time', 'late', 'absent', 'pending', 'exempted'];

    const schema = Joi.object({
      userId: Joi.objectId().optional(),
      sessionId: Joi.objectId().optional(),
      status: Joi.alternatives().try(
        Joi.string().valid(...validStatuses),
        Joi.array().items(Joi.string().valid(...validStatuses)),
        Joi.string().custom((value, helpers) => {
          const statuses = value.split(',').map(s => s.trim());
          const invalidStatuses = statuses.filter(s => !validStatuses.includes(s));
          if (invalidStatuses.length > 0) {
            return helpers.error('any.invalid');
          }
          return statuses;
        })
      ).optional()
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const getSuddenAttendanceRecords = (next) => {
    getFilteredSuddenAttendanceRecords(userId, sessionId, status)
      .then(records => {
        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: records
        });
      })
      .catch(error => {
        next(error);
      });
  };

  /**
   * Lấy danh sách bản ghi chấm công đột xuất với filter
   * @param {String} userId - ID người dung
   * @param {String} sessionId - ID phiên chấm công
   * @param {String|Array} status - Trạng thái filter
   * @returns {Array} Danh sách bản ghi chấm công đột xuất
   */
  const getFilteredSuddenAttendanceRecords = async (userId, sessionId, status) => {
    // Build query cho SuddenAttendanceSession
    const sessionQuery = {
      _id: sessionId
    };

    // Lấy các phiên chấm công trong khoảng thời gian
    const sessions = await SuddenAttendanceSession.find(sessionQuery)
      .populate({
        path: 'targetUsers',
        select: 'name idNumber units avatar',
        populate: {
          path: 'units',
          select: 'name'
        }
      })
      .populate('exemptedUsers.user', 'name idNumber')
      .lean();

    if (!sessions.length) {
      return [];
    }

    // Lấy tất cả bản ghi chấm công của các phiên này
    const sessionIds = sessions.map(s => s._id);
    const attendanceRecords = await SuddenAttendanceRecord.find({
      session: { $in: sessionIds }
    })
      .populate({
        path: 'user',
        select: 'name idNumber units avatar',
        populate: {
          path: 'units',
          select: 'name'
        }
      })
      .populate('session', 'title startTime validDurationMinutes')
      .lean();

    // Tạo map để tra cứu nhanh
    const attendanceMap = new Map();
    attendanceRecords.forEach(record => {
      console.log('record', record);
      const key = `${record.session._id}-${record.user._id}`;
      attendanceMap.set(key, record);
    });

    // Tạo danh sách bản ghi chấm công đột xuất
    const suddenAttendanceRecords = [];

    for (const session of sessions) {
      let targetUsers = session.targetUsers;

      // Tạo map exempted users
      const exemptedMap = new Map();
      if (session.exemptedUsers) {
        session.exemptedUsers.forEach(exempt => {
          exemptedMap.set(exempt.user._id.toString(), exempt);
        });
      }

      // Duyệt qua từng user trong phiên
      for (const user of targetUsers) {
        const attendanceKey = `${session._id}-${user._id}`;
        const attendanceRecord = attendanceMap.get(attendanceKey);
        const exemptedInfo = exemptedMap.get(user._id.toString());

        let recordStatus;
        let checkinTime = null;
        let note = '';

        if (attendanceRecord) {
          // Có bản ghi chấm công
          recordStatus = attendanceRecord.status;
          checkinTime = attendanceRecord.checkinTime;
          note = attendanceRecord.note || '';
        } else if (exemptedInfo) {
          // Được miễn chấm công
          recordStatus = 'exempted';
          note = exemptedInfo.note || `Miễn chấm công: ${exemptedInfo.reason}`;
        } else {
          // Không có bản ghi chấm công - xác định trạng thái
          const now = Date.now();
          const sessionEndTime = session.startTime + (session.validDurationMinutes * 60 * 1000);

          if (now <= sessionEndTime) {
            recordStatus = 'pending'; // Phiên còn active
          } else {
            recordStatus = 'absent'; // Phiên đã kết thúc mà không chấm công
          }
        }

        // Filter theo status nếu có
        if (status) {
          const statusArray = normalizeStatusFilter(status);
          if (!statusArray.includes(recordStatus)) {
            continue;
          }
        }

        // Thêm vào danh sách
        suddenAttendanceRecords.push({
          user,
          session: {
            id: session._id,
            title: session.title,
            startTime: session.startTime
          },
          status: recordStatus,
          checkinTime: checkinTime,
          note: note,
          date: convertTimestampToDDMMYYYY(session.startTime)
        });
      }
    }

    // Sắp xếp theo chữ cuối cùng trong tên
    suddenAttendanceRecords.sort((a, b) => {
      const lastWordA = getLastWord(a.user.name);
      const lastWordB = getLastWord(b.user.name);
      return lastWordA.localeCompare(lastWordB, 'vi');
    });

    return suddenAttendanceRecords;
  };

  async.waterfall([
    validateParams,
    getSuddenAttendanceRecords
  ], (err, data) => {
    if (_.isError(err)) {
      console.error('Error in sudden attendance statistics API:', err);
      // Sử dụng global logger nếu có
      if (global.logger && global.logger.logError) {
        global.logger.logError([err], req.originalUrl, req.body);
      }
      if (global.MailUtil && global.MailUtil.sendMail) {
        global.MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
      }
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};
/**
 * Lấy chữ cuối cùng trong tên
 * @param {String} fullName - Tên đầy đủ
 * @returns {String} Chữ cuối cùng
 */
const getLastWord = (fullName) => {
  if (!fullName) return '';
  const words = fullName.trim().split(/\s+/);
  return words[words.length - 1];
};

/**
 * Chuẩn hóa status filter thành array
 * @param {String|Array} status - Status filter
 * @returns {Array} Array các status
 */
const normalizeStatusFilter = (status) => {
  if (Array.isArray(status)) {
    return status;
  }
  if (typeof status === 'string') {
    if (status.includes(',')) {
      return status.split(',').map(s => s.trim());
    }
    return [status];
  }
  return [];
};/**

 * Parse ngày từ format DD-MM-YYYY thành timestamp
 * @param {String} dateStr - Ngày dạng DD-MM-YYYY
 * @param {String} timeStr - Thời gian dạng HH:MM:SS
 * @returns {Number} Timestamp
 */
const parseDDMMYYYYToTimestamp = (dateStr, timeStr = '00:00:00') => {
  const [day, month, year] = dateStr.split('-');
  const [hour, minute, second] = timeStr.split(':');
  return new Date(parseInt(year), parseInt(month) - 1, parseInt(day),
    parseInt(hour), parseInt(minute), parseInt(second)).getTime();
};

/**
 * Convert timestamp thành format DD-MM-YYYY
 * @param {Number} timestamp - Timestamp
 * @returns {String} Ngày dạng DD-MM-YYYY
 */
const convertTimestampToDDMMYYYY = (timestamp) => {
  const date = new Date(timestamp);
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();
  return `${day}-${month}-${year}`;
};
const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Jo<PERSON>);

const suddenAttendanceService = require('../../../../services/suddenAttendanceService');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

/**
 * API xóa phiên chấm công đột xuất
 * POST /api/v1.0/admin/sudden-attendance/delete
 */
module.exports = (req, res) => {
  const deleterId = req.user.id;
  const { sessionId } = req.body;

  let result;

  const validateParams = (next) => {
    const schema = Joi.object({
      sessionId: Joi.objectId().required()
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const deleteSession = (next) => {
    try {
      suddenAttendanceService.deleteSession(sessionId, deleterId)
        .then((res) => {
          if (!res || !res.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: res.message || MESSAGES.SYSTEM.ERROR
            });
          }

          result = res;
          next();
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: result.message,
      data: result.data
    });

    // Log hoạt động
    if (SystemLogModel) {
      SystemLogModel.create({
        user: deleterId,
        action: 'delete_sudden_attendance_session',
        description: 'Xóa phiên chấm công đột xuất',
        data: req.body,
        updatedData: result.data
      }, () => {});
    }
  };

  async.waterfall([
    validateParams,
    deleteSession,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};

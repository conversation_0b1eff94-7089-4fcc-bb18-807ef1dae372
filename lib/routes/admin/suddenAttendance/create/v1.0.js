const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Jo<PERSON>);

const suddenAttendanceService = require('../../../../services/suddenAttendanceService');
const PushNotifyManager = require('../../../../jobs/pushNotify');
const StatisticsTrigger = require('../../../../utils/statisticsTrigger');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

/**
 * API tạo phiên chấm công đột xuất
 * POST /api/v1.0/admin/sudden-attendance/create
 */
module.exports = (req, res) => {
  const creatorId = req.user.id;
  const {
    title,
    description,
    startTime,
    validDurationMinutes = 10,
    targetUnits,
    sendNotification = true,
    headquarters = 'main'
  } = req.body;

  let result;

  const startTimeText = new Date(startTime).toLocaleTimeString('vi-VN');
  const titleNotification = 'Thông báo chấm công đột xuất';
  const messageNotification = `Phiên chấm công đột xuất "${title}" sẽ bắt đầu lúc ${startTimeText} tại ${headquarters === 'main' ? 'trụ sở chính' : 'trụ sở phụ'} . Thời gian chấm công hợp lệ: ${validDurationMinutes} phút.`;
  const notificationData = {
    link: 'MainContainer',
    extras: {
      tabIndex: 2
    },
    linkWeb: `/attendance`
  };

  const validateParams = (next) => {
    const schema = Joi.object({
      title: Joi.string().required().trim().max(200),
      description: Joi.string().optional().trim().max(500),
      startTime: Joi.number().required().min(Date.now()),
      validDurationMinutes: Joi.number().optional().min(1).max(60).default(10),
      targetUnits: Joi.array().items(Joi.objectId()).optional(),
      sendNotification: Joi.boolean().optional().default(true),
      headquarters: Joi.string().valid('main', 'sub').optional().default('main')
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const createSession = (next) => {
    try {
      const sessionData = {
        title,
        description,
        startTime,
        validDurationMinutes: validDurationMinutes,
        targetUnits: targetUnits || [],
        headquarters
      };

      const notification = {
        title: titleNotification,
        message: messageNotification,
        notificationData
      };

      suddenAttendanceService.createSession(sessionData, creatorId, notification)
        .then((res) => {
          if (!res || !res.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: res.message || MESSAGES.SYSTEM.ERROR
            });
          }

          result = res;
          next();
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  const sendNotifications = (next) => {
    if (!sendNotification || !result.success || !result.data) {
      return next();
    }

    try {
      // Gửi thông báo cho các cán bộ được áp dụng
      result.data.targetUsers.forEach((user) => {
        PushNotifyManager.sendToMember(
          user,
          titleNotification,
          messageNotification,
          notificationData,
          '', // sudden_attendance_update
          'ioc'
        )
          .then(() => {
            console.log(`Sent notification to user ${user}`);
          })
          .catch(() => {
            console.log(`Failed to send notification to user ${user}`);
          });
      });

      next();
    } catch (error) {
      console.error('Error sending notifications:', error);
      // Không fail request nếu gửi thông báo lỗi
      next();
    }
  };

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: result.message,
      data: result.data
    });

    // 🚀 NEW: Trigger statistics update
    if (result.success && result.data) {
      try {
        StatisticsTrigger.triggerAttendanceUpdate('create', {
          type: 'sudden_attendance_session',
          sessionId: result.data._id,
          createdBy: creatorId,
          startTime: startTime,
          validDurationMinutes: validDurationMinutes,
          targetUnits: targetUnits,
          timestamp: Date.now()
        });
      } catch (error) {
        console.error('Error triggering sudden attendance update:', error);
      }
    }

    // Log hoạt động
    if (SystemLogModel) {
      SystemLogModel.create({
        user: creatorId,
        action: 'create_sudden_attendance_session',
        description: 'Tạo phiên chấm công đột xuất',
        data: req.body,
        updatedData: result.data
      }, () => { });
    }
  };

  async.waterfall([
    validateParams,
    createSession,
    sendNotifications,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};

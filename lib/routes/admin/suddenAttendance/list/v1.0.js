const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);

const suddenAttendanceService = require('../../../../services/suddenAttendanceService');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

/**
 * API lấy danh sách phiên chấm công đột xuất
 * POST /api/v1.0/admin/sudden-attendance/list
 */
module.exports = (req, res) => {
  const {
    status,
    createdBy,
    startDate,
    endDate,
    search,
    page = 1,
    limit = 20,
    sortBy = 'createdAt',
    sortOrder = 'desc'
  } = req.body;

  let result;

  const validateParams = (next) => {
    const schema = Joi.object({
      status: Joi.string().valid('scheduled', 'active', 'completed', 'cancelled').optional(),
      createdBy: Joi.objectId().optional(),
      startDate: Joi.string().optional(),
      endDate: Joi.string().optional(),
      search: Joi.string().optional().trim(),
      page: Joi.number().min(1).optional().default(1),
      limit: Joi.number().min(1).max(100).optional().default(20),
      sortBy: Joi.string().valid('createdAt', 'startTime', 'title').optional().default('createdAt'),
      sortOrder: Joi.string().valid('asc', 'desc').optional().default('desc')
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const getSessionsList = (next) => {
    try {
      const filters = {
        status,
        createdBy,
        startDate,
        endDate,
        search
      };

      const pagination = {
        page,
        limit,
        sortBy,
        sortOrder
      };

      suddenAttendanceService.getSessionsList(filters, pagination)
        .then((res) => {
          if (!res || !res.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: res.message || MESSAGES.SYSTEM.ERROR
            });
          }

          result = res;
          next();
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: result.data
    });
  };

  async.waterfall([
    validateParams,
    getSessionsList,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};

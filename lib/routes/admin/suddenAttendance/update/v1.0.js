const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);

const suddenAttendanceService = require('../../../../services/suddenAttendanceService');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

/**
 * API cập nhật phiên chấm công đột xuất
 * POST /api/v1.0/admin/sudden-attendance/update
 */
module.exports = (req, res) => {
  const updaterId = req.user.id;
  const {
    sessionId,
    title,
    description,
    startTime,
    validDurationMinutes,
    targetUnits,
    headquarters
  } = req.body;

  let result;

  const validateParams = (next) => {
    const schema = Joi.object({
      sessionId: Joi.objectId().required(),
      title: Joi.string().optional().trim().max(200),
      description: Joi.string().optional().trim().max(500),
      startTime: Joi.number().optional().min(Date.now()),
      validDurationMinutes: Joi.number().optional().min(1).max(60),
      targetUnits: Joi.array().items(Joi.objectId()).optional(),
      headquarters: Joi.string().valid('main', 'sub').optional()
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Tham số không hợp lệ',
          body: error.details[0].message
        }
      });
    }

    next();
  };

  const updateSession = (next) => {
    try {
      const updateData = {};

      if (title !== undefined) updateData.title = title;
      if (description !== undefined) updateData.description = description;
      if (startTime !== undefined) updateData.startTime = startTime;
      if (validDurationMinutes !== undefined) updateData.validDurationMinutes = validDurationMinutes;
      if (targetUnits !== undefined) updateData.targetUnits = targetUnits;
      if (headquarters !== undefined) updateData.headquarters = headquarters;

      suddenAttendanceService.updateSession(sessionId, updateData, updaterId)
        .then((res) => {
          if (!res || !res.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: res.message || MESSAGES.SYSTEM.ERROR
            });
          }

          result = res;
          next();
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: result.message,
      data: result.data
    });

    // Log hoạt động
    if (global.SystemLogModel) {
      global.SystemLogModel.create({
        user: updaterId,
        action: 'update_sudden_attendance_session',
        description: 'Cập nhật phiên chấm công đột xuất',
        data: req.body,
        updatedData: result.data
      }, () => {});
    }
  };

  async.waterfall([
    validateParams,
    updateSession,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};

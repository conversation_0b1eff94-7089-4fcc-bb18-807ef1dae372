const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Unit = require('../../../../models/unit')
const tool = require('../../../../util/tool');

const DutyShiftModel = require('../../../../models/dutyShift')
const LeaveRequestModel = require('../../../../models/leaveRequest')

module.exports = (req, res) => {

  let name = req.body.name || '';
  let parent = req.body.parent || null
  let units = [];

  let includeWorkStatus = req.body.includeWorkStatus || false;
  const startTime = req.body.startTime || 0;
  const endTime = req.body.endTime || 0;
  const forLeader = req.body.forLeader || false; 
  let countReadyLeaders = 0;
  // Validation: nếu includeWorkStatus = true thì phải có startTime
  if (includeWorkStatus && !startTime) {
    return res.json({
      code: CONSTANTS.CODE.WRONG_PARAMS,
      message: {
        head: 'Thông báo',
        body: "Phải có Thời gian bắt đầu"
      }
    });
  }

  const listUnits = (next) => {

    let objSearch = {
      status: 1,
      parent
    }

    if(name && name.trim()) {
      const nameAlias = tool.change_alias(name.trim());

      // Tìm kiếm theo nameAlias (đã được chuẩn hóa)
      objSearch.nameAlias = { $regex: nameAlias };
    }

    Unit
      .find(objSearch)
      .select('-createdAt')
      .sort({order: 1})
      .populate('parent', 'name')
      .populate('parentPath', 'name')
      .lean()
      .exec((err, results) => {
        if(err) {
          return next(err);
        }
        units = results
        next()
      })
  }

  const countUnit = (next) => {
    async.mapLimit(units, 3, (unit,done) => {
      Unit
        .countDocuments({
          parent: unit._id,
          status:1
        })
        .exec((err,count) => {
          if(err) {
            return done(err);
          }
          unit.countUnit = count;
          done()
        })
    },(err) => {
      if(err) {
        return next(err);
      }
      next();
    })
  }

  const countUser = (next) => {
    async.mapLimit(units, 3, (unit,done) => {
      User
        .countDocuments({
          units: unit._id,
          status:1
        })
        .exec((err,count) => {
          if(err) {
            return done(err);
          }
          unit.countUser = count;
          done()
        })
    },(err) => {
      if(err) {
        return next(err);
      }
      next();
    })
  }


  // Function đếm số cán bộ sẵn sàng dựa trên DutyShift
  const checkDutyShiftLeader = (next) => {
    if(!includeWorkStatus || !forLeader){
      return next();
    }

      User.find({
        units: {
          $size: 1, 
        },
        status: 1
      })
      .select('_id')
      .lean()
      .exec((err, users) => {
        if (err) return next(err);
        
        const userIds = users.map(u => u._id);
        
        // Tạo query condition cho DutyShift
        let dutyQuery = {
          officer: { $in: userIds },
          status: 1
        };
        
        // Nếu có endTime thì query theo khoảng thời gian, nếu không thì chỉ query từ startTime trở đi
        if (endTime) {
          dutyQuery.startTime = { $lt: endTime };
          dutyQuery.endTime = { $gt: startTime };
        } else {
          dutyQuery.endTime = { $gt: startTime };
          dutyQuery.startTime = { $lt: startTime };
        }
        
        // Lấy danh sách cán bộ duy nhất có duty shift
        DutyShiftModel.distinct('officer', dutyQuery)
        .exec((err, officersOnDuty) => {
          if (err) return next(err);
          
          // Số cán bộ sẵn sàng = tổng cán bộ - số cán bộ đang trực
          countReadyLeaders = userIds.length - officersOnDuty.length;
          next();
        });
      });
   
  }

  // Function đếm số cán bộ sẵn sàng dựa trên LeaveRequest
  const checkLeaveRequestLeader = (next) => {
    if(!includeWorkStatus || !forLeader){
      return next();
    }
      // Tìm các user thuộc unit này
      User.find({
        units: {
          $size: 1, 
        },
        status: 1
      })
      .select('_id')
      .lean()
      .exec((err, users) => {
        if (err) return next(err);
        
        const userIds = users.map(u => u._id);
        
        // Tạo query condition cho LeaveRequest
        let leaveQuery = {
          user: { $in: userIds },
          status: 'approved' 
        };
        
        // Nếu có endTime thì query theo khoảng thời gian, nếu không thì chỉ query từ startTime trở đi
        if (endTime) {
          leaveQuery.startTime = { $lt: endTime };
          leaveQuery.endTime = { $gt: startTime };
        } else {
          leaveQuery.endTime = { $gt: startTime };
          leaveQuery.startTime = { $lt: startTime };
        }
        
        // Lấy danh sách cán bộ duy nhất có leave request được duyệt
        LeaveRequestModel.distinct('user', leaveQuery)
        .exec((err, usersOnLeave) => {
          if (err) return next(err);
          
          // Trừ tiếp số cán bộ đang nghỉ phép
          countReadyLeaders = Math.max(0, countReadyLeaders - usersOnLeave.length);
          next();
        });
      });

  }

  // Function đếm số cán bộ sẵn sàng dựa trên DutyShift
  const checkDutyShift = (next) => {
    if(!includeWorkStatus){
      return next();
    }
    async.mapLimit(units, 3, (unit, done) => {
      // Tìm các user thuộc unit này
      User.find({
        units: unit._id,
        status: 1
      })
      .select('_id')
      .lean()
      .exec((err, users) => {
        if (err) return done(err);
        
        const userIds = users.map(u => u._id);
        
        // Tạo query condition cho DutyShift
        let dutyQuery = {
          officer: { $in: userIds },
          status: 1
        };
        
        // Nếu có endTime thì query theo khoảng thời gian, nếu không thì chỉ query từ startTime trở đi
        if (endTime) {
          dutyQuery.startTime = { $lt: endTime };
          dutyQuery.endTime = { $gt: startTime };
        } else {
          dutyQuery.endTime = { $gt: startTime };
          dutyQuery.startTime = { $lt: startTime };
        }
        
        // Lấy danh sách cán bộ duy nhất có duty shift
        DutyShiftModel.distinct('officer', dutyQuery)
        .exec((err, officersOnDuty) => {
          if (err) return done(err);
          
          // Số cán bộ sẵn sàng = tổng cán bộ - số cán bộ đang trực
          unit.countReadyOfficers = userIds.length - officersOnDuty.length;
          done();
        });
      });
    }, (err) => {
      if (err) return next(err);
      next();
    });
  }

  // Function đếm số cán bộ sẵn sàng dựa trên LeaveRequest
  const checkLeaveRequest = (next) => {
    if(!includeWorkStatus){
      return next(null,{
        code: CONSTANTS.CODE.SUCCESS,
        data: units
      });
    }
    async.mapLimit(units, 3, (unit, done) => {
      // Tìm các user thuộc unit này
      User.find({
        units: unit._id,
        status: 1
      })
      .select('_id')
      .lean()
      .exec((err, users) => {
        if (err) return done(err);
        
        const userIds = users.map(u => u._id);
        
        // Tạo query condition cho LeaveRequest
        let leaveQuery = {
          user: { $in: userIds },
          status: 'approved' 
        };
        
        // Nếu có endTime thì query theo khoảng thời gian, nếu không thì chỉ query từ startTime trở đi
        if (endTime) {
          leaveQuery.startTime = { $lt: endTime };
          leaveQuery.endTime = { $gt: startTime };
        } else {
          leaveQuery.endTime = { $gt: startTime };
          leaveQuery.startTime = { $lt: startTime };
        }
        
        // Lấy danh sách cán bộ duy nhất có leave request được duyệt
        LeaveRequestModel.distinct('user', leaveQuery)
        .exec((err, usersOnLeave) => {
          if (err) return done(err);
          
          // Trừ tiếp số cán bộ đang nghỉ phép
          unit.countReadyOfficers = Math.max(0, unit.countReadyOfficers - usersOnLeave.length);
          done();
        });
      });
    }, (err) => {
      if (err) return next(err);
      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        data: units,
        countReadyLeaders
      });
    });
  }

  // Tạo danh sách các function cần chạy
  async.waterfall([
    listUnits,
    countUnit,
    countUser,
    checkDutyShiftLeader,
    checkLeaveRequestLeader,
    checkDutyShift,
    checkLeaveRequest
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
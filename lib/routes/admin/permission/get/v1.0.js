const _ = require("lodash")
const async = require("async")
const ms = require("ms")
const config = require("config")
const util = require("util")
const rp = require("request-promise")

const redisConnection = require("../../../../connections/redis")
const User = require("../../../../models/user")
const CONSTANTS = require("../../../../const")
const MESSAGES = require("../../../../message")
const Permission = require("../../../../models/permission")

module.exports = (req, res) => {
 let id = req.body.id || ""

 const getInf = (next) => {
  Permission.findById(id)
    .populate('categoryPermission','name')
   .lean()
   .exec((err, result) => {
    if (err) {
     return next(err)
    }
    next(null, {
     code: CONSTANTS.CODE.SUCCESS,
     data: result,
    })
   })
 }

 async.waterfall([getInf], (err, data) => {
  err &&
   _.isError(err) &&
   (data = {
    code: CONSTANTS.CODE.SYSTEM_ERROR,
    message: MESSAGES.SYSTEM.ERROR,
   })

  res.json(data || err)
 })
}

const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Permission = require('../../../../models/permission')


module.exports = (req, res) => {

  let name = req.body.name || '';
  let code = req.body.code || '';
  const description = req.body.description || '';
  let categoryPermission = req.body.categoryPermission || '';
  let updatedData = {};
  const checkParams = (next) => {
    if(!name || !name.trim() ){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.PERMISSION.NOT_FOUND_NAME
      })
    }
    if(!code || !code.trim() ){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.PERMISSION.NOT_FOUND_CODE
      })
    }
    next();
  }

  const checkPermissionExists = (next) => {

    Permission
      .find({
        $or:[
          {name}, {code}
        ]
      })
      .lean()
      .exec((err, results) => {
        if(err) {
          return next(err);
        }
        if(results.length) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.PERMISSION.PERMISSION_EXISTS
          })
        }
        next()
      })

  }

  const createPermission = (next) => {

    let objCreate = {
      name,
      code,
      description
    }

    if(categoryPermission) {
      objCreate.categoryPermission = categoryPermission;
    }
    Permission
      .create(objCreate,(err, result) => {
        updatedData = result
        next(err)
      })
  }

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: MESSAGES.PERMISSION.CREATE_SUCCESS,
      data: updatedData._id
    })

    SystemLogModel
      .create({
        user: _.get(req,'user.id', ''),
        action: 'create_permission',
        description: 'Tạo quyền mới',
        data: req.body,
        updatedData
      },() =>{})
   }
  async.waterfall([
    checkParams,
    checkPermissionExists,
    createPermission,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
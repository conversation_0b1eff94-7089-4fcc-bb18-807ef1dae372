
const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Permission = require('../../../../models/permission')


module.exports = (req, res) => {

  let name = req.body.name || '';
  const idPermission = req.body._id || '';
  const description = req.body.description || '';
  const code = req.body.code || '';
  let permissionInf;
  let categoryPermission = req.body.categoryPermission || '';
  let updatedData = {};
  const checkParams = (next) => {
    if(!idPermission){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    if(!name){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.PERMISSION.NOT_FOUND_NAME
      })
    }
    next();
  }

  const checkPermissionExists = (next) => {

    Permission
      .findById(idPermission)
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err);
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.PERMISSION.PERMISSION_NOT_EXISTS
          })
        }
        permissionInf = result
        next()
      })

  }

  const checkNameNotExists = (next) => {

    Permission
      .findOne({
        $or:[
          {
            name: name.trim(),
            code: code.trim(),
          }
        ],
        _id: {
          $ne: idPermission
        },
        status: 1
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        if(result) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.PERMISSION.PERMISSION_EXISTS
          })
        }
        next()
      })

  }

  const updatePermission = (next) => {

    let obj = {
      updatedAt: Date.now(),
      name : name.trim(),
      description: description.trim(),
      code: code.trim(),
    }
    if(categoryPermission) {
      obj.categoryPermission = categoryPermission;
    }
    Permission
      .findOneAndUpdate({
        _id: idPermission
      },
      obj,
      {new: true})
      .lean()
      .exec((err, result) => {
        updatedData = result
        next(err)
      })
  }

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: MESSAGES.PERMISSION.UPDATE_SUCCESS,
    })

    SystemLogModel
      .create({
        user: _.get(req,'user.id', ''),
        action: 'update_permission',
        description: 'Xóa quyền',
        data: req.body,
        updatedData
      },() =>{})
   }

  async.waterfall([
    checkParams,
    checkPermissionExists,
    checkNameNotExists,
    updatePermission,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}

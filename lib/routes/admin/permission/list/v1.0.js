const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Permission = require('../../../../models/permission')
const escapeStringRegexp = require('escape-string-regexp');


module.exports = (req, res) => {

  let name = req.body.name || '';
  let excepts = req.body.excepts || [];

  const listPermissions = (next) => {

    let objSearch = {
      status: 1,
      isAdmin: {
        $ne: true
      }
    }

    if(name && name.trim()) {
      const $regex = escapeStringRegexp(name.trim());
      objSearch.name = {
        $regex,
        $options: 'i'
      }
    }
    if(excepts && excepts.length) {
      objSearch._id = {
        $nin: excepts
      }
    }
    Permission
      .find(objSearch)
      .populate('categoryPermission','name')
      .sort({createdAt:1})
      .lean()
      .exec((err, results) => {
        if(err) {
          return next(err);
        }
        next(null,{
          code: CONSTANTS.CODE.SUCCESS,
          data: results
        })
      })
  }

  async.waterfall([
    listPermissions
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
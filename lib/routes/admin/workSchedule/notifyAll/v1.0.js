const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const PushNotifyManager = require('../../../../jobs/pushNotify');
const SavedNotificationModel = require('../../../../models/savedNotification');

module.exports = (req, res) => {
  const idNotification = '68b1762475317f41a34b6972';
  const updateNotification = (next) => {
    next();

    SavedNotificationModel
      .findByIdAndUpdate(
        idNotification,
        {
          seen: [],
          updatedAt: Date.now()
        }
      )
      .exec((err, result) => {});
  };

  // Push notify
  const pushNotify = (next) => {
    const title = 'Thông báo lịch làm việc';
    const description = 'Đã có lịch làm việc mới. Bạn vui lòng kiểm tra lịch làm việc. Xin cảm ơn.';
    const data = {
      link: 'MainContainer',
      extras: {
        tabIndex: 1
      },
      linkWeb: `/my-work-schedule`
    }

    PushNotifyManager
      .sendAll({}, title, description, data)
      .then((result) => {
        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          message: MESSAGES.SYSTEM.SUCCESS,
          data: result.data
        });
      })
      .catch(err => {
        next(err);
      });
  };

  async.waterfall([
    updateNotification,
    pushNotify
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};

const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);

const scheduleService = require('../../../../services/scheduleService');
const StatisticsTrigger = require('../../../../utils/statisticsTrigger');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

/**
 * API cập nhật lịch làm việc
 * POST /api/v1.0/work-schedule/update
 *
 * Hỗ trợ hai format cho shifts:
 * - Format cũ: shifts: ['morning', 'afternoon'] (status mặc định = 'scheduled')
 * - Format mới: shifts: [{ type: 'morning', status: 'excused' }, { type: 'afternoon', status: 'scheduled' }]
 *
 * Body request:
 * {
 *   "schedules": [
 *     {
 *       "scheduleId": "ObjectId", // ID của lịch cần cập nhật
 *       "shifts": ["morning", "afternoon"] // Format cũ hoặc format mới với objects
 *     }
 *   ]
 * }
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const { schedules } = req.body;

  let result;

  /**
   * Hàm validation tham số đầu vào
   * Kiểm tra cấu trúc dữ liệu schedules với scheduleId và shifts
   */
  const validateParams = (next) => {
    // Schema cho shift object format mới
    const shiftObjectSchema = Joi.object({
      type: Joi.string().valid('morning', 'afternoon').required(),
      status: Joi.string().valid('scheduled', 'excused', 'business_trip').default('scheduled').optional()
    });

    // Schema cho shift string format cũ
    const shiftStringSchema = Joi.string().valid('morning', 'afternoon');

    const schema = Joi.object({
      // Danh sách lịch cần cập nhật
      schedules: Joi.array().items(
        Joi.object({
          // ID của lịch làm việc cần cập nhật (bắt buộc)
          scheduleId: Joi.objectId().required(),

          // Danh sách ca trực mới (bắt buộc, ít nhất 1 ca) - hỗ trợ cả hai format
          shifts: Joi.array()
            .items(Joi.alternatives().try(shiftStringSchema, shiftObjectSchema))
            .min(1)
            .required()
        })
      ).min(1).required() // Phải có ít nhất 1 lịch để cập nhật
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    // Kiểm tra tính hợp lệ của schedules
    if (!schedules || schedules.length === 0) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: 'Phải có ít nhất một lịch để cập nhật'
        }
      });
    }

    next();
  };

  /**
   * Chuẩn hóa dữ liệu shifts để hỗ trợ cả format cũ và mới
   * @param {Array} schedules - Mảng lịch từ client
   * @returns {Array} Mảng lịch đã được chuẩn hóa
   */
  const normalizeSchedulesData = (schedules) => {
    return schedules.map(schedule => {
      const normalizedShifts = schedule.shifts.map(shift => {
        // Nếu shift là string (format cũ), chuyển thành object với status mặc định
        if (typeof shift === 'string') {
          return {
            type: shift,
            status: 'scheduled'
          };
        }
        // Nếu shift đã là object (format mới), giữ nguyên nhưng đảm bảo có status
        return {
          type: shift.type,
          status: shift.status || 'scheduled'
        };
      });

      return {
        ...schedule,
        shifts: normalizedShifts
      };
    });
  };

  /**
   * Hàm xử lý cập nhật lịch làm việc
   * Gọi service để cập nhật danh sách lịch theo ID và ca trực mới
   */
  const updateSchedule = (next) => {
    try {
      // Chuẩn hóa dữ liệu shifts để hỗ trợ cả format cũ và mới
      const normalizedSchedules = normalizeSchedulesData(schedules);

      // Gọi service mới để cập nhật lịch theo danh sách ID
      scheduleService.updateWorkSchedulesByIds(userId, normalizedSchedules)
        .then((res) => {
          if (!res || !res.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: res.message || MESSAGES.SYSTEM.ERROR
            });
          }

          result = res;

          next();
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Hàm ghi log và trả về kết quả
   * Ghi lại hoạt động cập nhật lịch làm việc vào system log
   */
  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: result.message,
      data: result.data
    });

    // Trigger statistics update cho từng schedule được cập nhật
    if (result.success && result.data && result.data.updatedSchedules) {
      result.data.updatedSchedules.forEach(schedule => {
        StatisticsTrigger.triggerWorkScheduleUpdate('update', {
          _id: schedule._id,
          user: schedule.user,
          date: schedule.date,
          shifts: schedule.shifts
        });
      });
    }

    // Ghi log hoạt động cập nhật lịch làm việc
    try {
      SystemLogModel && SystemLogModel.create({
        user: userId,
        action: 'update_work_schedule_by_ids',
        description: 'Cập nhật lịch làm việc theo danh sách ID',
        data: req.body,
        updatedData: result.data,
        createdAt: Date.now()
      }, () => { });
    } catch (logError) {
      // Không làm gián đoạn response nếu log lỗi
      console.error('Lỗi ghi log:', logError.message);
    }
  };

  async.waterfall([
    validateParams,
    updateSchedule,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};
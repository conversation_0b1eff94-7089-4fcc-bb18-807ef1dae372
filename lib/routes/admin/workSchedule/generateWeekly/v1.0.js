const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Jo<PERSON>);

const scheduleService = require('../../../../services/scheduleService');
const StatisticsTrigger = require('../../../../utils/statisticsTrigger');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

/**
 * API tạo lịch làm việc tuần cho tất cả cán bộ trong hệ thống
 * POST /api/v1.0/admin/work-schedule/generate-weekly
 *
 * M<PERSON> tả:
 * - Tự động tìm tuần tiếp theo chưa có lịch làm việc nào
 * - Tạo lịch cho tất cả cán bộ active trong hệ thống
 * - Hỗ trợ 4 loại lịch: ca sáng, ca chiề<PERSON>, c<PERSON> ng<PERSON>, kế thừa tuần trước
 *
 * Tham số đầu vào:
 * - scheduleType: Loạ<PERSON> lịch cần tạo
 *   + 'morning_only': Tất cả cán bộ làm ca sáng (8:00)
 *   + 'afternoon_only': Tất cả cán bộ làm ca chiều (14:00)
 *   + 'full_day': Tất cả cán bộ làm cả ngày (ca sáng và ca chiều)
 *   + 'inherit_previous': Kế thừa lịch từ tuần trước, nếu không có thì mặc định full_day
 *
 * Kết quả trả về:
 * - Thông tin tuần được tạo lịch
 * - Số lượng cán bộ và ca làm việc được tạo
 * - Chi tiết lỗi nếu có
 */
module.exports = (req, res) => {
  const creatorId = req.user.id;
  const { scheduleType } = req.body;

  let result;

  /**
   * Bước 1: Validate tham số đầu vào
   */
  const validateParams = (next) => {
    const schema = Joi.object({
      scheduleType: Joi.string()
        .valid('morning_only', 'afternoon_only', 'full_day', 'inherit_previous')
        .required()
        .messages({
          'any.required': 'Vui lòng chọn loại lịch làm việc',
          'any.only': 'Loại lịch phải là: morning_only, afternoon_only, full_day, hoặc inherit_previous'
        })
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  /**
   * Bước 2: Tạo lịch làm việc tuần
   */
  const generateWeeklySchedule = (next) => {
    try {
      scheduleService.generateWeeklyScheduleForAllUsers(creatorId, scheduleType)
        .then((res) => {
          if (!res || !res.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: res.message || {
                head: 'Lỗi hệ thống',
                body: 'Không thể tạo lịch làm việc tuần'
              }
            });
          }

          result = res;
          next();
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Bước 3: Ghi log và trả kết quả
   */
  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: result.message,
      data: result.data
    });

    // Trigger statistics update cho các schedule được tạo
    if (result.success && result.data && result.data.schedules) {
      result.data.schedules.forEach(schedule => {
        StatisticsTrigger.triggerWorkScheduleUpdate('create', {
          _id: schedule._id,
          user: schedule.user,
          date: schedule.date,
          shifts: schedule.shifts
        });
      });
    }

    // Log hoạt động tạo lịch tuần
    SystemLogModel && SystemLogModel.create({
      user: creatorId,
      action: 'generate_weekly_schedule',
      description: `Tạo lịch làm việc tuần - Loại: ${scheduleType}`,
      data: {
        scheduleType: scheduleType,
        requestBody: req.body
      },
      updatedData: result.data
    }, () => { });
  };

  // Thực thi các bước
  async.waterfall([
    validateParams,
    generateWeeklySchedule,
    writeLog
  ], (err, data) => {
    // Xử lý lỗi
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Trả về lỗi hệ thống nếu có lỗi không mong muốn
    if (err && _.isError(err)) {
      data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      }
    }

    res.json(data || err);
  });
};

const _ = require('lodash')
const async = require('async')
const config = require('config')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const SavedNotification = require('../../../../models/savedNotification')
const tool = require('../../../../util/tool')
const MailUtil = require('../../../../util/mail')
const PushNotifyManager = require('../../../../jobs/pushNotify')


module.exports = (req, res) => {
  const { _id, titlePush, messagePush, userId, type } = req.body;
  const notificationId = _id
  if (!titlePush || !messagePush) {
    return res.json({
      code: CONSTANTS.CODE.FAIL,
      message: MESSAGES.SYSTEM.WRONG_PARAMS
    });
  }

  async.waterfall([
    // Bước 1: Lấy thông tin notification nếu có notificationId
    (callback) => {
      if (notificationId) {
        SavedNotification.findById(notificationId)
          .exec((err, notification) => {
            if (err) return callback(err);
            if (!notification) {
              return callback({
                code: CONSTANTS.CODE.FAIL,
                message: {
                  head: 'Thông báo',
                  body: 'Không tìm thấy thông báo với ID đã cung cấp'
                }
              });
            }
            callback(null, notification);
          });
      } else {
        callback(null, null);
      }
    },

    // Bước 2: Gửi thông báo
    (notification, callback) => {
      if (notification) {
        if(notification.data) {
          notification.data.linkWeb = `/profile/${notificationId}`
        }
        // Gửi dựa trên notification có sẵn
        if (notification.type === 'user' && notification.users.length > 0) {
          if(notification.data && notification.data.extras && notification.data.extras.content){
            delete notification.data.extras.content
          }
          PushNotifyManager.sendAll({
            _id: {
              $in: notification.users
            }
          }, titlePush, messagePush, notification.data || {})
            .then((result) => {
              callback(null, notification, result);
            })
            .catch((error) => {
              callback(error);
            });
        } else if (notification.type === 'all') {
          // Gửi cho tất cả
          if(notification.data && notification.data.extras && notification.data.extras.content){
            delete notification.data.extras.content
          }
          PushNotifyManager.sendAll({}, titlePush, messagePush, notification.data || {})
            .then((result) => {
              callback(null, notification, result);
            })
            .catch((error) => {
              callback(error);
            });
        } else {
          callback({
            code: CONSTANTS.CODE.FAIL,
            message: {
              head: 'Thông báo',
              body: 'Loại thông báo không hợp lệ hoặc không có người nhận'
            }
          });
        }
      } else {
        callback({
          code: CONSTANTS.CODE.FAIL,
          message: {
            head: 'Thông báo',
            body: 'Thiếu thông tin thông báo'
          }
        });
      }
    },

    // Bước 3: Cập nhật statusPush về 1 nếu có notificationId
    (notification, pushResult, callback) => {
      if (notification) {
        SavedNotification.updateOne(
          { _id: notification._id },
          { 
            $set: { 
              statusPush: 1,
              titlePush: titlePush,
              messagePush: messagePush,
              updatedAt: Date.now()
            }
          },
          (err, updateResult) => {
            if (err) return callback(err);
            callback(null, {
              code: CONSTANTS.CODE.SUCCESS,
              message: {
                head: 'Thông báo',
                body: 'Gửi thông báo thành công'
              },
              data: {
                pushResult: pushResult,
                updateResult: updateResult
              }
            });
          }
        );
      } else {
        callback(null, {
          code: CONSTANTS.CODE.SUCCESS,
          message: {
            head: 'Thông báo',
            body: 'Gửi thông báo thành công'
          },
          data: {
            pushResult: pushResult
          }
        });
      }
    }
  ], (err, data) => {
    if (_.isError(err)) {
      tool.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  });
}

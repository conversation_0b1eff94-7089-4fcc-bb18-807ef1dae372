const _ = require('lodash');
const async = require('async');
const config = require('config');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Model = require('../../../../models/savedNotification'); //<PERSON> tên cho dễ nhân bản

module.exports = (req, res) => {
  let {
    page = 1,
    limit = 20,
    type = '', // Lọc theo loại gửi: 'all', 'unit', 'user'
    unit = '', // Lọc theo đơn vị cụ thể
    status = '', // Lọc theo trạng thái: 1 = active, 0 = inactive
    statusPush = '', // Lọc theo trạng thái push: 1 = có push, 0 = không push
    search = '', // Tìm kiếm theo tiêu đề
  } = req.body;

  // Validate và convert parameters
  page = parseInt(page) || 1;
  limit = parseInt(limit) || 20;
  limit = Math.min(limit, 100); // Giới hạn tối đa 100 records per page

  search = search.trim();
  type = type.trim();
  unit = unit.trim();

  let notifications = [];
  let totalCount = 0;

  const buildQuery = (next) => {
    let query = {};

    // Lọc theo status (mặc định chỉ lấy notification chưa bị xóa)
    if (status !== '') {
      query.status = parseInt(status);
    } else {
      query.status = { $ne: 0 }; // Mặc định không lấy notification đã xóa
    }

    // Lọc theo loại gửi
    if (type && ['all', 'unit', 'user'].includes(type)) {
      query.type = type;
    }

    // Lọc theo đơn vị
    if (unit) {
      query.units = unit;
    }

    // Lọc theo trạng thái push
    if (statusPush !== '') {
      query.statusPush = parseInt(statusPush);
    }

    // Tìm kiếm theo tiêu đề
    if (search) {
      query.title = { $regex: search, $options: 'i' }; // Case insensitive search
    }
    query['data.link'] = 'DetailNotificationScreen';
    req.query = query;
    next();
  };

  const getTotal = (next) => {
    Model
      .countDocuments(req.query)
      .exec((err, count) => {
        if (err) {
          return next(err);
        }

        totalCount = count;
        next();
      });
  };

  const getNotifications = (next) => {
    const skip = (page - 1) * limit;

    // Build sort object
    let sortObj = {};

    Model
      .find(req.query)
      .sort({
        updatedAt: -1
      })
      .skip(skip)
      .limit(limit)
      .select('-data -seen')
      .populate('units', 'name')
      .populate('users', 'name idNumber')
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        notifications = results || [];
        next();
      });
  };

  const formatResponse = (next) => {
    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    // Format notifications data
    const formattedNotifications = notifications

    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        notifications: formattedNotifications,
        pagination: {
          currentPage: page,
          totalPages: totalPages,
          totalCount: totalCount,
          limit: limit,
        }
      }
    });
  };

  async.waterfall([
    buildQuery,
    getTotal,
    getNotifications,
    formatResponse
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
      });

    res.json(data || err);
  });
};

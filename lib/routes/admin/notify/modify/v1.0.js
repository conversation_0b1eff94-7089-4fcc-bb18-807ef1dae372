const _ = require('lodash')
const async = require('async')
const config = require('config')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const SavedNotification = require('../../../../models/savedNotification')
const tool = require('../../../../util/tool')
const PushNotifyManager = require('../../../../jobs/pushNotify')


module.exports = (req, res) => {
  let {
    image = '',
    _id = '',
    title = '',
    description = '',
    titlePush = '',
    messagePush = '',
    content = '',
    type = '',
    units = [],
    users = [],
    status = null
  } = req.body;

  // Trim các string inputs
  title = title.trim();
  titlePush = titlePush.trim();
  messagePush = messagePush.trim();
  id = _id.trim();

  let existingNotification = {};
  let updatedNotification = {};

  const checkParams = (next) => {
    // Kiểm tra ID
    if (!id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    if (!content) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SAVED_NOTIFICATION.MISSING_CONTENT
      });
    }

    // Nếu có title thì phải không rỗng
    if (title !== '' && !title) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SAVED_NOTIFICATION.MISSING_TITLE
      });
    }

    if (description !== '' && !description) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SAVED_NOTIFICATION.MISSING_DESCRIPTION
      });
    }

    // Kiểm tra loại thông báo nếu có
    if (type && !['all', 'unit', 'user'].includes(type)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SAVED_NOTIFICATION.INVALID_TYPE
      });
    }

    next();
  };

  const checkNotificationExists = (next) => {
    SavedNotification
      .findOne({
        _id: id,
        status: { $ne: 0 } // Không tìm notification đã bị xóa
      })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.SYSTEM.DATA_NOT_FOUND
          });
        }

        existingNotification = result;
        next();
      });
  };

  const validateRecipientsIfTypeChanged = (next) => {
    // Nếu type không thay đổi thì bỏ qua validation
    if (!type || type === existingNotification.type) {
      return next();
    }

    // Kiểm tra người nhận dựa trên type mới
    if (type === 'unit' && (!units || !units.length)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SAVED_NOTIFICATION.MISSING_RECIPIENTS
      });
    }

    if (type === 'user' && (!users || !users.length)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SAVED_NOTIFICATION.MISSING_RECIPIENTS
      });
    }

    next();
  };

  const updateNotification = (next) => {
    let updateObj = {
      updatedAt: Date.now(),
      data: {
        link: 'DetailNotificationScreen',
        extras: {
          content,
          _id
        }
      }
    };
  
    // Chỉ update các field được gửi lên
    if (image) updateObj.image = image;
    if (title) updateObj.title = title;
    if (description) updateObj.description = description;
    if (titlePush !== '') updateObj.titlePush = titlePush;
    if (messagePush !== '') updateObj.messagePush = messagePush;
    if (type) updateObj.type = type;
    if (status !== null) updateObj.status = status;

    // Update recipients based on type
    if (type) {
      if (type === 'unit') {
        updateObj.units = units || [];
        updateObj.users = []; // Clear users when switching to unit type
      } else if (type === 'user') {
        updateObj.users = users || [];
        updateObj.units = []; // Clear units when switching to user type
      } else if (type === 'all') {
        updateObj.units = [];
        updateObj.users = [];
      }
    } else {
      // Nếu không thay đổi type nhưng có update recipients
      if (existingNotification.type === 'unit' && units.length > 0) {
        updateObj.units = units;
      }
      if (existingNotification.type === 'user' && users.length > 0) {
        updateObj.users = users;
      }
    }

    SavedNotification
      .findByIdAndUpdate(
        id,
        { $set: updateObj },
        { new: true }
      )
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.SYSTEM.DATA_NOT_FOUND
          });
        }

        updatedNotification = result;
        next();
      });
  };

  
  const returnResult = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: MESSAGES.SAVED_NOTIFICATION.UPDATE_SUCCESS,
      data: updatedNotification
    });
  };

  async.waterfall([
    checkParams,
    checkNotificationExists,
    validateRecipientsIfTypeChanged,
    updateNotification,
    returnResult
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  });
}

const _ = require('lodash')
const async = require('async')
const config = require('config')
const CONSTANTS = require('../../../../const')
const SavedNotification = require('../../../../models/savedNotification')
const MESSAGES = require('../../../../message');


module.exports = (req, res) => {
  let {
    id = ''
  } = req.body;

  id = id.trim();
  let deletedNotification = {};

  const checkParams = (next) => {
    // Kiểm tra phải có ID
    if (!id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const checkNotificationExists = (next) => {
    SavedNotification
      .findOne({
        _id: id,
        status: { $ne: 0 } // Chỉ tìm notification chưa bị xóa
      })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.SYSTEM.ERROR
          });
        }

        deletedNotification = result;
        next();
      });
  };

  const inactiveNotification = (next) => {
    const updateObj = {
      status: 0, // Set status về 0 để "xóa" notification
      updatedAt: Date.now()
    };

    SavedNotification
      .findByIdAndUpdate(
        id,
        { $set: updateObj },
        { new: true }
      )
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.SYSTEM.ERROR
          });
        }

        next();
      });
  };

  const returnResult = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: MESSAGES.SAVED_NOTIFICATION.DELETE_SUCCESS,
      data: {
        id: deletedNotification._id,
        title: deletedNotification.title,
        type: deletedNotification.type
      }
    });
  };

  async.waterfall([
    checkParams,
    checkNotificationExists,
    inactiveNotification,
    returnResult
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  });
}
const _ = require('lodash')
const async = require('async')
const config = require('config')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const SavedNotification = require('../../../../models/savedNotification')
const User = require('../../../../models/user')
const tool = require('../../../../util/tool')
const MailUtil = require('../../../../util/mail')
const PushNotifyManager = require('../../../../jobs/pushNotify')


module.exports = (req, res) => {
  const { _id, titlePush, messagePush, userId, textSearch } = req.body;
  const text = textSearch || '';
  const notificationId = _id
  
  if (!titlePush || !messagePush) {
    return res.json({
      code: CONSTANTS.CODE.FAIL,
      message: MESSAGES.SYSTEM.WRONG_PARAMS
    });
  }

  // Kiểm tra phải có ít nhất một trong các thông tin: userId, text, hoặc notificationId
  if (!userId && !text && !notificationId) {
    return res.json({
      code: CONSTANTS.CODE.FAIL,
      message: {
        head: 'Thông báo',
        body: 'Vui lòng cung cấp ít nhất một trong các thông tin: userId, text (số điện thoại hoặc số hiệu), hoặc ID thông báo'
      }
    });
  }

  async.waterfall([
    // Bước 1: Tìm user theo text (số điện thoại hoặc số hiệu)
    (callback) => {
      if (userId) {
        // Nếu có userId trực tiếp thì dùng luôn
        callback(null, { targetUserId: userId, foundUser: null });
      } else if (text) {
        // Tìm user theo text (có thể là số điện thoại hoặc số hiệu)
        const query = {
          $or: [
            { phones: text },    // Tìm theo số điện thoại
            { idNumber: text }              // Tìm theo số hiệu
          ]
        };
        
        User.findOne(query, '_id name idNumber phones')
          .exec((err, user) => {
            if (err) return callback(err);
            if (!user) {
              return callback({
                code: CONSTANTS.CODE.FAIL,
                message: {
                  head: 'Thông báo',
                  body: `Không tìm thấy người dùng với thông tin: ${text}`
                }
              });
            }
            callback(null, { targetUserId: user._id, foundUser: user });
          });
      } else {
        callback(null, { targetUserId: null, foundUser: null });
      }
    },

    // Bước 2: Lấy thông tin notification nếu có notificationId
    (userInfo, callback) => {
      if (notificationId) {
        SavedNotification.findById(notificationId)
          .exec((err, notification) => {
            if (err) return callback(err);
            if (!notification) {
              return callback({
                code: CONSTANTS.CODE.FAIL,
                message: {
                  head: 'Thông báo',
                  body: 'Không tìm thấy thông báo với ID đã cung cấp'
                }
              });
            }
            callback(null, userInfo, notification);
          });
      } else {
        callback(null, userInfo, null);
      }
    },

    // Bước 3: Gửi thông báo
    (userInfo, notification, callback) => {
      if (userInfo.targetUserId) {
        // Gửi cho user cụ thể (tìm được từ số điện thoại/số hiệu hoặc userId trực tiếp)
        if(notification.data && notification.data.extras && notification.data.extras.content){
          delete notification.data.extras.content
        }
        if(notification.data) {
          notification.data.linkWeb = `/profile/${notificationId}`
        }
        PushNotifyManager.sendToMember(
          userInfo.targetUserId, 
          titlePush, 
          messagePush, 
          notification?.data || {},
          '',
          'ioc'
        )
          .then((result) => {
            callback(null, {
              code: CONSTANTS.CODE.SUCCESS,
              message: {
                  head: 'Thông báo',
                  body: `Đã gửi thông báo đến người dùng `
              },
              data: {
                
                result
              }
            });
          })
          .catch((error) => {
            callback({
              code: CONSTANTS.CODE.FAIL,
              message: {
                head: 'Thông báo',
                body: 'Không thể gửi thông báo đến người dùng'
              }
            });
          });
      } else {
        callback({
          code: CONSTANTS.CODE.FAIL,
          message: {
            head: 'Thông báo',
            body: 'Không tìm thấy người dùng để gửi thông báo'
          }
        });
      }
    }

  ], (err, data) => {
    if (_.isError(err)) {
      tool.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR
    });

    res.json(data || err);
  });
}

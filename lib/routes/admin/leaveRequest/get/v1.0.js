const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);

const leaveService = require('../../../../services/leaveService');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

/**
 * API lấy chi tiết lịch nghỉ/công tác
 * POST /api/v1.0/admin/leave-request/get
 */
module.exports = (req, res) => {
  const { _id } = req.body;

  let result;

  const validateParams = (next) => {
    const schema = Joi.object({
      _id: Joi.objectId().required()
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const getLeaveRequest = (next) => {
    try {
      leaveService.getLeaveRequestDetail(_id)
        .then((res) => {
          if (!res || !res.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: res && res.message ? res.message : MESSAGES.SYSTEM.ERROR
            });
          }

          result = res;
          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: result.data
          });
        })
        .catch((err) => next(err));
    } catch (error) {
      next(error);
    }
  };

  async.waterfall([
    validateParams,
    getLeaveRequest
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};

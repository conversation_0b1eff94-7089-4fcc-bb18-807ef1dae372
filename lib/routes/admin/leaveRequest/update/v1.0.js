const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);

const leaveService = require('../../../../services/leaveService');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

/**
 * API cập nhật lịch nghỉ/công tác
 * POST /api/v1.0/admin/leave-request/update
 */
module.exports = (req, res) => {
  const {
    _id,
    user,
    type,
    startDate,
    endDate,
    reason,
    attachments
  } = req.body;

  let result;

  const validateParams = (next) => {
    const schema = Joi.object({
      _id: Joi.objectId().required(),
      user: Joi.objectId().optional(),
      type: Joi.string().valid('leave', 'business_trip').optional(),
      startDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).optional(),
      endDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).optional(),
      reason: Joi.string().optional().allow('').trim().max(500),
      attachments: Joi.array().items(Joi.string().uri()).optional()
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const updateLeave = (next) => {
    try {
      const payload = _.pick({ user, type, startDate, endDate, reason, attachments }, ['user', 'type', 'startDate', 'endDate', 'reason', 'attachments']);
      leaveService.updateLeaveRequest(_id, payload)
        .then((res) => {
          if (!res || !res.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: res && res.message ? res.message : MESSAGES.SYSTEM.ERROR
            });
          }

          result = res;
          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            message: result.message,
            data: result.data
          });
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  async.waterfall([
    validateParams,
    updateLeave
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};

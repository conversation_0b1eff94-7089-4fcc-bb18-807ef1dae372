const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);

const leaveService = require('../../../../services/leaveService');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

/**
 * API danh sách lịch nghỉ/công tác (filter/sort/pagination)
 * POST /api/v1.0/admin/leave-request/list
 */
module.exports = (req, res) => {
  const {
    type, // 'leave' | 'business_trip' (optional)
    unit, // ObjectId Unit (optional)
    user, // ObjectId User (optional)
    startDate, // 'dd-mm-yyyy' (optional)
    endDate, // 'dd-mm-yyyy' (optional)
    page = 1, // number (optional)
    limit = 20, // number (optional)
    sortBy = 'name', // 'name' | 'unit' (optional)
    sortOrder = 'asc', // 'asc' | 'desc' (optional)
    textSearch
  } = req.body;

  let result;

  const validateParams = (next) => {
    const schema = Joi.object({
      type: Joi.string().valid('leave', 'business_trip').optional(),
      unit: Joi.objectId().optional(),
      user: Joi.objectId().optional(),
      startDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).optional(),
      endDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).optional(),
      page: Joi.number().min(1).optional().default(1),
      limit: Joi.number().min(1).max(100).optional().default(20),
      sortBy: Joi.string().valid('name', 'unit').optional().default('name'),
      sortOrder: Joi.string().valid('asc', 'desc').optional().default('asc'),
      textSearch: Joi.string().optional().trim()
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const listLeaves = (next) => {
    try {
      const filters = { type, unit, user, startDate, endDate, textSearch };
      const pagination = { page, limit, sortBy, sortOrder };
      leaveService.listLeaveRequests(filters, pagination)
        .then((res) => {
          if (!res || !res.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: res && res.message ? res.message : MESSAGES.SYSTEM.ERROR
            });
          }

          result = res;
          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: result.data
          });
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  async.waterfall([
    validateParams,
    listLeaves
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};

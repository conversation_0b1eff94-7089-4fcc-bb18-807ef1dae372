const _ = require('lodash')
const async = require('async')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Area = require('../../../../models/area')


module.exports = (req, res) => {

  let _id = req.body._id || '';

  const getArea = (next) => {

    let objSearch = {
      // status: 1,
      _id
    }

    Area
      .findOne(objSearch)
      .populate('parent', 'name') // populate thông tin khu vực cha
      .populate('parentPath', 'name') // populate đường dẫn phân cấp
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: result
        })
      })
  }

  async.waterfall([
    getArea
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
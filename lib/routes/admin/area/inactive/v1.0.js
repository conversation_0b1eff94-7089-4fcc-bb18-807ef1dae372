const _ = require('lodash')
const async = require('async')
const StatisticsTrigger = require('../../../../utils/statisticsTrigger')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Area = require('../../../../models/area')


module.exports = (req, res) => {
  const idArea = req.body._id || '';
  let updatedData = {};

  const checkParams = (next) => {
    if (!idArea) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  }


  const inactiveArea = (next) => {

    Area
      .findOneAndUpdate({
        _id: idArea
      }, {
        status: 0
      }, { new: true })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.AREA.AREA_NOT_EXISTS
          });
        }

        updatedData = result;

        next();
      })
  }

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: MESSAGES.AREA.INACTIVE_SUCCESS
    });

    // Trigger statistics update khi vô hiệu hóa area
    try {
      StatisticsTrigger.triggerAreaUpdate('delete', {
        _id: updatedData._id,
        name: updatedData.name,
        level: updatedData.level,
        status: updatedData.status
      });
    } catch (error) {
      console.error('Error triggering area update:', error);
    }

    SystemLogModel.create(
      {
        user: _.get(req, 'user.id', ''),
        action: 'inactive_area',
        description: 'Vô hiệu hóa khu vực địa bàn',
        data: req.body,
        updatedData,
      },
      () => { }
    );
  };

  async.waterfall([
    checkParams,
    inactiveArea,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
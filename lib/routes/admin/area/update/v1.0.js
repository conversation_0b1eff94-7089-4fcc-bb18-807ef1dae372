const _ = require('lodash');
const async = require('async');
const StatisticsTrigger = require('../../../../utils/statisticsTrigger');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Area = require('../../../../models/area');
const tool = require('../../../../util/tool');

module.exports = (req, res) => {
  const idArea = req.body._id || '';
  let {
    name = '',
    description = '',
    level = null, // cấp độ khu vực: 1 = khu vực lớn, 2 = tổ dân phố
    parent = null, // ID khu vực cha (chỉ áp dụng cho level 2)
    boundaries = [],
    routes = [],
    familyCount = 0,
    populationCount = 0,
    area = 0,
    leader = '',
    leaderPhone = '',
  } = req.body;
  name = name.trim();
  description = description.trim();

  let areaInf = {};
  let updatedData = {};

  const checkParams = (next) => {
    if (!idArea) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS,
      });
    }

    // Validation cho cấu trúc phân cấp (nếu có thay đổi level)
    if (level && (![1, 2].includes(level) || (level === 1 && parent) || (level === 2 && !parent))) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS,
      });
    }

    if (!name && !description && !routes && !familyCount && !populationCount && !area && !boundaries && !level && !boundaries.length && !routes.length && !parent && !leader && !leaderPhone) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.AREA.NOT_CHANGE,
      });
    }

    next();
  };

  const checkAreaExists = (next) => {
    Area.findById(idArea)
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.AREA.AREA_NOT_EXISTS,
          });
        }

        areaInf = result;

        next();
      });
  };

  const validateParentArea = (next) => {
    // Nếu không có parent hoặc parent không thay đổi thì bỏ qua bước này
    if (!parent || parent === areaInf.parent) {
      return next();
    }

    // Kiểm tra parent area có tồn tại và phải là level 1
    Area.findOne({
      _id: parent,
      // status: 1,
      level: 1,
    })
      .lean()
      .exec((err, parentArea) => {
        if (err) {
          return next(err);
        }

        if (!parentArea) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.AREA.AREA_NOT_EXISTS,
          });
        }

        // Kiểm tra không được tự tham chiếu
        if (parent === idArea) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.AREA.CANNOT_SELF_REFERENCE,
          });
        }

        next();
      });
  };

  const checkNameNotExists = (next) => {
    if (!name || name === areaInf.name) {
      return next();
    }

    Area.findOne({
      name,
      _id: {
        $ne: idArea,
      },
      // status: 1,
    })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (result) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.AREA.AREA_EXISTS,
          });
        }

        next();
      });
  };

  const updateArea = (next) => {
    let objUpdate = {};

    if (name && name !== areaInf.name) {
      objUpdate.name = name;
      objUpdate.nameAlias = tool.change_alias(name);
    }

    // Cập nhật level nếu có thay đổi
    if (level && level !== areaInf.level) {
      objUpdate.level = level;
    }

    // Cập nhật parent và parentPath nếu có thay đổi
    if (parent !== undefined && parent !== areaInf.parent) {
      objUpdate.parent = parent;
      // Cập nhật parentPath: nếu có parent thì parentPath sẽ bao gồm parent
      objUpdate.parentPath = parent ? [parent] : [];
    }

    if (routes && routes.length && !_.isEqual(routes, areaInf.routes)) {
      objUpdate.routes = routes;
    }

    if (familyCount !== undefined && familyCount !== areaInf.familyCount) {
      objUpdate.familyCount = familyCount;
    }

    if (description && description !== areaInf.description) {
      objUpdate.description = description;
    }

    if (boundaries && boundaries.length && !_.isEqual(boundaries, areaInf.boundaries)) {
      objUpdate.boundaries = boundaries;
    }

    if (populationCount !== undefined && populationCount !== areaInf.populationCount) {
      objUpdate.populationCount = populationCount;
    }

    if (area && area !== areaInf.area) {
      objUpdate.area = area;
    }

    if (leader && leader !== areaInf.leader) {
      objUpdate.leader = leader;
    }

    if (leaderPhone && leaderPhone !== areaInf.leaderPhone) {
      objUpdate.leaderPhone = leaderPhone;
    }

    if (Object.keys(objUpdate).length) {
      objUpdate.updatedAt = Date.now();
    } else {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.AREA.NOT_CHANGE,
      });
    }

    Area.findOneAndUpdate(
      {
        _id: idArea,
      },
      objUpdate,
      {
        new: true,
      }
    )
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        updatedData = result;

        next();
      });
  };

  const updateParent = (next) => {
    if (!parent) {
      return next();
    }
    let populationCountChange = populationCount - areaInf.populationCount;
    let familyCountChange = familyCount - areaInf.familyCount;
    Area.findByIdAndUpdate({ _id: parent }, { $inc: { populationCount: populationCountChange, familyCount: familyCountChange } }, { new: true })
      .lean()
      .exec((err, result) => {
        next();
      });
  };

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: MESSAGES.AREA.UPDATE_SUCCESS,
    });

    // Trigger statistics update khi cập nhật area
    try {
      StatisticsTrigger.triggerAreaUpdate('update', {
        _id: updatedData._id,
        name: updatedData.name,
        level: updatedData.level,
        status: updatedData.status
      });
    } catch (error) {
      console.error('Error triggering area update:', error);
    }

    SystemLogModel.create(
      {
        user: _.get(req, 'user.id', ''),
        action: 'update_area',
        description: 'Cập nhật khu vực địa bàn',
        data: req.body,
        updatedData,
      },
      () => {}
    );
  };

  async.waterfall([checkParams, checkAreaExists, validateParentArea, checkNameNotExists, updateArea, updateParent, writeLog], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};

const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Position = require('../../../../models/position')
const Permission = require('../../../../models/permission')
const GroupPermission = require('../../../../models/groupPermission')


module.exports = (req, res) => {

  let name = req.body.name || '';
  const idPosition = req.body._id || '';
  const unit = req.body.unit || '';
  const permissions = req.body.permissions || [];
  const groupPermissions = req.body.groupPermissions || [];

  let positionInf;
  let updatedData = {};
  const checkParams = (next) => {
    if(!idPosition){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    if(!name){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.POSITION.NOT_FOUND_NAME
      })
    }
    next();
  }

  const checkPositionExists = (next) => {

    Position
      .findById(idPosition)
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err);
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.POSITION.POSITION_NOT_EXISTS
          })
        }
        positionInf = result
        next()
      })

  }

  const checkNameNotExists = (next) => {

    Position
      .findOne({
        name: name.trim(),
        unit,
        _id: {
          $ne: idPosition
        },
        status: 1
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        if(result) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.POSITION.POSITION_EXISTS
          })
        }
        next()
      })

  }


  const createPosition = (next) => {

    let obj = {
      updatedAt: Date.now(),
      name : name.trim(),
      unit: unit.trim(),
      permissions,
      groupPermissions
    }

    Position
      .update({
        _id: idPosition
      },obj,(err, result) => {
        if(err) {
          return next(err);
        }
        updatedData = result;
        next(null)
      })
  }

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: MESSAGES.POSITION.UPDATE_SUCCESS,
    });

    SystemLogModel.create(
      {
        user: _.get(req,'user.id', ''),
        action: 'update_position',
        description: 'Cập nhật vị trí',
        data: req.body,
        updatedData,
      },
      () => {}
    );
  };

  async.waterfall([
    checkParams,
    checkPositionExists,
    checkNameNotExists,
    createPosition,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}

const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Position = require('../../../../models/position')


module.exports = (req, res) => {

  let _id = req.body._id || '';

  const listPositions = (next) => {

    let objSearch = {
      _id,
      status: 1
    }

    Position
      .findOne(objSearch)
      .populate('unit','name')
      .populate('permissions', 'name code status')
      .populate('groupPermissions', 'name status')
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err);
        }
        next(null,{
          code: CONSTANTS.CODE.SUCCESS,
          data: result
        })
      })
  }

  async.waterfall([
    listPositions
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
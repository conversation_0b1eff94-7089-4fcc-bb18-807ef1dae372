const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Position = require('../../../../models/position')


module.exports = (req, res) => {

  let name = req.body.name || '';
  const unit = req.body.unit || '';
  const permissions = req.body.permissions || [];
  const groupPermissions = req.body.groupPermissions || [];
  let order = 0;
  let updatedData = {};

  const checkParams = (next) => {
    if(!name || !name.trim() ){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.POSITION.NOT_FOUND_NAME
      })
    }
    if(!unit) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.POSITION.UNIT_NOT_EXISTS
      })
    }
    next();
  }

  const checkPositionExists = (next) => {

    Position
      .find({
        name,
        unit,
        status: 1
      })
      .lean()
      .exec((err, results) => {
        if(err) {
          return next(err);
        }
        if(results.length) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.POSITION.POSITION_EXISTS
          })
        }
        next()
      })

  }

  const getBiggestOrder = (next) => {
    Position
      .findOne({unit, status: 1})
      .sort({order: -1})
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err);
        }
        order = result ? result.order + 1 : req.body.order || 1;
        next();
      })
  }

  const createPosition = (next) => {

    let objCreate = {
      name,
      unit,
      permissions,
      groupPermissions,
      order
    }


    Position
      .create(objCreate,(err, result) => {
        updatedData = result;
        next(err);
      })
  }

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: MESSAGES.POSITION.CREATE_SUCCESS,
      data: updatedData._id
    });

    SystemLogModel.create(
      {
        user: _.get(req,'user.id', ''),
        action: 'create_position',
        description: 'Tạo vị trí',
        data: req.body,
        updatedData,
      },
      () => {}
    );
  };

  async.waterfall([
    checkParams,
    checkPositionExists,
    getBiggestOrder,
    createPosition,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
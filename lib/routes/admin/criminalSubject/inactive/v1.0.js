const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Jo<PERSON>);

const CriminalSubjectModel = require('../../../../models/criminalSubject');
const CriminalSubjectUpdateModel = require('../../../../models/criminalSubjectUpdate');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

/**
 * API xóa (inactive) đối tượng hình sự
 * POST /api/v1.0/admin/criminal-subject/inactive
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const { subjectId } = req.body;

  let data;
  let existingSubject;

  // Validation schema
  const schema = Joi.object({
    subjectId: Joi.objectId().required()
  });

  const validateParams = (next) => {
    const { error } = schema.validate(req.body);
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: error.details[0].message
        }
      });
    }
    next();
  };

  const checkSubjectExists = (next) => {
    CriminalSubjectModel.findOne({
      _id: subjectId,
      status: 1
    }).lean().exec((err, subject) => {
      if (err) {
        return next(err);
      }
      if (!subject) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGES.CRIMINAL_SUBJECT.NOT_FOUND
        });
      }
      existingSubject = subject;
      next();
    });
  };

  const inactiveSubject = (next) => {
    CriminalSubjectModel.updateOne(
      { _id: subjectId },
      {
        $set: {
          status: 0,
          updatedAt: Date.now()
        }
      }
    ).exec((err, result) => {
      if (err) {
        return next(err);
      }
      if (result.modifiedCount === 0) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGES.CRIMINAL_SUBJECT.NOT_FOUND
        });
      }
      next();
    });
  };

  const inactiveRelatedUpdates = (next) => {
    // Cũng inactive tất cả các bản cập nhật liên quan
    CriminalSubjectUpdateModel.updateMany(
      { subjectId: subjectId },
      {
        $set: {
          status: 0
        }
      }
    ).exec((err, result) => {
      if (err) {
        logger && logger.logError(['Error inactivating related updates:', err], req.originalUrl, req.body);
        // Không fail toàn bộ process nếu lỗi này
      }
      next();
    });
  };

  const formatResponse = (next) => {
    data = {
      code: CONSTANTS.CODE.SUCCESS,
      message: MESSAGES.CRIMINAL_SUBJECT.DELETE_SUCCESS,
      data: {
        subjectId: subjectId,
        name: existingSubject.name,
        deletedAt: Date.now()
      }
    };
    next();
  };

  // Execute waterfall
  async.waterfall([
    validateParams,
    checkSubjectExists,
    inactiveSubject,
    inactiveRelatedUpdates,
    formatResponse
  ], (err) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Handle system errors
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};

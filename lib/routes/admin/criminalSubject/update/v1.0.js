const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Jo<PERSON>);

const CriminalSubjectModel = require('../../../../models/criminalSubject');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const { change_alias } = require('../../../../util/tool');

/**
 * API cập nhật thông tin đối tượng hình sự
 * POST /api/v1.0/admin/criminal-subject/update
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const {
    subjectId,
    name,
    photos,
    dob,
    gender,
    idNumber,
    phones,
    permanentAddress,
    temporaryAddress,
    currentResidence,
    category,
    dangerLevel,
    legalStatus,
    description,
    managingUnit,
    assignedOfficers,
    businessNotes
  } = req.body;

  let data;
  let existingSubject;
  let updatedSubject;

  // Validation schema
  const schema = Joi.object({
    subjectId: Joi.objectId().required(),
    name: Joi.string().trim().min(1).max(100),
    photos: Joi.array().items(Joi.string().uri()),
    dob: Joi.string().pattern(/^\d{2}\/\d{2}\/\d{4}$/),
    gender: Joi.string().valid('Male', 'Female'),
    idNumber: Joi.string().trim().min(9).max(20),
    phones: Joi.array().items(Joi.string().trim()),
    permanentAddress: Joi.string().trim().max(500),
    temporaryAddress: Joi.string().trim().max(500),
    currentResidence: Joi.string().trim().max(500),
    category: Joi.string().valid('An ninh', 'Hình sự', 'Ma túy', 'Khác'),
    dangerLevel: Joi.string().valid('ít nghiêm trọng', 'nghiêm trọng', 'đặc biệt nghiêm trọng'),
    legalStatus: Joi.string().trim().max(200),
    description: Joi.string().trim().max(2000),
    managingUnit: Joi.objectId(),
    assignedOfficers: Joi.array().items(Joi.objectId()),
    businessNotes: Joi.string().trim().max(1000)
  });

  const validateParams = (next) => {
    const { error } = schema.validate(req.body);
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: error.details[0].message
        }
      });
    }
    next();
  };

  const checkSubjectExists = (next) => {
    CriminalSubjectModel.findOne({
      _id: subjectId,
      status: 1
    }).lean().exec((err, subject) => {
      if (err) {
        return next(err);
      }
      if (!subject) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGES.CRIMINAL_SUBJECT.NOT_FOUND
        });
      }
      existingSubject = subject;
      next();
    });
  };

  const checkDuplicateIdNumber = (next) => {
    // Chỉ kiểm tra nếu idNumber được cập nhật và khác với giá trị hiện tại
    if (!idNumber || !idNumber.trim() || idNumber.trim() === existingSubject.idNumber) {
      return next();
    }

    CriminalSubjectModel.findOne({
      idNumber: idNumber.trim(),
      _id: { $ne: subjectId },
      status: 1
    }).lean().exec((err, duplicateSubject) => {
      if (err) {
        return next(err);
      }
      if (duplicateSubject) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGES.CRIMINAL_SUBJECT.DUPLICATE_ID_NUMBER
        });
      }
      next();
    });
  };

  const updateSubject = (next) => {
    const updateData = {
      updatedAt: Date.now()
    };

    // Cập nhật các trường nếu có giá trị mới
    if (name && name.trim()) {
      updateData.name = name.trim();
      updateData.nameAlias = change_alias(name.trim());
    }
    if (photos !== undefined) updateData.photos = photos;
    if (dob) updateData.dob = dob;
    if (gender) updateData.gender = gender;
    if (idNumber && idNumber.trim()) updateData.idNumber = idNumber.trim();
    if (phones !== undefined) updateData.phones = phones.filter(p => p && p.trim());
    if (permanentAddress !== undefined) updateData.permanentAddress = permanentAddress ? permanentAddress.trim() : '';
    if (temporaryAddress !== undefined) updateData.temporaryAddress = temporaryAddress ? temporaryAddress.trim() : '';
    if (currentResidence !== undefined) updateData.currentResidence = currentResidence ? currentResidence.trim() : '';
    if (category) updateData.category = category;
    if (dangerLevel) updateData.dangerLevel = dangerLevel;
    if (legalStatus !== undefined) updateData.legalStatus = legalStatus ? legalStatus.trim() : '';
    if (description !== undefined) updateData.description = description ? description.trim() : '';
    if (managingUnit !== undefined) updateData.managingUnit = managingUnit;
    if (assignedOfficers !== undefined) updateData.assignedOfficers = assignedOfficers;
    if (businessNotes !== undefined) updateData.businessNotes = businessNotes ? businessNotes.trim() : '';

    CriminalSubjectModel.findOneAndUpdate(
      { _id: subjectId, status: 1 },
      { $set: updateData },
      { new: true }
    ).exec((err, result) => {
      if (err) {
        return next(err);
      }
      if (!result) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGES.CRIMINAL_SUBJECT.NOT_FOUND
        });
      }
      updatedSubject = result;
      next();
    });
  };

  const populateSubject = (next) => {
    CriminalSubjectModel.findById(updatedSubject._id)
      .populate('managingUnit', 'name')
      .populate('assignedOfficers', 'name idNumber avatar')
      .populate('createdBy', 'name idNumber')
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }
        data = {
          code: CONSTANTS.CODE.SUCCESS,
          message: MESSAGES.CRIMINAL_SUBJECT.UPDATE_SUCCESS,
          data: result
        };
        next();
      });
  };

  // Execute waterfall
  async.waterfall([
    validateParams,
    checkSubjectExists,
    checkDuplicateIdNumber,
    updateSubject,
    populateSubject
  ], (err) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Handle system errors
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};

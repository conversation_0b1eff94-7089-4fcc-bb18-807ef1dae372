const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Jo<PERSON>);

const CriminalSubjectModel = require('../../../../models/criminalSubject');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const { change_alias } = require('../../../../util/tool');

/**
 * API tạo đối tượng hình sự mới
 * POST /api/v1.0/admin/criminal-subject/create
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const {
    fullName,
    photos = [],
    dob,
    gender,
    idNumber,
    phones = [],
    permanentAddress,
    temporaryAddress,
    currentResidence,
    category,
    dangerLevel,
    legalStatus,
    description,
    managingUnit,
    assignedOfficers = [],
    businessNotes
  } = req.body;

  let data;
  let newSubject;

  // Validation schema
  const schema = Joi.object({
    fullName: Joi.string().required().trim().min(1).max(100),
    photos: Joi.array().items(Joi.string().uri()),
    dob: Joi.string().pattern(/^\d{2}\/\d{2}\/\d{4}$/),
    gender: Joi.string().valid('Male', 'Female'),
    idNumber: Joi.string().trim().min(9).max(20),
    phones: Joi.array().items(Joi.string().trim()),
    permanentAddress: Joi.string().trim().max(500),
    temporaryAddress: Joi.string().trim().max(500),
    currentResidence: Joi.string().trim().max(500),
    category: Joi.string().required().valid('An ninh', 'Hình sự', 'Ma túy', 'Khác'),
    dangerLevel: Joi.string().required().valid('ít nghiêm trọng', 'nghiêm trọng', 'đặc biệt nghiêm trọng'),
    legalStatus: Joi.string().trim().max(200),
    description: Joi.string().trim().max(2000),
    managingUnit: Joi.objectId(),
    assignedOfficers: Joi.array().items(Joi.objectId()),
    businessNotes: Joi.string().trim().max(1000)
  });

  const validateParams = (next) => {
    const { error } = schema.validate(req.body);
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: error.details[0].message
        }
      });
    }
    next();
  };

  const checkDuplicateIdNumber = (next) => {
    if (!idNumber || !idNumber.trim()) {
      return next();
    }

    CriminalSubjectModel.findOne({
      idNumber: idNumber.trim(),
      status: 1
    }).lean().exec((err, existingSubject) => {
      if (err) {
        return next(err);
      }
      if (existingSubject) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGES.CRIMINAL_SUBJECT.DUPLICATE_ID_NUMBER
        });
      }
      next();
    });
  };

  const createSubject = (next) => {
    const subjectData = {
      fullName: fullName.trim(),
      nameAlias: change_alias(fullName.trim()),
      category,
      dangerLevel,
      createdBy: userId,
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    // Optional fields
    if (photos && photos.length > 0) subjectData.photos = photos;
    if (dob) subjectData.dob = dob;
    if (gender) subjectData.gender = gender;
    if (idNumber && idNumber.trim()) subjectData.idNumber = idNumber.trim();
    if (phones && phones.length > 0) subjectData.phones = phones.filter(p => p && p.trim());
    if (permanentAddress) subjectData.permanentAddress = permanentAddress.trim();
    if (temporaryAddress) subjectData.temporaryAddress = temporaryAddress.trim();
    if (currentResidence) subjectData.currentResidence = currentResidence.trim();
    if (legalStatus) subjectData.legalStatus = legalStatus.trim();
    if (description) subjectData.description = description.trim();
    if (managingUnit) subjectData.managingUnit = managingUnit;
    if (assignedOfficers && assignedOfficers.length > 0) subjectData.assignedOfficers = assignedOfficers;
    if (businessNotes) subjectData.businessNotes = businessNotes.trim();

    const subject = new CriminalSubjectModel(subjectData);
    subject.save((err, result) => {
      if (err) {
        return next(err);
      }
      newSubject = result;
      next();
    });
  };

  const populateSubject = (next) => {
    CriminalSubjectModel.findById(newSubject._id)
      .populate('managingUnit', 'name')
      .populate('assignedOfficers', 'name idNumber')
      .populate('createdBy', 'name idNumber')
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }
        data = {
          code: CONSTANTS.CODE.SUCCESS,
          message: MESSAGES.CRIMINAL_SUBJECT.CREATE_SUCCESS,
          data: result
        };
        next();
      });
  };

  // Execute waterfall
  async.waterfall([
    validateParams,
    checkDuplicateIdNumber,
    createSubject,
    populateSubject
  ], (err) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Handle system errors
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};

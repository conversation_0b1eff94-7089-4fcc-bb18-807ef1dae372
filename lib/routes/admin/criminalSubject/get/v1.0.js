const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Jo<PERSON>);

const CriminalSubjectModel = require('../../../../models/criminalSubject');
const CriminalSubjectUpdateModel = require('../../../../models/criminalSubjectUpdate');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

/**
 * API lấy thông tin chi tiết đối tượng hình sự
 * POST /api/v1.0/admin/criminal-subject/get
 */
module.exports = (req, res) => {
  const { subjectId } = req.body;

  let data;
  let subjectInfo;
  let updateHistory = [];

  // Validation schema
  const schema = Joi.object({
    subjectId: Joi.objectId().required()
  });

  const validateParams = (next) => {
    const { error } = schema.validate(req.body);
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: error.details[0].message
        }
      });
    }
    next();
  };

  const getSubjectInfo = (next) => {
    CriminalSubjectModel.findOne({
      _id: subjectId,
      status: 1
    })
    .populate('managingUnit', 'name')
    .populate('assignedOfficers', 'name idNumber avatar')
    .populate('createdBy', 'name idNumber')
    .lean()
    .exec((err, result) => {
      if (err) {
        return next(err);
      }
      if (!result) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGES.CRIMINAL_SUBJECT.NOT_FOUND
        });
      }
      subjectInfo = result;
      next();
    });
  };

  const getUpdateHistory = (next) => {
    CriminalSubjectUpdateModel.find({
      subjectId: subjectId,
      status: 1
    })
    .populate('updatedBy', 'name idNumber')
    .sort({ createdAt: -1 })
    .limit(50) // Giới hạn 50 bản cập nhật gần nhất
    .lean()
    .exec((err, results) => {
      if (err) {
        return next(err);
      }
      updateHistory = results;
      next();
    });
  };

  const formatResponse = (next) => {
    data = {
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        subject: subjectInfo,
        updateHistory: updateHistory,
        totalUpdates: updateHistory.length
      }
    };
    next();
  };

  // Execute waterfall
  async.waterfall([
    validateParams,
    getSubjectInfo,
    getUpdateHistory,
    formatResponse
  ], (err) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Handle system errors
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};

const _ = require("lodash");
const async = require("async");
const Joi = require("joi");
Joi.objectId = require("joi-objectid")(Jo<PERSON>);

const User = require("../../../../models/user");
const DutyShiftModel = require("../../../../models/dutyShift");
const CONSTANTS = require("../../../../const");
const MESSAGES = require("../../../../message");

// Import utility function để xử lý cấu trúc phân cấp areas
const { processAreasHierarchy } = require('../../../../util/areasHierarchy');
const { change_alias } = require('../../../../util/tool');

module.exports = (req, res) => {
  const limit = _.get(req, "body.limit", 10);
  const page = _.get(req, "body.page", 0);
  const sort = _.get(req, "body.sort", 1);
  const isFilter = _.get(req, "body.isFilter");
  const textSearch = _.get(req, "body.textSearch", "");
  const active = _.get(req, "body.active", "");
  const unit = _.get(req, "body.unit", "");
  const positions = _.get(req, "body.positions", []);
  const gender = _.get(req, "body.gender", "");
  const excludes = _.get(req, "body.excludes", []);
  const isLeader = _.get(req, "body.isLeader", false);
  const includeJobStatus = _.get(req, 'body.includeJobStatus', false);
  const onlySelectCombatDutyShift = _.get(req, 'body.onlySelectCombatDutyShift', false);

  const startTime = _.get(req, 'body.startTime', null);
  const endTime = _.get(req, 'body.endTime', null);

  let obj = { status: 1 };
  let count = 0;
  let data = [];
  const checkParams = (next) => {
    if (textSearch && textSearch.trim()) {
      const $regex = change_alias(textSearch.trim());
      obj["$or"] = [
        {
          nameAlias: {
            $regex,
            $options: "i",
          },
        },
        {
          idNumber: {
            $regex,
            $options: "i",
          },
        },
        {
          phones: {
            $regex,
            $options: "i",
          },
        },
        {
          code: {
            $regex,
            $options: "i",
          },
        },
      ];
    }

    if (isFilter) {
      if (_.isNumber(active)) {
        obj.active = active;
      }
      if (unit) {
        obj.units = unit;
      }
      if (positions.length) {
        obj.positions = {
          $in: positions
        }
      }
      if (gender) {
        obj.gender = gender;
      }
    }

    if (excludes.length) {
      obj._id = { $nin: excludes };
    }
    if (isLeader) {
      obj.units = {
        $size: 1, // Chỉ lấy những user có đúng 1 đơn vị
      };
    }

    if (includeJobStatus) {
      if (!startTime) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: "Thiếu tham số Thời gian bắt đầu"
          }
        });
      }

      if (!endTime) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: "Thiếu tham số Thời gian kết thúc"
          }
        });
      }

      // Validate endTime is after startTime
      if (endTime <= startTime ) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: "Thời gian kết thúc phải sau Thời gian bắt đầu"
          }
        });
      }
      if(endTime - startTime > 2*24*60*60*1000) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: "Khoảng thời gian không được vượt quá 2 ngày"
          }
        });
      }
    }

    next();
  };

  const findUserInCombatDutyShift = (next) => {
    if (!onlySelectCombatDutyShift) {
      return next();
    }

    // Tìm tất cả các user có ca trực trong khoảng thời gian đã cho
    DutyShiftModel.distinct('officer', {
      startTime: {
        $lt: endTime
      },
      endTime: {
        $gt: startTime
      },
      source: {
        $in: ['main','sub']
      },
      status: 1
    })
    .lean()
    .exec((err, userIds) => {
      if (err) {
        return next(err);
      }

      obj._id = { $in: userIds };
      next();
    });
  }

  const countUser = (next) => {
    User.countDocuments(obj)
      .lean()
      .exec((err, total) => {
        count = Math.ceil(total / limit);
        next();
      });
  };

  const listUser = (next) => {
    const skip = page * limit;
    const options = {
      limit,
      skip,
      sort: sort == 1 ? "createdAt" : "-createdAt",
    };
    User.find(obj, "-password", options)
      .populate("permissions", "name code")
      .populate({
        path: "units",
        select: "name parentPath",
        populate: {
          path: "parentPath",
          select: "name icon",
        },
      })
      .populate("positions", "name unit special")
      .populate("categories", "name icon")
      .populate("jobTypes", "name")
      .populate({
        path: "areas",
        select: "name level parent parentPath",
        populate: {
          path: "parent",
          select: "name",
        },
      })
      .populate({
        path: "groupPermissions",
        select: "name permissions",
        populate: {
          path: "permissions",
          select: "name",
        },
      })
      .populate('managedUnits', 'name icon')
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        // Xử lý cấu trúc phân cấp areas cho từng user
        if (results && Array.isArray(results)) {
          results.forEach(user => {
            if (user.areas && user.areas.length > 0) {
              user.areas = processAreasHierarchy(user.areas);
            }
          });
        }
        data = results;
        next(null);
      });
  };

  const listShifts = (next) => {
    if (!includeJobStatus) {
      return next();
    }

    // Query duty shifts cho từng user
    async.eachSeries(data, (user, callback) => {
      const shiftQuery = {
        startTime: {
          $gte: startTime,
          $lt: endTime
        },
        status: 1,
        officer: user._id
      };

      DutyShiftModel.find(shiftQuery)
        .select('name startTime endTime')
        .lean()
        .exec((err, shifts) => {
          if (err) {
            return callback(err);
          }

          // Thêm dutyShifts vào user object
          user.dutyShifts = shifts || [];
          user.workStatus = user.dutyShifts.length > 0 ? 'busy' : 'free';
          callback();
        });
    }, (err) => {
      if (err) {
        return next(err);
      }
      next();
    });
  }

  const handleDataReturn = (next) => {
    return next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data,
      count
    });
  }

  async.waterfall([checkParams, findUserInCombatDutyShift, countUser, listUser, listShifts, handleDataReturn], (err, data) => {
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};

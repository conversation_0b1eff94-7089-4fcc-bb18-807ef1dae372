const _ = require('lodash');
const async = require('async');
const ms = require('ms');
const { v4: uuidv4 } = require('uuid');
const config = require('config');
const util = require('util');
const rp = require('request-promise');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);

const User = require('../../../../models/user');
const PositionModel = require('../../../../models/position');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const tool = require('../../../../util/tool');
const MailUtil = require('../../../../util/mail');
const validator = require('validator');
const redisConnection = require('../../../../connections/redis');

const schemaInput = Joi.object({
  units: Joi.array(),
  positions: Joi.array(),
  username: Joi.string().required(),
  name: Joi.string().required(),
  phone: Joi.string().required(),
  email: Joi.string().required(),
  gender: Joi.string().required(),
});

module.exports = (req, res) => {
  const _id = req.body._id || '';
  let permissions = _.get(req, 'body.permissions', '');
  let groupPermissions = _.get(req, 'body.groupPermissions', '');
  let permissionInGroup = [];
  let updatedData = {};
  let objUpdate = {};

  const checkParams = (next) => {
    if (!_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }

    if (!permissions) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }

    if (!groupPermissions) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }

    next(null);
  };

  const findPermissionsInGroup = (next) => {
    GroupPermissionModel.find({
      _id: {
        $in: groupPermissions,
      },
    })
      .populate('permissions', 'code -_id')
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }
        results.map((groupPermission) => {
          permissionInGroup = permissionInGroup.concat(groupPermission.permissions);
        });
        next();
      });
  };

  const updateUser = (next) => {
    objUpdate = {
      permissions: permissions,
      groupPermissions: groupPermissions,
      updatedAt: Date.now(),
    };

    User.findOneAndUpdate(
      {
        _id,
      },
      objUpdate,
      { new: true }
    )
      .populate('permissions', 'code _id')
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        updatedData = {
          ...result,
          permissions: result.permissions.map((permission) => permission._id),
        };
        result.permissions.forEach((permission) => {
          delete permission._id;
        });

        redisConnection('master')
          .getConnection()
          .get(`user:${_id}`, (err, token) => {
            if (token) {
              const obj = {
                id: _id,
                permissions: [...result.permissions, ...permissionInGroup],
                groupPermissions,
              };
              redisConnection('master')
                .getConnection()
                .multi()
                .set(`user:${token}`, JSON.stringify(obj))
                .exec((err, result) => {});
            }
          });

        next(null);
      });
  };

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
    });

    SystemLogModel.create(
      {
        user: _.get(req, 'user.id', ''),
        action: 'grant_permission',
        description: 'Cấp quyền cho người dùng',
        data: objUpdate,
        updatedData,
      },
      () => {}
    );
  };

  async.waterfall([checkParams, findPermissionsInGroup, updateUser, writeLog], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};

const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');
const Joi = require('joi')
Joi.objectId = require('joi-objectid')(Joi);
const bcrypt = require('bcryptjs')
const User = require('../../../../models/user')
const PositionModel = require('../../../../models/position')
const RankModel = require('../../../../models/rank')
const StatisticsTrigger = require('../../../../utils/statisticsTrigger')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const tool = require('../../../../util/tool')
const MailUtil = require('../../../../util/mail');
const validator = require('validator');
const UnitModel = require('../../../../models/unit');


module.exports = (req, res) => {

  const {username, name, phones, email, gender, avatar, dob, apps, address, rank, educationLevel, politicalTheoryLevel, idNumber } = req.body || ''
  let {units, areas, jobTypes, permissions, groupPermissions, positions, managedUnits} = req.body || []
  const password = tool.generatePassword(10)
  let passwordHash;

  let newUser;
  let rankImage;

  const checkParams = (next) => {
    if(!username || (username && !username.trim())) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.NOT_FOUND_USERNAME
      })
    }
    if(!email || (email && !email.trim())) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.NOT_FOUND_EMAIL
      })
    }
    if(!idNumber || (idNumber && !idNumber.trim())) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.NOT_FOUND_IDNUMBER
      })
    }
    if(!/^\d{6}$/.test(idNumber.trim())) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: 'Số hiệu phải là 6 chữ số'
      })
    }

    if(!validator.isEmail(email)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.EMAIL_NOT_VALID
      })
    }
    if(!name || (name && !name.trim())) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.NOT_FOUND_NAME
      })
    }
    if(!gender){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.NOT_FOUND_GENDER
      })
    }
    if(!['male', 'female'].includes(gender)){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.NOT_FOUND_GENDER
      })
    }
    if(!phones || !phones.length) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.NOT_FOUND_PHONE
      })
    }
    if(!phones.every(phone => validator.isMobilePhone(phone, ['vi-VN']))) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.PHONE_NOT_VALID
      })
    }
    if (!apps || apps && !apps.length) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.NOT_FOUND_APPS,
      });
    }
    if(!['cms','ioc'].includes(...apps)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.APPS_NOT_VALID
      })
    }
    if(!units || !units.length){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.NOT_FOUND_UNIT
      })
    }
    // Kiểm tra nếu có managedUnits thì phải có position với special=true
    if (managedUnits && managedUnits.length) {
      if (!positions || !positions.length) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: 'Nếu có tổ phụ trách thì phải chức vụ phải là chức vụ của lãnh đạo'
        });
      }
      // Kiểm tra trong danh sách positions có ít nhất một position với special=true
      PositionModel.find({
        _id: { $in: positions },
        special: true
      }).lean().exec((err, results) => {
        if (err) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          });
        }
        if (!results || !results.length) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Nếu có tổ phụ trách thì phải chức vụ phải là chức vụ của lãnh đạo'
            }
          });
        }
        UnitModel.find({ _id: { $in: managedUnits } }).lean().exec((err2, unitsResult) => {
          if (err2) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: MESSAGES.SYSTEM.ERROR
            });
          }
          // Kiểm tra tổ phụ trách có tồn tại không
          if (!unitsResult || unitsResult.length !== managedUnits.length) {
            return next({
              code: CONSTANTS.CODE.WRONG_PARAMS,
              message: {
                head: 'Thông báo',
                body: 'Tổ phụ trách không tồn tại hoặc có tổ không hợp lệ'
              }
            });
          }
          const invalidUnits = unitsResult.filter(u => !u.parent);
          if (invalidUnits.length) {
            return next({
              code: CONSTANTS.CODE.WRONG_PARAMS,
              message: {
                head: 'Thông báo',
                body: 'Tổ phụ trách chỉ được chọn các Tổ công tác, không được chọn các đơn vị cấp trên'
              }
            });
          }
          next(null);
        });
      });
      return;
    }
    next(null);
  }

  const checkUserExists = (next) => {

    User
      .find({
        $or:[
          {
            username
          },
          {
            email
          },
          {
            phones: {
              $in: phones
            }
          },
          {
            idNumber
          }
        ],
        status: 1
      })
      .lean()
      .exec((err, results) => {
        if(err) {
          return next(err)
        }
        if(results.length) {
          // Kiểm tra cụ thể trường nào bị trùng để trả về message phù hợp
          const existingUser = results[0];
          let errorMessage = MESSAGES.USER.EXISTS;

          if(existingUser.username === username) {
            errorMessage = {
              head: 'Thông báo',
              body: 'Tên đăng nhập đã tồn tại'
            };
          } else if(existingUser.email === email) {
            errorMessage = {
              head: 'Thông báo',
              body: 'Email cán bộ đã tồn tại'
            };
          } else if(existingUser.phones.some(phone => phones.includes(phone))) {
            errorMessage = {
              head: 'Thông báo',
              body: 'Số điện thoại cán bộ đã tồn tại'
            };
          } else if(existingUser.idNumber === idNumber) {
            errorMessage = {
              head: 'Thông báo',
              body: 'Số hiệu cán bộ đã tồn tại'
            };
          }
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: errorMessage
          })
        }
        next()
      })

  }

  const findRankImage = (next) => {
    if (!rank) {
      return next();
    }

    RankModel
      .findOne({ name: rank })
      .select('image')
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        if(result && result.image) {
          rankImage = result.image;
        }
        next();
      })
  }

  const findPermissions = (next) => {
    PositionModel
      .find({
        _id: {
          $in: positions
        }
      })
      .select('permissions groupPermissions')
      .lean()
      .exec((err, results) => {
        if(err) {
          return next(err)
        }
        results.forEach(position => {
          permissions = _.union(permissions, position.permissions)
          groupPermissions = _.union(groupPermissions, position.groupPermissions)
        })
        next();
      })
  }

  const setDefaultAvatar = (next) => {
    // Nếu không có avatar, thiết lập avatar mặc định dựa trên giới tính
    if (!avatar || (avatar && !avatar.trim())) {
      if (gender === 'female') {
        req.body.avatar = 'https://media-hb-dev.canbo.ai/uploads/avatar/2025-08-22-image2.png';
      } else if (gender === 'male') {
        req.body.avatar = 'https://media-hb-dev.canbo.ai/uploads/avatar/2025-08-22-image12.png';
      }
    }
    next();
  }

  const checkAndEncryptPassword = (next) => {
    bcrypt.hash(password, 10, function(err, hash) {
        if(err) {
          return next(err);
        }
        passwordHash = hash;
        next();
      });
  }

  const createUser = (next) => {
    User
      .create({
        username,
        name: name.trim(),
        nameAlias: tool.change_alias(name.trim()), // Tự động tạo nameAlias từ name
        idNumber,
        dob,
        gender,
        address,
        email,
        phones,
        avatar: req.body.avatar || avatar,
        rank,
        rankImage,
        units,
        areas, // thêm trường areas để lưu danh sách khu vực địa bàn mà cán bộ quản lý
        jobTypes,
        positions,
        educationLevel,
        politicalTheoryLevel,
        permissions,
        groupPermissions,
        apps,
        managedUnits,
        password: passwordHash
      },(err, result) => {
        if(err) {
          return next(err)
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          })
        }
        newUser = result

        MailUtil.sendEMail({
          subject: `[IOC Hồng Bàng] - Thông tin tài khoản đăng nhập`,
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: auto; padding: 20px; border: 1px solid #ddd; border-radius: 10px; background-color: #f9f9f9;">
              <h4 style="color: #2c3e50; text-align: center;">Chào mừng bạn đến với IOC Hồng Bàng</h4>
              <p style="font-size: 16px; color: #333;">Xin chào <strong>${name}</strong>,</p>
              <p style="font-size: 16px; color: #333;">Tài khoản của bạn đã được tạo thành công!.</p>

              <div style="background-color: #ffffff; padding: 15px; border-radius: 8px; box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);">
                <p style="font-size: 16px; margin: 5px 0;"><strong>Tên đăng nhập:</strong> ${username}</p>
                <p style="font-size: 16px; margin: 5px 0;"><strong>Mật khẩu:</strong> ${password}</p>
              </div>

              <p style="font-size: 16px; color: #333; margin-top: 15px;">Bạn có thể đăng nhập tại:</p>

              ${apps.includes('ioc') ? `
                <p style="margin: 5px 0;">
                  <strong>Hệ thống IOC:</strong>
                  <a href="${config.iocUrl}" style="color: #3498db; text-decoration: none; font-weight: bold;">
                    ${config.iocUrl}
                  </a>
                </p>` : ''}

              <p style="font-size: 16px; color: #e74c3c;"><strong>Vui lòng thay đổi mật khẩu sau khi đăng nhập.</strong></p>

              <hr style="border: 0; border-top: 1px solid #ddd; margin: 20px 0;">

              <p style="text-align: center; font-size: 14px; color: #777;">
                Trân trọng,<br>
                <strong>IOC Hồng Bàng</strong>
              </p>
            </div>
          `
        }, email);

        next();
      })
  }

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: newUser._id,
      message: MESSAGES.USER.CREATE_SUCCESS
    });

    // Trigger statistics update khi tạo user mới
    try {
      StatisticsTrigger.triggerUserUpdate('create', {
        _id: newUser._id,
        status: newUser.status,
        units: newUser.units,
        areas: newUser.areas,
        positions: newUser.positions
      });
    } catch (error) {
      console.error('Error triggering user update:', error);
    }

    SystemLogModel.create(
      {
        user: _.get(req,'user.id', ''),
        action: 'create_user',
        description: 'Tạo người dùng mới',
        data: req.body,
        updatedData: newUser,
      },
      () => {}
    );
  };

  async.waterfall([
    checkParams,
    checkUserExists,
    findRankImage,
    findPermissions,
    setDefaultAvatar,
    checkAndEncryptPassword,
    createUser,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
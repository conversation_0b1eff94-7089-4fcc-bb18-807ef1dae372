const _ = require('lodash');
const async = require('async');
const ms = require('ms');
const config = require('config');
const util = require('util');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Model = require('../../../../models/user'); //<PERSON> tên cho dễ nhân bản
const tool = require('../../../../util/tool');

module.exports = (req, res) => {
  const userId = _.get(req, 'body.userId', '');
  const block = _.get(req, 'body.block', 0);
  let query = {};
  let count = 0;

  const checkParams = (next) => {
    if (!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS,
      });
    }

    next();
  };

  const toggleBlock = (next) => {
    query = {
      _id: userId,
      block: { $ne: block }, // Only toggle if current block status is different
    };
    Model.findOneAndUpdate(query, { $set: { block } }, { new: true })
      .lean()
      .exec((err, result) => {
        if (err || !result) {
          return next(
            err || {
              code: CONSTANTS.CODE.NOT_FOUND,
              message: {
                head: 'Thông báo',
                body: 'Không tìm thấy tài khoản hoặc thái khóa/mở khóa đã thay đổi',
              },
            }
          );
        }
        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          message: `Thành công ${block ? 'khóa' : 'mở khóa'} tài khoản`,
        });
      });
  };

  async.waterfall([checkParams, toggleBlock], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
      });

    res.json(data || err);
  });
};

const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const GroupPermission = require('../../../../models/groupPermission')


module.exports = (req, res) => {

  let name = req.body.name || '';
  const description = req.body.description || '';
  const permissions = req.body.permissions || [];
  let updatedData = {};
  const checkParams = (next) => {
    if(!name || !name.trim() ){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.GROUP_PERMISSION.NOT_FOUND_NAME
      })
    }
    next();
  }

  const checkGroupPermissionExists = (next) => {

    GroupPermission
      .find({
        $or:[
          {name}
        ]
      })
      .lean()
      .exec((err, results) => {
        if(err) {
          return next(err);
        }
        if(results.length) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.GROUP_PERMISSION.GROUP_PERMISSION_EXISTS
          })
        }
        next()
      })

  }

  const createGroupPermission = (next) => {

    let objCreate = {
      name,
      description,
      permissions
    }


    GroupPermission
      .create(objCreate,(err, result) => {
        updatedData = result
        next(err)
      })
  }

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: MESSAGES.GROUP_PERMISSION.CREATE_SUCCESS,
      data: updatedData._id
    })

    SystemLogModel
      .create({
        user: _.get(req,'user.id', ''),
        action: 'create_group_permission',
        description: 'Tạo nhóm quyền',
        data: req.body,
        updatedData
      },() =>{})
   }

  async.waterfall([
    checkParams,
    checkGroupPermissionExists,
    createGroupPermission,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
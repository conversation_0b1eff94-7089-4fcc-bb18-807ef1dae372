
const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const GroupPermission = require('../../../../models/groupPermission')


module.exports = (req, res) => {

  let name = req.body.name || '';
  const idGroupPermission = req.body._id || '';
  const description = req.body.description || '';
  const permissions = req.body.permissions || [];
  let groupPermissionInf;
  let updatedData = {};
  const checkParams = (next) => {
    if(!idGroupPermission){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    if(!name){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.GROUP_PERMISSION.NOT_FOUND_NAME
      })
    }
    next();
  }

  const checkGroupPermissionExists = (next) => {

    GroupPermission
      .findById(idGroupPermission)
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err);
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.GROUP_PERMISSION.GROUP_PERMISSION_NOT_EXISTS
          })
        }
        groupPermissionInf = result
        next()
      })

  }

  const checkNameNotExists = (next) => {

    GroupPermission
      .findOne({
        $or:[
          {
            name: name.trim()
          }
        ],
        _id: {
          $ne: idGroupPermission
        },
        status: 1
      })
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        if(result) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.GROUP_PERMISSION.GROUP_PERMISSION_EXISTS
          })
        }
        next()
      })

  }

  const createGroupPermission = (next) => {

    let obj = {
      updatedAt: Date.now(),
      name : name.trim(),
      description: description.trim(),
      permissions
    }

    GroupPermission
      .findOneAndUpdate({
        _id: idGroupPermission
      },obj,{new: true})
      .lean()
      .exec((err, result) => {
        updatedData = result
        next(err)
      })
  }

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: MESSAGES.GROUP_PERMISSION.UPDATE_SUCCESS,
    })

    SystemLogModel
      .create({
        user: _.get(req,'user.id', ''),
        action: 'update_group_permission',
        description: `Cập nhật nhóm quyền`,
        data: req.body,
        updatedData
      },() =>{})
   }

  async.waterfall([
    checkParams,
    checkGroupPermissionExists,
    checkNameNotExists,
    createGroupPermission,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}

const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const GroupPermission = require('../../../../models/groupPermission')


module.exports = (req, res) => {
  const idGroupPermission = req.body._id || '';
  let groupPermissionInf;
  let updatedData = {};
  const checkParams = (next) => {
    if(!idGroupPermission){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    next();
  }


  const deactiveGroupPermission = (next) => {

    GroupPermission
      .findOneAndUpdate({
        _id: idGroupPermission
      },{
        status: 0
      },(err, result) => {
        if(err) {
          return next(err);
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.GROUP_PERMISSION.GROUP_PERMISSION_NOT_EXISTS
          })
        }
        updatedData = result
        next()
      })
  }

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS
    })

    SystemLogModel
      .create({
        user: _.get(req,'user.id', ''),
        action: 'inactive_group_permission',
        description: `Xóa nhóm quyền`,
        data: req.body,
        updatedData
      },() =>{})
   }

  async.waterfall([
    checkParams,
    deactiveGroupPermission,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
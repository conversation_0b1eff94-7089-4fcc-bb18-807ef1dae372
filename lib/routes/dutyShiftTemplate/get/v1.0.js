const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const DutyShiftTemplateModel = require('../../../models/dutyShiftTemplate');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  const { source } = req.body;

  const checkParams = (next) => {
    if (!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    // Kiểm tra source có được cung cấp không
    if (!source) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp source để tìm kiếm template'
        }
      });
    }

    next();
  };

  const getDutyShiftTemplate = (next) => {
    const query = {
      source: source,
      status: 1 // Chỉ lấy template đang active
    };

    DutyShiftTemplateModel.findOne(query)
      .populate('createdBy', 'name email') // Populate thông tin người tạo nếu cần
      .exec((err, template) => {
        if (err) {
          return next(err);
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: template
        });
      });
  };

  async.waterfall([checkParams, getDutyShiftTemplate], (err, data) => {
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};

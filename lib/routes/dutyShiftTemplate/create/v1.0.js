const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const DutyShiftTemplateModel = require('../../../models/dutyShiftTemplate');
const UnitModel = require('../../../models/unit');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  const { template, source, location } = req.body;
  const checkParams = (next) => {
    if (!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    // Kiểm tra template có tồn tại và hợp lệ
    if (!template || !Array.isArray(template) || template.length === 0) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: '<PERSON>ui lòng cung cấp template ca trực hợp lệ'
        }
      });
    }

    // Validate dữ liệu template
    const validateTemplate = (templateData) => {
      return templateData.every(day => {
        // Kiểm tra tên ngày
        if (!day.name || typeof day.name !== 'string') {
          return false;
        }
        
        // Kiểm tra dữ liệu ca trực
        if (!Array.isArray(day.data)) {
          return false;
        }
        
        return day.data.every(shift => {
          // Validate unit ID (chỉ kiểm tra khi có unit)
          if (shift.unit && (typeof shift.unit !== 'string' || !shift.unit.match(/^[0-9a-fA-F]{24}$/))) {
            return false;
          }
          
          return typeof shift.startTime === 'number' && 
                 typeof shift.endTime === 'number' &&
                 shift.startTime <= 86400000 &&
                 shift.endTime <= 86400000 &&
                 shift.startTime < shift.endTime && // Thời gian bắt đầu phải nhỏ hơn thời gian kết thúc
                 (shift.forLeader === undefined || typeof shift.forLeader === 'boolean');
        });
      });
    };

    if (!validateTemplate(template)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Dữ liệu template ca trực không hợp lệ'
        }
      });
    }

    next();
  };

  const validateUnits = (next) => {
    // Lấy tất cả unit IDs từ template
    const unitIds = [];
    template.forEach(day => {
      day.data.forEach(shift => {
        if (shift.unit && !unitIds.includes(shift.unit)) {
          unitIds.push(shift.unit);
        }
      });
    });

    if (unitIds.length === 0) {
      return next();
    }

    // Kiểm tra tất cả unit IDs có tồn tại trong database không
    UnitModel.find({
      _id: { $in: unitIds },
      status: 1 // Chỉ lấy unit đang active
    }, (err, units) => {
      if (err) {
        return next(err);
      }

      const foundUnitIds = units.map(unit => unit._id.toString());
      const missingUnits = unitIds.filter(unitId => !foundUnitIds.includes(unitId));

      if (missingUnits.length > 0) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: `Không tìm thấy hoặc unit không hoạt động với ID: ${missingUnits.join(', ')}`
          }
        });
      }

      next();
    });
  };

  const createDutyShiftTemplate = (next) => {
    const templateData = {
      template: template,
      createdBy: userId,
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    if (source) templateData.source = source;
    if (location) templateData.location = location;
    const newTemplate = new DutyShiftTemplateModel(templateData);
    
    newTemplate.save((err, template) => {
      if (err) {
        return next(err);
      }

      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        message: {
          head: 'Thông báo',
          body: 'Tạo mẫu ca trực thành công'
        },
        data: template
      });
    });
  };

  async.waterfall([checkParams, validateUnits, createDutyShiftTemplate], (err, data) => {
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};

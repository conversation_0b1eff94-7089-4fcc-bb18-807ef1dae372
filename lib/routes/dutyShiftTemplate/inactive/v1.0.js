const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const DutyShiftTemplateModel = require('../../../models/dutyShiftTemplate');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  const { _id } = req.body;

  const checkParams = (next) => {
    if (!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    if (!_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp ID mẫu ca trực'
        }
      });
    }

    next();
  };

  const inactiveDutyShiftTemplate = (next) => {
    const updateData = {
      status: 0,
      updatedAt: Date.now()
    };

    DutyShiftTemplateModel.findOneAndUpdate(
      { _id: _id, status: 1 },
      { $set: updateData },
      { new: true }
    )
    .lean()
    .exec((err, template) => {
      if (err) {
        return next(err);
      }

      if (!template) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Mẫu ca trực không tồn tại hoặc đã bị xóa'
          }
        });
      }

      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        message: {
          head: 'Thông báo',
          body: 'Xóa mẫu ca trực thành công'
        },
        data: template
      });
    });
  };

  async.waterfall([checkParams, inactiveDutyShiftTemplate], (err, data) => {
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};

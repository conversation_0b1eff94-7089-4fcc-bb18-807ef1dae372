const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const DutyShiftTemplateModel = require('../../../models/dutyShiftTemplate');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  const { _id, template, source } = req.body;

  const checkParams = (next) => {
    if (!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    if (!_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp ID mẫu ca trực'
        }
      });
    }

    // Validate dữ liệu template nếu có
    if (template) {
      if (!Array.isArray(template)) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Template phải là một array'
          }
        });
      }

      const validateTemplate = (templateData) => {
        return templateData.every(day => {
          // Kiểm tra tên ngày
          if (!day.name || typeof day.name !== 'string') {
            return false;
          }
          
          // Kiểm tra dữ liệu ca trực
          if (!Array.isArray(day.data)) {
            return false;
          }
          
          return day.data.every(shift => {
            return typeof shift.startTime === 'number' && 
                   typeof shift.endTime === 'number' &&
                   shift.startTime >= 0 && shift.startTime <= 86400000 &&
                   shift.endTime >= 0 && shift.endTime <= 86400000 &&
                   shift.startTime < shift.endTime &&
                   (shift.forLeader === undefined || typeof shift.forLeader === 'boolean');
          });
        });
      };

      if (!validateTemplate(template)) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Dữ liệu template ca trực không hợp lệ'
          }
        });
      }
    }

    next();
  };

  const updateDutyShiftTemplate = (next) => {
    const updateData = {
      updatedAt: Date.now()
    };

    // Cập nhật các trường nếu có
    if (template !== undefined) updateData.template = template;
    if (source !== undefined) updateData.source = source;

    // Kiểm tra có dữ liệu để cập nhật không (ngoài updatedAt)
    if (Object.keys(updateData).length === 1) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Không có dữ liệu để cập nhật'
        }
      });
    }

    DutyShiftTemplateModel.findOneAndUpdate(
      { _id: _id, status: 1 },
      { $set: updateData },
      { new: true }
    )
    .lean()
    .exec((err, template) => {
      if (err) {
        return next(err);
      }

      if (!template) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Mẫu ca trực không tồn tại hoặc đã bị xóa'
          }
        });
      }

      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        message: {
          head: 'Thông báo',
          body: 'Cập nhật mẫu ca trực thành công'
        },
        data: template
      });
    });
  };

  async.waterfall([checkParams, updateDutyShiftTemplate], (err, data) => {
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};

const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Jo<PERSON>);

const CriminalSubjectModel = require('../../../models/criminalSubject');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const { change_alias } = require('../../../util/tool');

/**
 * API tạo đối tượng hình sự mới
 * POST /api/v1.0/criminal-subject/create
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const {
    name,
    photos = [],
    dob,
    gender,
    idNumber,
    phones = [],
    permanentAddress,
    temporaryAddress,
    currentResidence,
    category,
    dangerLevel,
    legalStatus,
    description,
    managingUnit,
    assignedOfficers = [],
    businessNotes,
    areas = []
  } = req.body;

  // Validation schema
  const schema = Joi.object({
    name: Joi.string().required().trim().max(100),
    photos: Joi.array().items(Joi.string().uri()),
    dob: Joi.string().pattern(/^\d{2}\/\d{2}\/\d{4}$/),
    gender: Joi.string().valid('male', 'female'),
    idNumber: Joi.string().trim().max(20),
    phones: Joi.array().items(Joi.string().trim().min(10).max(11)),
    permanentAddress: Joi.string().trim().max(500).optional().allow(''),
    temporaryAddress: Joi.string().trim().max(500).optional().allow(''),
    currentResidence: Joi.string().trim().max(500).optional().allow(''),
    category: Joi.string().required().valid('security', 'criminal', 'drug', 'other'),
    dangerLevel: Joi.string().required().valid('low', 'medium', 'high'),
    legalStatus: Joi.string().required().valid('pretrial', 'trial', 'suspended_sentence', 'detention', 'arrest'),
    description: Joi.string().trim().max(2000).optional().allow(''),
    managingUnit: Joi.objectId().allow(''),
    assignedOfficers: Joi.array().items(Joi.objectId()),
    businessNotes: Joi.string().trim().max(1000).optional().allow(''),
    areas: Joi.array().items(Joi.objectId())
  });

  const validateParams = (next) => {
    const { error } = schema.validate(req.body, { allowUnknown: true });
    console.log('error', error);
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const checkDuplicateIdNumber = (next) => {
    if (!idNumber || !idNumber.trim()) {
      return next();
    }

    CriminalSubjectModel.findOne({
      idNumber: idNumber.trim(),
      status: 1
    }).lean().exec((err, existingSubject) => {
      if (err) {
        return next(err);
      }

      if (existingSubject) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGES.CRIMINAL_SUBJECT.DUPLICATE_ID_NUMBER
        });
      }

      next();
    });
  };

  const createSubject = (next) => {
    const subjectData = {
      name: name.trim(),
      nameAlias: change_alias(name.trim()),
      category,
      dangerLevel,
      createdBy: userId,
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    // Optional fields
    if (photos && photos.length > 0) subjectData.photos = photos;
    if (dob) subjectData.dob = dob;
    if (gender) subjectData.gender = gender;
    if (idNumber && idNumber.trim()) subjectData.idNumber = idNumber.trim();
    if (phones && phones.length > 0) subjectData.phones = phones.filter(p => p && p.trim());
    if (permanentAddress) subjectData.permanentAddress = permanentAddress.trim();
    if (temporaryAddress) subjectData.temporaryAddress = temporaryAddress.trim();
    if (currentResidence) subjectData.currentResidence = currentResidence.trim();
    if (legalStatus) subjectData.legalStatus = legalStatus.trim();
    if (description) subjectData.description = description.trim();
    if (managingUnit) subjectData.managingUnit = managingUnit;
    if (assignedOfficers && assignedOfficers.length > 0) subjectData.assignedOfficers = assignedOfficers;
    if (businessNotes) subjectData.businessNotes = businessNotes.trim();
    if (areas && areas.length > 0) subjectData.areas = areas;

    const subject = new CriminalSubjectModel(subjectData);
    subject.save((err, result) => {
      if (err) {
        return next(err);
      }

      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        message: MESSAGES.CRIMINAL_SUBJECT.CREATE_SUCCESS,
        data: result
      });
    });
  };

  // Execute waterfall
  async.waterfall([
    validateParams,
    checkDuplicateIdNumber,
    createSubject
  ], (err, data) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Handle system errors
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};

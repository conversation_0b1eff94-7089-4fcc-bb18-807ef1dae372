const _ = require('lodash');
const async = require('async');
const ms = require('ms');
const { v4: uuidv4 } = require('uuid');
const config = require('config');
const util = require('util');
const rp = require('request-promise');

const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const HandbookCategoryModel = require('../../../models/handbookCategory');

module.exports = (req, res) => {
  HandbookCategoryModel.find({ status: 1 })
    .sort({ updatedAt: -1 })
    .lean()
    .exec((err, results) => {
      if (err) {
        return res.json({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR,
        });
      }
      res.json({
        code: CONSTANTS.CODE.SUCCESS,
        data: results
      });
    });
};

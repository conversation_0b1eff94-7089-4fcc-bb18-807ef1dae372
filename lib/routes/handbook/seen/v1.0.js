const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const HandbookModel = require('../../../models/handbook');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  const { _id } = req.body;
  if (!userId || !_id) {
    return res.json({
      code: CONSTANTS.CODE.WRONG_PARAMS,
      message: MESSAGES.SYSTEM.WRONG_PARAMS
    });
  }
  // Push userId vào seen nếu chưa có, cập nhật views = seen.length
  HandbookModel.findOneAndUpdate(
    { _id, status: 1 },
    [
      {
        $set: {
          seen: {
            $cond: [
              { $in: [userId, "$seen"] },
              "$seen",
              { $concatArrays: ["$seen", [userId]] }
            ]
          },
          views: {
            $cond: [
              { $in: [userId, "$seen"] },
              { $size: "$seen" },
              { $add: [{ $size: "$seen" }, 1] }
            ]
          },
          updatedAt: Date.now()
        }
      }
    ],
    { new: true }
  ).lean().exec((err, handbook) => {
    if (err) {
      return res.json({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });
    }
    if (!handbook) {
      return res.json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Cẩm nang không tồn tại hoặc đã bị xóa'
        }
      });
    }
    res.json({
      code: CONSTANTS.CODE.SUCCESS,
      data: handbook
    });
  });
};

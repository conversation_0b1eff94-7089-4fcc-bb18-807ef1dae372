const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const HandbookModel = require('../../../models/handbook');

module.exports = (req, res) => {
  const { _id } = req.body;
  if (!_id) {
    return res.json({
      code: CONSTANTS.CODE.WRONG_PARAMS,
      message: MESSAGES.SYSTEM.WRONG_PARAMS
    });
  }
  HandbookModel.findOne({ _id, status: 1 })
    .populate('categories', 'name icon')
    .select('-content')
    .lean()
    .exec((err, handbook) => {
      if (err) {
        return res.json({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR
        });
      }
      res.json({
        code: CONSTANTS.CODE.SUCCESS,
        data: handbook
      });
    });
};

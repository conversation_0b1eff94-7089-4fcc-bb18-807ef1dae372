const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const HandbookModel = require('../../../models/handbook');
const UserModel = require('../../../models/user');
const escapeStringRegexp = require('escape-string-regexp');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  let {
    page = 1,
    limit = 20,
    sort = 'createdAt',
    order = -1,
    textSearch = '',
    category = ''
  } = req.body;

  page = parseInt(page, 10) || 1;
  limit = parseInt(limit, 10) || 20;
  order = parseInt(order, 10) || -1;

  let userUnits = [];

  async.waterfall([
    function getUserUnits(next) {
      if (!userId) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGES.SYSTEM.WRONG_PARAMS
        });
      }
      UserModel.findById(userId).select('units').lean().exec((err, user) => {
        if (err) return next(err);
        if (!user) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: {
              head: 'Thông báo',
              body: 'Người dùng không tồn tại'
            }
          });
        }
        userUnits = user.units || [];
        next();
      });
    },
    function listHandbooks(next) {
      let objSearch = { status: 1 };
      if (textSearch && textSearch.trim()) {
        const $regex = escapeStringRegexp(textSearch.trim());
        objSearch.$or = [
          { title: { $regex, $options: 'i' } },
          { idNumber: { $regex, $options: 'i' } },
          { content: { $regex, $options: 'i' } }
        ];
      }
      if (category && category.trim()) {
        objSearch.categories = category;
      }
      if (userUnits.length > 0) {
        objSearch.$or = objSearch.$or || [];
        objSearch.$or.push(
          { units: { $in: userUnits } },
          { units: { $size: 0 } }
        );
      }
      HandbookModel.find(objSearch)
        .populate('categories', 'name icon')
        .select('-content -seen')
        .sort({ [sort]: order })
        .skip((page - 1) * limit)
        .limit(limit)
        .lean()
        .exec((err, results) => {
          if (err) return next(err);
          HandbookModel.countDocuments(objSearch, (err, total) => {
            if (err) return next(err);
            next(null, {
              code: CONSTANTS.CODE.SUCCESS,
              data: results,
              page,
              limit,
              total,
              totalPages: Math.ceil(total / limit)
            });
          });
        });
    }
  ], (err, data) => {
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR,
    });
    res.json(data || err);
  });
};

const _ = require('lodash');
const async = require('async');
const Joi = require('joi');

const statisticsService = require('../../../services/statisticsService');
const statisticsMetadataService = require('../../../services/statisticsMetadataService');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

/**
 * API thống kê báo cáo theo trạng thái chi tiết
 * POST /api/v1.0/statistics/reports-status
 *
 * Tr<PERSON> về thống kê chi tiết về trạng thái báo cáo trong hệ thống
 * Bao gồm phân tích workflow, thời gian xử lý và hiệu suất
 * Dữ liệu này được sử dụng để hiển thị dashboard quản lý và giám sát
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const {
    timeRange = 'week',
    startDate,
    endDate
  } = req.body;

  let result;

  /**
   * Validate tham số đầu vào
   */
  const validateParams = (next) => {
    const schema = Joi.object({
      timeRange: Joi.string().valid('day', 'week', 'month', 'custom').default('week'),
      startDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).when('timeRange', {
        is: 'custom',
        then: Joi.required(),
        otherwise: Joi.optional()
      }),
      endDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).when('timeRange', {
        is: 'custom',
        then: Joi.required(),
        otherwise: Joi.optional()
      })
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: error.details[0].message
        }
      });
    }

    next();
  };

  const getReportsStatusStatsFromCache = (next) => {
    statisticsMetadataService.getCachedStatisticsData(
      'reports_status',
      timeRange
    )
      .then((cachedData) => {
        if (cachedData) {
          console.log('[Cache Hit] Reports status stats from cache');
          result = {
            success: true,
            data: cachedData,
            fromCache: true,
            cacheTimestamp: Date.now()
          };
        }

        next();
      })
      .catch(error => {
        console.error('[Cache Error] Failed to get reports status stats from cache:', error);
        return next();
      })
  };

  /**
   * Lấy thống kê báo cáo theo trạng thái từ service với cache layer
   */
  const getReportsStatusStats = (next) => {
    if (result) {
      return next();
    }

    try {
      // Cache miss - gọi service
      console.log('[Cache Miss] Fetching reports status stats from database');
      statisticsService.getReportsStatusStats({
        timeRange,
        startDate,
        endDate,
        userId
      })
        .then((serviceResult) => {
          if (!serviceResult || !serviceResult.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: serviceResult?.message || MESSAGES.SYSTEM.ERROR
            });
          }

          // Cache kết quả
          try {
            statisticsMetadataService.cacheStatisticsData(
              'reports_status',
              serviceResult.data,
              timeRange
            );
            console.log('[Cache Set] Cached reports status stats');
          } catch (cacheError) {
            console.error('[Cache Error] Failed to cache reports status stats:', cacheError);
          }

          result = {
            ...serviceResult,
            fromCache: false,
            cacheTimestamp: Date.now()
          };
          next();
        })
    } catch (error) {
      console.error('[API Error] Error in getReportsStatusStats:', error);
      next(error);
    }
  };

  /**
   * Ghi log và trả về kết quả
   */
  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      // message: result.message,
      data: result.data
    });

    // Ghi log hoạt động
    if (global.SystemLogModel) {
      global.SystemLogModel.create({
        user: userId,
        action: 'get_reports_status_stats',
        description: 'Xem thống kê báo cáo theo trạng thái',
        data: req.body,
        updatedData: {
          totalReports: result.data?.statusStats?.total || 0,
          approvalRate: result.data?.workflowStats?.approvalRate || 0,
          completionRate: result.data?.workflowStats?.completionRate || 0,
          averageProcessingTime: result.data?.processingTime?.averageProcessingTime || 0,
          timeRange: result.data?.period?.type
        }
      }, () => {});
    }
  };

  // Thực thi waterfall
  async.waterfall([
    validateParams,
    getReportsStatusStatsFromCache,
    getReportsStatusStats,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      global.logger && global.logger.logError([err], req.originalUrl, req.body);
      global.MailUtil && global.MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Xử lý lỗi hệ thống
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};

const _ = require('lodash');
const async = require('async');
const Joi = require('joi');

const statisticsService = require('../../../services/statisticsService');
const statisticsMetadataService = require('../../../services/statisticsMetadataService');
const statisticsUtils = require('../../../utils/statisticsUtils');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

/**
 * API thống kê các vấn đề khác (Incidents Other)
 * POST /api/v1.0/statistics/reports-incidents-other
 *
 * Tr<PERSON> về thống kê các vấn đề khác từ các báo cáo có chartTypes khác "highlight"
 * Bao gồm tổng số vụ việc theo lĩnh vực, theo khu vực và tỷ lệ thay đổi so với khoảng thời gian trước
 * Dữ liệu này đư<PERSON>c sử dụng để hiển thị thống kê incidents other trên dashboard
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const {
    timeRange = 'day',
    startDate,
    endDate,
    area // ID khu vực để filter (optional)
  } = req.body;

  let result;

  /**
   * Validate tham số đầu vào
   */
  const validateParams = (next) => {
    const schema = Joi.object({
      timeRange: Joi.string().valid('day', 'week', 'month', 'custom').default('day'),
      startDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).when('timeRange', {
        is: 'custom',
        then: Joi.required(),
        otherwise: Joi.optional()
      }),
      endDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).when('timeRange', {
        is: 'custom',
        then: Joi.required(),
        otherwise: Joi.optional()
      }),
      area: Joi.objectId().optional() // Khu vực filter là optional
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const getIncidentsOtherStatsFromCache = (next) => {
    statisticsMetadataService.getCachedStatisticsData(
      'reports_incidents_other',
      area,
      timeRange,
      startDate,
      endDate
    )
      .then((cachedData) => {
        if (cachedData) {
          console.log('[Cache Hit] Reports incidents other stats from cache');
          result = {
            success: true,
            data: cachedData,
            fromCache: true,
            cacheTimestamp: Date.now()
          };
        }

        next();
      })
      .catch(error => {
        console.error('[Cache Error] Failed to get reports incidents other stats from cache:', error);
        return next();
      })
  };

  /**
   * Lấy thống kê các vấn đề khác từ service với cache layer
   */
  const getIncidentsOtherStats = (next) => {
    if (result) {
      return next();
    }

    try {
      // Cache miss - gọi service
      console.log('[Cache Miss] Fetching reports incidents other stats from database');
      statisticsService.getIncidentsOtherStats({
        timeRange,
        startDate,
        endDate,
        userId,
        area // Thêm area filter vào params
      })
        .then((serviceResult) => {
          if (!serviceResult || !serviceResult.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: serviceResult?.message || MESSAGES.SYSTEM.ERROR
            });
          }

          // Cache kết quả
          try {
            statisticsMetadataService.cacheStatisticsData(
              'reports_incidents_other',
              serviceResult.data,
              area,
              timeRange,
              startDate,
              endDate
            );
            console.log('[Cache Set] Cached reports incidents other stats');
          } catch (cacheError) {
            console.error('[Cache Error] Failed to cache reports incidents other stats:', cacheError);
          }

          result = {
            ...serviceResult,
            fromCache: false,
            cacheTimestamp: Date.now()
          };
          next();
        })
    } catch (error) {
      console.error('[API Error] Error in getIncidentsOtherStats:', error);
      next(error);
    }
  };

  /**
   * Ghi log và trả về kết quả
   */
  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: result.data
    });

    // Ghi log hoạt động
    if (SystemLogModel) {
      SystemLogModel.create({
        user: userId,
        action: 'get_incidents_other_stats',
        description: 'Xem thống kê các vấn đề khác',
        data: req.body,
        updatedData: {
          totalIncidents: result.data?.summary?.totalIncidents || 0,
          totalReports: result.data?.summary?.totalReports || 0,
          jobTypeCount: result.data?.byJobType?.length || 0,
          areaCount: result.data?.byArea?.length || 0,
          timeRange: result.data?.period?.type,
          hasChangeRate: !!result.data?.changeRate,
          hasAreaFilter: !!area,
          areaId: area
        }
      }, () => { });
    }
  };

  // Thực thi waterfall
  async.waterfall([
    validateParams,
    getIncidentsOtherStatsFromCache,
    getIncidentsOtherStats,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Xử lý lỗi hệ thống
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};

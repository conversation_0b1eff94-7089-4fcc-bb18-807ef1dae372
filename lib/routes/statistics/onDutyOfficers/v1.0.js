const _ = require('lodash');
const async = require('async');
const Joi = require('joi');

const statisticsService = require('../../../services/statisticsService');
const statisticsMetadataService = require('../../../services/statisticsMetadataService');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

/**
 * API lấy danh sách cán bộ đang trực ban
 * POST /api/v1.0/statistics/on-duty-officers
 *
 * Tr<PERSON> về danh sách chi tiết các cán bộ đang trong ca trực hiện tại
 * <PERSON>o gồm thông tin cá nhân, đơn vị, loại trực và thống kê tổng quan
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const {
    timeRange = 'week',
    startDate,
    endDate
  } = req.body;

  let result;

  /**
   * Validate tham số đầu vào
   */
  const validateParams = (next) => {
    const schema = Joi.object({
      timeRange: Joi.string().valid('day', 'week', 'month', 'custom').default('week'),
      startDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).when('timeRange', {
        is: 'custom',
        then: Joi.required(),
        otherwise: Joi.optional()
      }),
      endDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).when('timeRange', {
        is: 'custom',
        then: Joi.required(),
        otherwise: Joi.optional()
      })
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const getOnDutyOfficersFromCache = (next) => {
    statisticsMetadataService.getCachedStatisticsData(
      'on_duty_officers',
      timeRange
    )
      .then((cachedData) => {
        if (cachedData) {
          console.log('[Cache Hit] On duty officers from cache');
          result = {
            success: true,
            data: cachedData,
            fromCache: true,
            cacheTimestamp: Date.now()
          };
        }

        next();
      })
      .catch(error => {
        console.error('[Cache Error] Failed to get on duty officers from cache:', error);
        return next();
      });
  };

  /**
   * Lấy danh sách cán bộ trực ban từ service với cache layer
   */
  const getOnDutyOfficers = (next) => {
    if (result) {
      return next();
    }

    try {
      // Cache miss - gọi service
      console.log('[Cache Miss] Fetching on duty officers from database');
      statisticsService.getOnDutyOfficers({
        timeRange,
        startDate,
        endDate,
        userId
      })
        .then((serviceResult) => {
          if (!serviceResult || !serviceResult.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: serviceResult?.message || MESSAGES.SYSTEM.ERROR
            });
          }

          // Cache kết quả
          try {
            statisticsMetadataService.cacheStatisticsData(
              'on_duty_officers',
              serviceResult.data,
              timeRange
            );
            console.log('[Cache Set] Cached on duty officers');
          } catch (cacheError) {
            console.error('[Cache Error] Failed to cache on duty officers:', cacheError);
          }

          result = {
            ...serviceResult,
            fromCache: false,
            cacheTimestamp: Date.now()
          };
          next();
        })
    } catch (error) {
      console.error('[API Error] Error in getOnDutyOfficers:', error);
      next(error);
    }
  };

  /**
   * Ghi log và trả về kết quả
   */
  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      // message: result.message,
      data: result.data
    });

    // Ghi log hoạt động
    if (SystemLogModel) {
      SystemLogModel.create({
        user: userId,
        action: 'get_on_duty_officers',
        description: 'Xem danh sách cán bộ trực ban',
        data: req.body,
        updatedData: {
          totalOfficers: result.data?.summary?.totalOnDuty || 0,
          timeRange: result.data?.period?.type
        }
      }, () => {});
    }
  };

  // Thực thi waterfall
  async.waterfall([
    validateParams,
    getOnDutyOfficersFromCache,
    getOnDutyOfficers,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Xử lý lỗi hệ thống
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};

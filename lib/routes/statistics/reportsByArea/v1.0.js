const _ = require('lodash');
const async = require('async');
const Joi = require('joi');

const statisticsService = require('../../../services/statisticsService');
const statisticsMetadataService = require('../../../services/statisticsMetadataService');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

/**
 * API thống kê số lượng báo cáo theo khu vực
 * POST /api/v1.0/statistics/reports-by-area
 *
 * Tr<PERSON> về thống kê phân bố báo cáo trong từng khu vực địa lý
 * Bao gồm số lượng báo cáo, phân loại theo trạng thái và loại báo cáo
 * Dữ liệu này được sử dụng để render heatmap hiển thị mật độ vụ việc theo khu vực
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const {
    timeRange = 'day',
    startDate,
    endDate
  } = req.body;

  let result;

  /**
   * Validate tham số đầu vào
   */
  const validateParams = (next) => {
    const schema = Joi.object({
      timeRange: Joi.string().valid('day', 'week', 'month', 'custom').default('day'),
      startDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).when('timeRange', {
        is: 'custom',
        then: Joi.required(),
        otherwise: Joi.optional()
      }),
      endDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).when('timeRange', {
        is: 'custom',
        then: Joi.required(),
        otherwise: Joi.optional()
      })
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const getReportsByAreaStatsFromCache = (next) => {
    statisticsMetadataService.getCachedStatisticsData(
      'reports_by_area',
      timeRange
    )
      .then((cachedData) => {
        if (cachedData) {
          console.log('[Cache Hit] Reports by area stats from cache');
          result = {
            success: true,
            data: cachedData,
            fromCache: true,
            cacheTimestamp: Date.now()
          };
        }

        next();
      })
      .catch(error => {
        console.error('[Cache Error] Failed to get reports by area stats from cache:', error);
        return next();
      });
  };

  /**
   * Lấy thống kê báo cáo theo khu vực từ service với cache layer
   */
  const getReportsByAreaStats = (next) => {
    if (result) {
      return next();
    }

    try {
      // Cache miss - gọi service
      console.log('[Cache Miss] Fetching reports by area stats from database');
      statisticsService.getReportsByAreaStats({
        timeRange,
        startDate,
        endDate,
        userId
      })
        .then((serviceResult) => {
          if (!serviceResult || !serviceResult.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: serviceResult?.message || MESSAGES.SYSTEM.ERROR
            });
          }

          // Cache kết quả
          try {
            statisticsMetadataService.cacheStatisticsData(
              'reports_by_area',
              serviceResult.data,
              timeRange
            );
            console.log('[Cache Set] Cached reports by area stats');
          } catch (cacheError) {
            console.error('[Cache Error] Failed to cache reports by area stats:', cacheError);
          }

          result = {
            ...serviceResult,
            fromCache: false,
            cacheTimestamp: Date.now()
          };
          next();
        })
    } catch (error) {
      console.error('[API Error] Error in getReportsByAreaStats:', error);
      next(error);
    }
  };

  /**
   * Ghi log và trả về kết quả
   */
  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: result.data
    });

    // Ghi log hoạt động
    if (SystemLogModel) {
      SystemLogModel.create({
        user: userId,
        action: 'get_reports_by_area_stats',
        description: 'Xem thống kê báo cáo theo khu vực',
        data: req.body,
        updatedData: {
          totalAreas: result.data?.summary?.totalAreas || 0,
          totalReports: result.data?.summary?.totalReports || 0,
          averageReportsPerArea: result.data?.summary?.averageReportsPerArea || 0,
          timeRange: result.data?.period?.type
        }
      }, () => {});
    }
  };

  // Thực thi waterfall
  async.waterfall([
    validateParams,
    getReportsByAreaStatsFromCache,
    getReportsByAreaStats,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Xử lý lỗi hệ thống
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};

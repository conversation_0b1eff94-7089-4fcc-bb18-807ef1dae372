const _ = require('lodash');
const async = require('async');
const Joi = require('joi');

const statisticsService = require('../../../services/statisticsService');
const statisticsMetadataService = require('../../../services/statisticsMetadataService');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

/**
 * API thống kê số cán bộ điểm danh
 * POST /api/v1.0/statistics/attendance
 *
 * Trả về thống kê chi tiết về tình hình điểm danh của cán bộ
 * Bao gồm tỷ lệ điểm danh, phân tích theo ca, theo đơn vị và danh sách cán bộ điểm danh
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const {
    timeRange = 'week',
    startDate,
    endDate
  } = req.body;

  let result;

  /**
   * Validate tham số đầu vào
   */
  const validateParams = (next) => {
    const schema = Joi.object({
      timeRange: Joi.string().valid('day', 'week', 'month', 'custom').default('week'),
      startDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).when('timeRange', {
        is: 'custom',
        then: Joi.required(),
        otherwise: Joi.optional()
      }),
      endDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).when('timeRange', {
        is: 'custom',
        then: Joi.required(),
        otherwise: Joi.optional()
      })
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const getAttendanceStatsFromCache = (next) => {
    statisticsMetadataService.getCachedStatisticsData(
      'attendance',
      timeRange
    )
      .then((cachedData) => {
        if (cachedData) {
          console.log('[Cache Hit] Attendance stats from cache');
          result = {
            success: true,
            data: cachedData,
            fromCache: true,
            cacheTimestamp: Date.now()
          };
        }

        next();
      })
      .catch(error => {
        console.error('[Cache Error] Failed to get attendance stats from cache:', error);
        return next();
      });
  };

  /**
   * Lấy thống kê cán bộ điểm danh từ service với cache layer
   */
  const getAttendanceStats = (next) => {
    if (result) {
      return next();
    }

    try {
      // ❌ KHÔNG CÓ CACHE - GỌI SERVICE
      console.log('[Cache Miss] Fetching attendance stats from database');
      statisticsService.getAttendanceStats({
        timeRange,
        startDate,
        endDate,
        userId
      })
        .then((serviceResult) => {
          if (!serviceResult || !serviceResult.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: serviceResult?.message || MESSAGES.SYSTEM.ERROR
            });
          }

          // 💾 CACHE KẾT QUẢ CHO LẦN SAU
          try {
            statisticsMetadataService.cacheStatisticsData(
              'attendance',
              serviceResult.data,
              timeRange
            );
            console.log('[Cache Set] Cached attendance stats for future requests');
          } catch (cacheError) {
            console.error('[Cache Error] Failed to cache attendance stats:', cacheError);
            // Không throw error, vẫn trả về data
          }

          result = {
            ...serviceResult,
            fromCache: false,
            cacheTimestamp: Date.now()
          };
          next();
        })
        .catch((error) => {
          console.error('[API Error] Error in getAttendanceStats:', error);
          return next(error);
        });
    } catch (error) {
      console.error('[API Error] Error in getAttendanceStats:', error);
      next(error);
    }
  };

  /**
   * Ghi log và trả về kết quả
   */
  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: result.data
    });

    // Ghi log hoạt động
    if (SystemLogModel) {
      SystemLogModel.create({
        user: userId,
        action: 'get_attendance_stats',
        description: 'Xem thống kê cán bộ điểm danh',
        data: req.body,
        updatedData: {
          totalAttendance: result.data?.summary?.totalAttendance || 0,
          attendanceRate: result.data?.summary?.attendanceRate || 0,
          timeRange: result.data?.period?.type
        }
      }, () => { });
    }
  };

  // Thực thi waterfall
  async.waterfall([
    validateParams,
    getAttendanceStatsFromCache,
    getAttendanceStats,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Xử lý lỗi hệ thống
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};

const _ = require('lodash');
const async = require('async');
const Joi = require('joi');

const statisticsService = require('../../../services/statisticsService');
const statisticsMetadataService = require('../../../services/statisticsMetadataService');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

/**
 * API thống kê tổng số lượng cán bộ (đã cải thiện)
 * POST /api/v1.0/statistics/officer-summary
 *
 * <PERSON><PERSON><PERSON> về thống kê tổng quan về số lượng cán bộ theo các trạng thái khác nhau
 * Bao gồm: tổng số cán bộ, đang làm việc, đang trực ban, nghỉ phép, đi công tác
 *
 * Tính toán dựa trên:
 * - Thời gian hiện tại và ca làm việc hiện tại
 * - Trạng thái duty shifts đã được xác nhận
 * - Leave requests đã được phê duyệt và đang hiệu lực
 * - Work schedule status cho business trip
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const {
    timeRange = 'week',
    startDate,
    endDate
  } = req.body;

  let result;

  /**
   * Validate tham số đầu vào
   */
  const validateParams = (next) => {
    const schema = Joi.object({
      timeRange: Joi.string().valid('day', 'week', 'month', 'custom').default('week'),
      startDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).when('timeRange', {
        is: 'custom',
        then: Joi.required(),
        otherwise: Joi.optional()
      }),
      endDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).when('timeRange', {
        is: 'custom',
        then: Joi.required(),
        otherwise: Joi.optional()
      })
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const getOfficerSummaryStatsFromCache = (next) => {
    statisticsMetadataService.getCachedStatisticsData(
      'officer_summary',
      timeRange
    )
      .then((cachedData) => {
        if (cachedData) {
          console.log('[Cache Hit] Officer summary stats from cache');
          result = {
            success: true,
            data: cachedData,
            fromCache: true,
            cacheTimestamp: Date.now()
          };
        }

        next();
      })
      .catch(error => {
        console.error('[Cache Error] Failed to get officer summary stats from cache:', error);
        next();
      })
  };

  /**
   * Lấy thống kê tổng số lượng cán bộ từ service với cache layer
   */
  const getOfficerSummaryStats = (next) => {
    if (result) {
      return next();
    }

    try {
      // Cache miss - gọi service
      console.log('[Cache Miss] Fetching officer summary stats from database');
      statisticsService.getOfficerSummaryStats({
        timeRange,
        startDate,
        endDate,
        userId
      })
        .then((serviceResult) => {
          if (!serviceResult || !serviceResult.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: serviceResult?.message || MESSAGES.SYSTEM.ERROR
            });
          }

          // Cache kết quả
          try {
            statisticsMetadataService.cacheStatisticsData(
              'officer_summary',
              serviceResult.data,
              timeRange
            );
            console.log('[Cache Set] Cached officer summary stats');
          } catch (cacheError) {
            console.error('[Cache Error] Failed to cache officer summary stats:', cacheError);
          }

          result = {
            ...serviceResult,
            fromCache: false,
            cacheTimestamp: Date.now()
          };
          next();
        })
    } catch (error) {
      console.error('[API Error] Error in getOfficerSummaryStats:', error);
      next(error);
    }
  };

  /**
   * Ghi log và trả về kết quả
   */
  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: result.data
    });

    // Ghi log hoạt động
    if (SystemLogModel) {
      SystemLogModel.create({
        user: userId,
        action: 'get_officer_summary_stats',
        description: 'Xem thống kê tổng số lượng cán bộ',
        data: req.body,
        updatedData: {
          totalOfficers: result.data?.summary?.totalOfficers || 0,
          working: result.data?.summary?.working || 0,
          onDuty: result.data?.summary?.onDuty || 0,
          onLeave: result.data?.summary?.onLeave || 0,
          businessTrip: result.data?.summary?.businessTrip || 0,
          timeRange: result.data?.period?.type
        }
      }, () => { });
    }
  };

  // Thực thi waterfall
  async.waterfall([
    validateParams,
    getOfficerSummaryStatsFromCache,
    getOfficerSummaryStats,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Xử lý lỗi hệ thống
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};

const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const MeetingScheduleModel = require('../../../models/meetingSchedule');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  const { _id } = req.body;

  const checkParams = (next) => {
    if (!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    if (!_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp ID lịch họp'
        }
      });
    }

    // Validate ObjectId
    if (typeof _id !== 'string' || !_id.match(/^[0-9a-fA-F]{24}$/)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'ID lịch họp không hợp lệ'
        }
      });
    }

    next();
  };

  const inactiveMeetingSchedule = (next) => {
    const updateData = {
      status: 0,
      updatedAt: Date.now()
    };

    MeetingScheduleModel.findOneAndUpdate(
      { _id: _id, status: 1 }, // Chỉ cập nhật lịch họp đang hoạt động
      { $set: updateData },
      { new: true }
    )
    .populate('officers', 'name email phone')
    .populate('assignedBy', 'name email')
    .exec((err, meeting) => {
      if (err) {
        return next(err);
      }

      if (!meeting) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Lịch họp không tồn tại hoặc đã bị xóa'
          }
        });
      }

      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        message: {
          head: 'Thông báo',
          body: 'Xóa lịch họp thành công'
        },
        data: meeting
      });
    });
  };

  async.waterfall([checkParams, inactiveMeetingSchedule], (err, data) => {
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};

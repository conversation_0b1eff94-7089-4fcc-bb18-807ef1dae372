const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const MeetingScheduleModel = require('../../../models/meetingSchedule');
const UserModel = require('../../../models/user');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  const { week, weekType, startTime, endTime } = req.body; // Hỗ trợ cả week/weekType và startTime/endTime
  let userInfo = null; 
  const checkParams = (next) => {
    if (!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    // Validate week/weekType parameter
    const currentWeekType = weekType || week || 'current'; // Ưu tiên weekType, sau đó week, cuối cùng mặc định 'current'
    const validWeekTypes = ['current', 'previous', 'next'];

    if (currentWeekType && !validWeekTypes.includes(currentWeekType)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'weekType phải là current, previous hoặc next'
        }
      });
    }

    // Validate startTime và endTime nếu có
    if (startTime || endTime) {
      if (startTime && !_.isNumber(startTime)) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Thời gian bắt đầu phải là Số (number)'
          }
        });
      }
      
      if (endTime && !_.isNumber(endTime)) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Thời gian kết thúc phải là Số (number)'
          }
        });
      }
      
      if (startTime && endTime && startTime >= endTime) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Thời gian bắt đầu phải nhỏ hơn Thời gian kết thúc'
          }
        });
      }
      
      // Kiểm tra khoảng thời gian tối đa 31 ngày
      if (startTime && endTime) {
        const maxDuration = 31 * 24 * 60 * 60 * 1000; // 31 ngày tính bằng milliseconds
        const duration = endTime - startTime;
        
        if (duration > maxDuration) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Khoảng thời gian tối đa là 31 ngày'
            }
          });
        }
      }
    }

    next(null, { weekType: currentWeekType });
  };

  

  const calculateTimeRange = (result, next) => {
    const { weekType } = result;
    let finalStartTime, finalEndTime, timeRangeType;

    // Ưu tiên sử dụng startTime và endTime nếu có
    if (startTime || endTime) {
      finalStartTime = startTime;
      finalEndTime = endTime;
      timeRangeType = 'custom';
    } 
    // Nếu không có startTime/endTime, tính toán theo weekType
    else if (weekType) {
      const now = new Date();
      
      // Tính toán ngày đầu tuần (Thứ 2)
      const currentDay = now.getDay(); // 0 = Chủ nhật, 1 = Thứ 2, ...
      const mondayOffset = currentDay === 0 ? -6 : 1 - currentDay; // Số ngày cần trừ để về Thứ 2
      
      const monday = new Date(now);
      monday.setDate(now.getDate() + mondayOffset);
      monday.setHours(0, 0, 0, 0);

      // Tính toán tuần cần lấy
      let startOfWeek = new Date(monday);
      
      if (weekType === 'previous') {
        startOfWeek.setDate(monday.getDate() - 7);
      } else if (weekType === 'next') {
        startOfWeek.setDate(monday.getDate() + 7);
      }
      
      // Ngày cuối tuần (Chủ nhật)
      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(startOfWeek.getDate() + 6);
      endOfWeek.setHours(23, 59, 59, 999);

      finalStartTime = startOfWeek.getTime();
      finalEndTime = endOfWeek.getTime();
      timeRangeType = 'week';
    }

    next(null, {
      startTime: finalStartTime,
      endTime: finalEndTime,
      weekType,
      timeRangeType
    });
  };

  const getUserInfo = (result, next) => {
    // Lấy thông tin user để hiển thị tên cán bộ trong cuộc họp
    UserModel
      .findById(userId)
      .select('units')
      .exec((err, user) => {
        if (err) {
          return next(err);
        }
        if (!user) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: {
              head: 'Thông báo',
              body: 'Người dùng không tồn tại'
            }
          });
        }
        userInfo = user;
        next(null, result);
      });
  }

  const getMeetingSchedules = (result, next) => {
    const { startTime, endTime, weekType, timeRangeType } = result;
    
    let query = {
      status: 1, // Chỉ lấy cuộc họp đang hoạt động
    };
    query['$or'] = [
      { officers: userId },
      { units: { $in: userInfo.units } }
    ];
    // Thêm điều kiện thời gian nếu có
    if (startTime || endTime) {
      query.startTime = {};
      if (startTime) {
        query.startTime.$gte = startTime;
      }
      if (endTime) {
        query.startTime.$lte = endTime;
      }
    }

    console.log('Query for fetching meetings:', query.$or);

    MeetingScheduleModel.find(query)
      .populate('officers', 'name') // Populate thông tin cán bộ
      .populate('assignedBy', 'name') // Populate thông tin người tạo lịch
      .populate('units', 'name')
      .sort({ startTime: 1 }) // Sắp xếp theo thời gian bắt đầu
      .exec((err, meetings) => {
        if (err) {
          return next(err);
        }

        // Nhóm cuộc họp theo ngày nếu có khoảng thời gian
        let groupedMeetings = null;
        if (startTime || endTime) {
          const meetingsByDate = {};
          meetings.forEach(meeting => {
            const meetingDate = new Date(meeting.startTime);
            // Tạo dateKey dựa trên ngày/tháng/năm để đảm bảo cùng ngày được group lại
            const year = meetingDate.getFullYear();
            const month = String(meetingDate.getMonth() + 1).padStart(2, '0');
            const day = String(meetingDate.getDate()).padStart(2, '0');
            const dateKey = `${year}-${month}-${day}`;
            
            if (!meetingsByDate[dateKey]) {
              meetingsByDate[dateKey] = [];
            }
            
            meetingsByDate[dateKey].push(meeting);
          });

          // Chuyển đổi sang format mong muốn
          groupedMeetings = Object.keys(meetingsByDate)
            .sort() // Sắp xếp theo ngày
            .map(dateKey => {
              // Sử dụng meeting đầu tiên để lấy thông tin ngày chính xác
              const firstMeeting = meetingsByDate[dateKey][0];
              const meetingDate = new Date(firstMeeting.startTime);
              const dayOfWeek = meetingDate.getDay();
              
              // Format ngày theo dd/MM
              const day = String(meetingDate.getDate()).padStart(2, '0');
              const month = String(meetingDate.getMonth() + 1).padStart(2, '0');
              
              return {
                date: `${day}/${month}`,
                dayOfWeek: getDayName(dayOfWeek),
                meetings: meetingsByDate[dateKey]
              };
            });
        }

        const result = {
          meetings: (startTime || endTime) ? groupedMeetings : meetings,
          totalMeetings: meetings.length
        };

        // Thêm thông tin thời gian nếu có khoảng thời gian
        if (startTime || endTime) {
          result.timeRangeInfo = {
            type: timeRangeType,
            weekType: timeRangeType === 'week' ? weekType : null,
            startDate: startTime ? `${new Date(startTime).getDate().toString().padStart(2, '0')}/${(new Date(startTime).getMonth() + 1).toString().padStart(2, '0')}` : null,
            endDate: endTime ? `${new Date(endTime).getDate().toString().padStart(2, '0')}/${(new Date(endTime).getMonth() + 1).toString().padStart(2, '0')}` : null
          };
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: result
        });
      });
  };

  // Helper function để lấy tên ngày
  const getDayName = (dayOfWeek) => {
    const days = ['Chủ nhật', 'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7'];
    return days[dayOfWeek];
  };

  async.waterfall([checkParams, calculateTimeRange, getUserInfo, getMeetingSchedules], (err, data) => {
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};

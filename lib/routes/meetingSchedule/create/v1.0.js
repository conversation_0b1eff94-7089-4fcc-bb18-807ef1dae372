const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const MeetingScheduleModel = require('../../../models/meetingSchedule');
const UserModel = require('../../../models/user');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  const { startTime, endTime, officers, topic, content, attachments, units, address } = req.body;

  const checkParams = (next) => {
    if (!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    // Kiểm tra thời gian bắt đầu
    if (!startTime || typeof startTime !== 'number' || startTime <= 0) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: '<PERSON>ui lòng cung cấp thời gian bắt đầu hợp lệ'
        }
      });
    }

    // Kiểm tra thời gian kết thúc (có thể null)
    if (endTime !== null && endTime !== undefined && (typeof endTime !== 'number' || endTime <= 0)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Thời gian kết thúc không hợp lệ'
        }
      });
    }

    // Kiểm tra thời gian bắt đầu phải nhỏ hơn thời gian kết thúc (nếu có endTime)
    if (endTime && startTime >= endTime) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Thời gian bắt đầu phải nhỏ hơn thời gian kết thúc'
        }
      });
    }

    // Kiểm tra thời gian họp không được trong quá khứ
    if (startTime < Date.now()) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Thời gian họp không được trong quá khứ'
        }
      });
    }

    // Kiểm tra danh sách cán bộ
    const hasOfficers = officers && Array.isArray(officers) && officers.length > 0;
    const hasUnits = units && Array.isArray(units) && units.length > 0;
    if (!hasOfficers && !hasUnits) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp officers hoặc units tham gia'
        }
      });
    }

    // Validate ObjectId của officers
    const isValidObjectId = (id) => {
      return typeof id === 'string' && id.match(/^[0-9a-fA-F]{24}$/);
    };

    if (hasOfficers && !officers.every(officerId => isValidObjectId(officerId))) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'ID cán bộ không hợp lệ'
        }
      });
    }

    // Kiểm tra chủ đề cuộc họp
    if (!topic || typeof topic !== 'string' || topic.trim().length === 0) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp chủ đề cuộc họp'
        }
      });
    }

    // Validate attachments nếu có
    if (attachments && (!Array.isArray(attachments) || !attachments.every(att => typeof att === 'string'))) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Dữ liệu tệp đính kèm không hợp lệ'
        }
      });
    }

    next();
  };

  const validateOfficers = (next) => {
    // Kiểm tra tất cả officer IDs có tồn tại trong database không
    UserModel.find({
      _id: { $in: officers },
      status: 1 // Chỉ lấy user đang active
    }, (err, users) => {
      if (err) {
        return next(err);
      }

      const foundUserIds = users.map(user => user._id.toString());
      const missingOfficers = officers.filter(officerId => !foundUserIds.includes(officerId));

      if (missingOfficers.length > 0) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: `Không tìm thấy hoặc cán bộ không hoạt động với ID: ${missingOfficers.join(', ')}`
          }
        });
      }

      next();
    });
  };



  const createMeetingSchedule = (next) => {
    const meetingData = {
      startTime: startTime,
      officers: officers,
      topic: topic.trim(),
      assignedBy: userId,
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    // Thêm endTime nếu có
    if (endTime !== null && endTime !== undefined) {
      meetingData.endTime = endTime;
    }

    // Thêm nội dung nếu có
    if (content && typeof content === 'string' && content.trim().length > 0) {
      meetingData.content = content.trim();
    }

    // Thêm tệp đính kèm nếu có
    if (attachments && attachments.length > 0) {
      meetingData.attachments = attachments;
    }

    // Thêm đơn vị nếu có
    if (units && Array.isArray(units) && units.length > 0) {
      meetingData.units = units;
    } else {
      meetingData.units = [];
    }

    // Thêm địa chỉ nếu có
    if (address && typeof address === 'string' && address.trim().length > 0) {
      meetingData.address = address.trim();
    } 

    const newMeeting = new MeetingScheduleModel(meetingData);
    
    newMeeting.save((err, meeting) => {
      if (err) {
        return next(err);
      }

      // Populate thông tin officers và assignedBy
      MeetingScheduleModel.findById(meeting._id)
        .populate('officers', 'name idNumber avatar')
        .populate('assignedBy', 'name')
        .populate('units', 'name') 
        .exec((err, populatedMeeting) => {
          if (err) {
            return next(err);
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            message: {
              head: 'Thông báo',
              body: 'Tạo lịch họp thành công'
            },
            data: populatedMeeting
          });
        });
    });
  };

  async.waterfall([checkParams, validateOfficers, createMeetingSchedule], (err, data) => {
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};

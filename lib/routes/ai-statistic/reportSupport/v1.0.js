const _ = require('lodash');
const async = require('async');
const Joi = require('joi');


const statisticsService = require('../../../services/statisticsService');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const config = require('config');
const fetch = require('node-fetch');

const { Configuration, OpenAIApi } = require("openai");

const configuration = new Configuration({
  apiKey: config.ai.apiKey,
  basePath: config.ai.url,
  baseOptions: {
    headers: {
      'Authorization': `Bearer ${config.ai.apiKey}`,
    },
  },
});

const client = new OpenAIApi(configuration);


module.exports = (req, res) => {
  let {
    type = 'week',
    endTime,
    document,
    identity,
    protection,
    licensePlate
  } = req.body;
  let startTime;
  let statisticsData = {
  };
  let aiResponse = {};
  let timeText = '';
  let strategyText = '';
  let contextPrompt = '';

  const validateParams = (next) => {
    const options = ['3days', '7days', 'week', 'month', 'year'];
    if (type && !options.includes(type)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: `Tham số type không hợp lệ. Các giá trị hợp lệ là: ${options.join(', ')}`
        }
      });
    }
    if (!endTime) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: 'Tham số endTime là bắt buộc'
        }
      });
    }

    // Tính toán startTime dựa theo endTime và type
    const endDate = new Date(endTime);

    switch (type) {
      case '3days':
        strategyText = '3 ngày gần nhất';
        startTime = new Date(endDate);
        startTime.setDate(endDate.getDate() - 2);
        break;
      case '7days':
        strategyText = '7 ngày gần nhất';
        startTime = new Date(endDate);
        startTime.setDate(endDate.getDate() - 6);
        break;
      case 'week':
        strategyText = 'tuần hiện tại';
        startTime = new Date(endDate);
        // Tính ngày Thứ 2 của tuần (1 = Monday)
        const dayOfWeek = endDate.getDay(); // 0=Sunday, 1=Monday, ..., 6=Saturday
        const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Nếu là CN thì lùi 6 ngày, ngược lại lùi (dayOfWeek - 1) ngày
        startTime.setDate(endDate.getDate() - daysToMonday);
        break;
      case 'month':
        strategyText = 'của các tuần trong tháng hiện tại';
        startTime = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
        break;
      case 'year':
        strategyText = 'của các tháng trong năm nay';
        startTime = new Date(endDate.getFullYear(), 0, 1);
        break;
      default:
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Lỗi tham số',
            body: `Tham số type không hợp lệ`
          }
        });
    }

    // Tạo timeText từ startTime và endTime
    const formatDate = (date) => {
      const day = String(date.getDate()).padStart(2, '0');
      const month = String(date.getMonth() + 1).padStart(2, '0');
      return `${day}/${month}`;
    };

    timeText = `Từ ${formatDate(startTime)} đến ${formatDate(endDate)}`;

    next()
  };

  const getStatisticsData = (next) => {
    // Xử lý dữ liệu document nếu có
    if (document && Array.isArray(document)) {
      statisticsData.congVan = document.map(item => ({
        "Mốc": item.timeLabel,
        "Công văn đến": item.incomingDocuments,
        "Công văn đi": item.outgoingDocuments,
        "Công văn chưa trả lời": item.unansweredDocuments
      }));
    }

    // Xử lý dữ liệu identity nếu có
    if (identity && Array.isArray(identity)) {
      statisticsData.canCuocDinhDanh = identity.map(item => ({
        "Mốc": item.timeLabel,
        "Cấp căn cước": item.idCardIssued,
        "Định danh điện tử": item.identificationCount
      }));
    }

    // Xử lý dữ liệu protection nếu có
    if (protection && Array.isArray(protection)) {
      statisticsData.baoVeDoanCongTac = protection.map(item => ({
        "Mốc": item.timeLabel,
        "Số đoàn bảo vệ": item.protectionCount
      }));
    }

    // Xử lý dữ liệu licensePlate nếu có
    if (licensePlate && Array.isArray(licensePlate)) {
      statisticsData.dangKyBienSo = licensePlate.map(item => ({
        "Mốc": item.timeLabel,
        "Cấp biển số": item.registrationIssued,
        "Thu hồi biển số": item.registrationRevoked
      }));
    }

    console.log('Statistics Data:', statisticsData);
    next();
  };


  const callAIStream = (next) => {
    contextPrompt = `
Dữ liệu đầu vào là số liệu thống kê trong ${strategyText} về các mảng: công văn, cấp căn cước/định danh điện tử, bảo vệ đoàn công tác, đăng ký biển số xe ${timeText}.

Dữ liệu:
${JSON.stringify(statisticsData, null, 2)}

Nhiệm vụ của bạn:
1. **Phân tích thống kê & xu hướng**
   - Nêu tổng số, trung bình/mốc thời gian, biến động theo các mốc dữ liệu.
   - Chỉ ra mốc thời gian cao điểm và thấp điểm.

2. **Đánh giá hiệu quả xử lý**
   - Công văn: so sánh công văn đến, đi, trả lời.
   - Căn cước/định danh: so sánh mức độ triển khai, tỷ lệ định danh điện tử/căn cước.
   - Đăng ký biển số: phân tích quan hệ giữa số thu hồi và số cấp mới.
   - Bảo vệ đoàn công tác: xác định áp lực và phân bổ theo các mốc.

3. **Phát hiện bất thường & cảnh báo**
   - Mốc nào có số liệu bất thường so với trung bình?
   - Điểm nào cần lưu ý (ví dụ công văn đến nhiều nhưng trả lời ít).

4. **Khuyến nghị**
   - Đề xuất phân bổ nhân lực.
   - Gợi ý cải tiến quy trình.

Hãy trình bày kết quả thành một báo cáo phân tích chi tiết, văn phong hành chính – ngắn gọn, rõ ràng, có số liệu minh chứng.
Trả lời hoàn toan bằng tiếng Việt.`;

    const url = `${config.ai.url}/chat/completions`;
    const headers = {
      'Authorization': `Bearer ${config.ai.apiKey}`,
      'Content-Type': 'application/json'
    };
    const body = JSON.stringify({
      model: config.ai.model,
      messages: [
        { role: "system", content: "Bạn là chuyên gia phân tích dữ liệu cho hệ thống IOC Công an." },
        { role: "user", content: contextPrompt }
      ],
      max_tokens: 60000,
      temperature: 0.2,
      stream: true
    });

    fetch(url, { method: 'POST', headers, body })
      .then(response => {
        if (!response.ok || !response.body) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: 'Không thể stream từ OpenAI'
          });
        }

        res.setHeader('Content-Type', 'text/event-stream; charset=utf-8');
        res.setHeader('Cache-Control', 'no-cache');
        res.setHeader('Connection', 'keep-alive');

        let fullContent = '';
        response.body.on('data', (chunk) => {
          const lines = chunk.toString().split('\n');
          lines.forEach(line => {
            if (line.startsWith('data:')) {
              const data = line.replace('data: ', '').trim();
              if (data === '[DONE]') {
                res.end();
              } else {
                try {
                  const json = JSON.parse(data);
                  const delta = json.choices?.[0]?.delta?.content || '';
                  if (delta) {
                    fullContent += delta;
                    res.write(delta);
                  }
                } catch (e) {}
              }
            }
          });
        });
        response.body.on('end', () => {
          aiResponse = { content: fullContent };
          next();
        });
        response.body.on('error', (err) => {
          next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: 'Lỗi stream OpenAI',
            error: err
          });
        });
      })
      .catch(error => {
        next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR,
          error
        });
      });
  };

  const returnResult = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        report: aiResponse.content,
        timeText,
        contextPrompt,
        metadata: {
          statisticsData,
          aiModel: aiResponse.model,
          usage: aiResponse.usage,
          generatedAt: Date.now()
        }
      }
    });
  };

  async.waterfall([
    validateParams,
    getStatisticsData,
    callAIStream,
    returnResult
  ], (err, data) => {
    if (_.isError(err)) {
      global.logger && global.logger.logError([err], req.originalUrl, req.body);
      global.MailUtil && global.MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Nếu đã trả về qua stream thì không cần res.json nữa
    if (!res.headersSent) {
      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });
      res.json(data || err);
    }
  });
};

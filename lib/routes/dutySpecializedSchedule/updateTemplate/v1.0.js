const _ = require('lodash');
const async = require('async');
const mongoose = require('mongoose');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const DutySpecializedScheduleModel = require('../../../models/dutySpecializedSchedule');
const DutyShiftTemplateModel = require('../../../models/dutyShiftTemplate');
const DutyShiftModel = require('../../../models/dutyShift');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  let { _id, templateId, data } = req.body; // _id: ID của schedule, templateId: ID của template trong weeklyScheduleTemplate, data: array mới để thay thế hoàn toàn

  const checkParams = (next) => {
    if (!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    if (!_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp ID lịch trực'
        }
      });
    }

    if (!templateId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp ID template'
        }
      });
    }

    // Validate templateId là ObjectId hợp lệ
    if (!mongoose.Types.ObjectId.isValid(templateId)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Template ID không hợp lệ'
        }
      });
    }

    // Validate data
    if (!Array.isArray(data)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'data phải là một array'
        }
      });
    }

    // Validate từng phần tử trong data
    const validateData = (dataList) => {
      return dataList.every(shift => {
        // Kiểm tra các trường bắt buộc
        const hasRequiredFields = typeof shift.startTime === 'number' && 
               typeof shift.endTime === 'number' &&
               typeof shift.forLeader === 'boolean' &&
               shift.startTime >= 0 &&
               shift.endTime > 0 &&
               shift.startTime < shift.endTime;
        
        // Ràng buộc: nếu forLeader = true thì không được có unit
        if (shift.forLeader === true && shift.unit) {
          return false;
        }
        
        // Kiểm tra unit nếu có (unit phải là string ObjectId hợp lệ hoặc ObjectId)
        if (shift.unit && !mongoose.Types.ObjectId.isValid(shift.unit)) {
          return false;
        }
        
        return hasRequiredFields;
      });
    };

    if (!validateData(data)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Cấu trúc data không hợp lệ. Mỗi phần tử phải có startTime, endTime, forLeader và unit (nếu có) phải là ObjectId hợp lệ. Lưu ý: Ca trực dành cho leader (forLeader=true) không được có unit'
        }
      });
    }

    next();
  };

  const validateTimeConflicts = (next) => {
    // Kiểm tra xung đột thời gian trong data
    const conflicts = [];
    
    // Kiểm tra xung đột trong cùng một ngày
    for (let i = 0; i < data.length; i++) {
      if(data[i].forLeader) {
        continue; // Bỏ qua ca trực dành cho leader
      }
      for (let j = i + 1; j < data.length; j++) {
        if(data[j].forLeader) {
          continue; // Bỏ qua ca trực dành cho leader
        }
        
        const shift1 = data[i];
        const shift2 = data[j];
        
        // Kiểm tra trùng lặp thời gian (chỉ cần so sánh số milliseconds trong ngày)
        const isConflict = (
          (shift1.startTime < shift2.endTime && shift1.endTime > shift2.startTime)
        );
        
        if (isConflict) {
          const formatTime = (milliseconds) => {
            const hours = Math.floor(milliseconds / 3600000);
            const minutes = Math.floor((milliseconds % 3600000) / 60000);
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
          };
          
          conflicts.push({
            shift1: `${formatTime(shift1.startTime)} - ${formatTime(shift1.endTime)}`,
            shift2: `${formatTime(shift2.startTime)} - ${formatTime(shift2.endTime)}`
          });
        }
      }
    }
    
    if (conflicts.length > 0) {
      const conflictMessages = conflicts.map(conflict => 
        `${conflict.shift1} và ${conflict.shift2}`
      ).join('; ');
      
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: `Có xung đột thời gian trong ca trực: ${conflictMessages}`
        }
      });
    }
    
    next();
  };

  const checkExistingDutyShifts = (next) => {
    // Kiểm tra xem có ca trực nào đang tồn tại không
    DutyShiftModel.findOne({
      dutySpecializedSchedule: _id,
      status: 1
    })
    .lean()
    .exec((err, existingShift) => {
      if (err) {
        return next(err);
      }

      if (existingShift) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Cập nhật không thành công do đang có cán bộ đã được sắp xếp lịch trực. Vui lòng xóa lịch trực hiện tại để cập nhật'
          }
        });
      }

      next();
    });
  };

  const updateWeeklyScheduleTemplate = (next) => {
    // Lấy dữ liệu hiện tại
    DutySpecializedScheduleModel.findOne({ _id: _id, status: 1 })
      .lean()
      .exec((err, schedule) => {
        if (err) {
          return next(err);
        }

        if (!schedule) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Lịch trực không tồn tại hoặc đã bị xóa'
            }
          });
        }

        // Tìm template cần update
        const templateIndex = schedule.weeklyScheduleTemplate.findIndex(
          template => template._id.toString() === templateId
        );

        if (templateIndex === -1) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Template không tồn tại trong lịch trực'
            }
          });
        }

        // Tạo bản copy của weeklyScheduleTemplate để update
        const updatedWeeklyScheduleTemplate = [...schedule.weeklyScheduleTemplate];
        const targetTemplate = { ...updatedWeeklyScheduleTemplate[templateIndex] };
        
        // Tính toán thời gian thực tế cho template này dựa trên ngày của template
        const scheduleStartDate = new Date(schedule.startTime);
        const dayOfWeek = scheduleStartDate.getDay(); // 0: Chủ nhật, 1: Thứ 2, ..., 6: Thứ 7
        const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
        const mondayDate = new Date(scheduleStartDate);
        mondayDate.setDate(scheduleStartDate.getDate() - daysToMonday);
        mondayDate.setHours(0, 0, 0, 0);

        // Xác định ngày của template hiện tại (dựa trên templateIndex)
        const templateDate = new Date(mondayDate);
        templateDate.setDate(mondayDate.getDate() + templateIndex);
        const dayStartTimestamp = templateDate.getTime();

        // Thay thế hoàn toàn data với thời gian thực tế
        const newData = data.map(shift => {
          const shiftData = {
            startTime: dayStartTimestamp + shift.startTime,
            endTime: dayStartTimestamp + shift.endTime,
            forLeader: shift.forLeader
          };
          
          // Thêm unit nếu có
          if (shift.unit) {
            shiftData.unit = shift.unit;
          }
          
          return shiftData;
        });

        // Cập nhật lại template với data mới
        targetTemplate.data = newData;
        updatedWeeklyScheduleTemplate[templateIndex] = targetTemplate;

        // Cập nhật vào database
        DutySpecializedScheduleModel.findOneAndUpdate(
          { _id: _id, status: 1 },
          { 
            $set: { 
              weeklyScheduleTemplate: updatedWeeklyScheduleTemplate,
              updatedAt: Date.now()
            }
          },
          { new: true }
        )
        .lean()
        .exec((err, updatedSchedule) => {
          if (err) {
            return next(err);
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: updatedSchedule
          });
        });
      });
  };

  async.waterfall([checkParams, validateTimeConflicts, updateWeeklyScheduleTemplate], (err, data) => {
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};

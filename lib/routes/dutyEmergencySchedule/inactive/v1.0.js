const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const DutyEmergencyScheduleModel = require('../../../models/dutyEmergencySchedule');
const DutyShiftModel = require('../../../models/dutyShift');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  const { _id } = req.body;

  const checkParams = (next) => {
    if (!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    if (!_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: '<PERSON>ui lòng cung cấp ID lịch trực đột xuất'
        }
      });
    }

    next();
  };

  const inactivedutyEmergencySchedule = (next) => {
    const updateData = {
      status: 0,
      updatedAt: Date.now()
    };

    DutyEmergencyScheduleModel.findOneAndUpdate(
      { _id: _id, status: 1 },
      { $set: updateData },
      { new: true }
    )
    .lean()
    .exec((err, schedule) => {
      if (err) {
        return next(err);
      }

      if (!schedule) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Lịch trực đột xuất không tồn tại hoặc đã bị xóa'
          }
        });
      }

      // Cập nhật tất cả ca trực liên quan về status = 2
      DutyShiftModel.updateMany(
        { 
          dutyEmergencySchedule: _id,
          status: { $in: [0, 1] } // Chỉ cập nhật các ca trực đang active hoặc chưa xác nhận
        },
        { 
          $set: { 
            status: 2,
            updatedAt: Date.now()
          } 
        }
      )
      .exec((err, updateResult) => {
        if (err) {
          console.error('Lỗi khi cập nhật status ca trực:', err);
          // Không return error vì lịch thường trực chiến đấu đã được xóa thành công
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          message: {
            head: 'Thông báo',
            body: 'Xóa lịch trực đột xuất thành công'
          },
          data: {
            ...schedule,
            affectedDutyShifts: updateResult ? updateResult.modifiedCount : 0
          }
        });
      });
    });
  };

  async.waterfall([checkParams, inactivedutyEmergencySchedule], (err, data) => {
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};

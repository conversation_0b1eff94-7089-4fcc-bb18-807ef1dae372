const _ = require('lodash');
const async = require('async');
const mongoose = require('mongoose');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const DutyEmergencyScheduleModel = require('../../../models/dutyEmergencySchedule');
const DutyShiftTemplateModel = require('../../../models/dutyShiftTemplate');
const UserModel = require('../../../models/user');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  const { weekType } = req.body; // 'previous', 'current', 'next'
  const currentDate = new Date();
  let targetDate;

  const checkParams = (next) => {
    if (!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }
    
    // Validate weekType parameter
    if (!weekType) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp loại tuần (previous, current, next)'
        }
      });
    }
    
    const validWeekTypes = ['previous', 'current', 'next'];
    if (weekType && !validWeekTypes.includes(weekType)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }
    
    next();
  };

  const getdutyEmergencySchedule = (next) => {
    // Tính toán ngày cần tìm dựa trên weekType
    targetDate = new Date(currentDate);
    
    if (weekType === 'previous') {
      // Tuần trước: trừ 7 ngày
      targetDate.setDate(currentDate.getDate() - 7);
    } else if (weekType === 'next') {
      // Tuần kế tiếp: cộng 7 ngày
      targetDate.setDate(currentDate.getDate() + 7);
    }
    // weekType === 'current' hoặc undefined thì dùng currentDate

    // Tìm bản ghi có status = 1 và thời gian target nằm trong khoảng startTime và endTime
    DutyEmergencyScheduleModel.findOne({
      status: 1,
      startTime: { $lte: targetDate.getTime() },
      endTime: { $gte: targetDate.getTime() }
    })
    .populate({
      path: 'weeklySchedule',
      select: 'name startTime endTime officer unit',
      populate: {
        path: 'officer',
        select: 'name avatar',
      }
    })
    .populate({
      path: 'weeklyScheduleTemplate.data.unit',
      select: 'name'
    })
    .lean()
    .exec((err, schedule) => {
      if (err) {
        return next(err);
      }

      if (schedule) {
        // Nếu tìm thấy bản ghi, trả về để tính toán maxOfficer
        return next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: schedule
        });
      }

      // Nếu không tìm thấy
      if (weekType === 'previous') {
        // Với tuần trước, nếu không có thì trả về null
        return next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: null
        });
      }

      // Với tuần hiện tại hoặc tuần kế tiếp, tạo mới bản ghi mặc định
      next(null, null);
    });
  };

  const createDefaultSchedule = (result, next) => {
    if (result) {
      return next(null, result);
    }

    // Chỉ tạo bản ghi mặc định cho tuần hiện tại hoặc tuần kế tiếp
    // Không tạo cho tuần trước
    if (weekType === 'previous') {
      return next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        data: null
      });
    }

    // Lấy template mặc định từ dutyShiftTemplate
    DutyShiftTemplateModel.findOne({
      status: 1,
      source: 'emergency'
    })
    .lean()
    .exec((err, template) => {
      if (err) {
        return next(err);
      }

      let defaultWeeklyTemplate = [];
      
      // Tính thời gian đầu ngày thứ 2
      const getWeekStartTime = () => {
        const date = new Date(targetDate);
        const day = date.getDay();
        const diff = date.getDate() - day + (day === 0 ? -6 : 1); // Lấy thứ 2 đầu tuần
        const mondayDate = new Date(date.setDate(diff));
        mondayDate.setHours(0, 0, 0, 0); // Set về 0h00 ngày thứ 2
        return mondayDate.getTime();
      };

      const weekStartTime = getWeekStartTime();
      if (template && template.template) {
        // Sử dụng template từ database với cấu trúc mới
        defaultWeeklyTemplate = template.template.map((dayTemplate, index) => {
          // Tính thời gian đầu ngày dựa trên index
          const dayStartTime = weekStartTime + (index * 24 * 60 * 60 * 1000);
          
          return {
            name: dayTemplate.name,
            dayStartTime,
            data: dayTemplate.data.map(shift => ({
              unit: shift.unit || null, // Thêm trường unit
              requiredOfficer: shift.requiredOfficer || 0, // Số lượng cán bộ bắt buộc
              startTime: dayStartTime + shift.startTime,
              endTime: dayStartTime + shift.endTime
            }))
          };
        });
      } else {
        // Fallback template nếu không tìm thấy
        const dayNames = ['Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7', 'Chủ nhật'];
        
        defaultWeeklyTemplate = dayNames.map((dayName, index) => {
          // Tính thời gian đầu ngày cho ngày hiện tại (thứ 2 + index ngày)
          const dayStartTime = weekStartTime + (index * 24 * 60 * 60 * 1000);
          
          return {
            name: dayName,
            dayStartTime,
            data: [] 
          };
        });
      }
      
      const defaultSchedule = new DutyEmergencyScheduleModel({
        name: 'Lịch trực đột xuất',
        description: 'Lịch trực đột xuất cho các cán bộ',
        startTime: (() => {
        const date = new Date(targetDate);
        const day = date.getDay();
        const diff = date.getDate() - day + (day === 0 ? -6 : 1); // Lấy thứ 2 đầu tuần
        const mondayDate = new Date(date.setDate(diff));
        mondayDate.setHours(0, 0, 0, 0); // Set về 0h00 ngày thứ 2
        return mondayDate.getTime();
        })(),
        endTime: (() => {
        const date = new Date(targetDate);
        const day = date.getDay();
        const diff = date.getDate() - day + (day === 0 ? 0 : 7); // Lấy chủ nhật cuối tuần
        const sundayDate = new Date(date.setDate(diff));
        sundayDate.setHours(23, 59, 59, 999); // Set về 23h59 ngày chủ nhật (gần 24h)
        return sundayDate.getTime() + 1;
        })(),
        dutyType: 'emergency',
        dutyName: 'Trực đột xuất',
        location: '',
        weeklyScheduleTemplate: defaultWeeklyTemplate,
        weeklySchedule: [],
        status: 1,
        createdBy: userId
      });

      defaultSchedule.save((err, savedSchedule) => {
        if (err) {
          return next(err);
        }

        // Populate thông tin unit sau khi save để trả về đầy đủ thông tin
        DutyEmergencyScheduleModel.findById(savedSchedule._id)
          .populate({
            path: 'weeklySchedule',
            select: 'name startTime endTime officer unit',
            populate: {
              path: 'officer',
              select: 'name avatar',
            }
          })
          .populate({
            path: 'weeklyScheduleTemplate.data.unit',
            select: 'name'
          })
          .lean()
          .exec((err, populatedSchedule) => {
            if (err) {
              return next(err);
            }

            next(null, {
              code: CONSTANTS.CODE.SUCCESS,
              data: populatedSchedule
            });
          });
      });
    });
  };

  const calculateMaxOfficer = (result, next) => {
    if (!result || !result.data || !result.data.weeklyScheduleTemplate) {
      return next(null, result);
    }

    // Lấy tất cả các unit ID duy nhất từ weeklyScheduleTemplate
    const unitIds = [];
    result.data.weeklyScheduleTemplate.forEach(dayTemplate => {
      if (dayTemplate.data && Array.isArray(dayTemplate.data)) {
        dayTemplate.data.forEach(shift => {
          if (shift.unit) {
            // Xử lý cả trường hợp unit là object (đã populate) và string ID
            const unitId = shift.unit._id ? shift.unit._id.toString() : shift.unit.toString();
            if (!unitIds.includes(unitId)) {
              unitIds.push(unitId);
            }
          }
        });
      }
    });

    if (unitIds.length === 0) {
      return next(null, result);
    }

    // Đếm số lượng user cho mỗi unit
    const unitUserCounts = {};

    async.each(unitIds, (unitId, callback) => {
      UserModel.countDocuments({
        units: new mongoose.Types.ObjectId(unitId),
        status: 1
      }, (err, count) => {
        if (err) {
          return callback(err);
        }
        unitUserCounts[unitId] = count || 0;
        callback();
      });
    }, (err) => {
      if (err) {
        return next(err);
      }

      // Cập nhật maxOfficer cho từng shift trong weeklyScheduleTemplate
      result.data.weeklyScheduleTemplate.forEach(dayTemplate => {
        if (dayTemplate.data && Array.isArray(dayTemplate.data)) {
          dayTemplate.data.forEach(shift => {
            if (shift.unit) {
              const unitId = shift.unit._id ? shift.unit._id.toString() : shift.unit.toString();
              shift.maxOfficer = unitUserCounts[unitId] || 0;
            } else {
              shift.maxOfficer = 0;
            }
          });
        }
      });

      next(null, result);
    });
  };

  async.waterfall([checkParams, getdutyEmergencySchedule, createDefaultSchedule, calculateMaxOfficer], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    if (err && _.isError(err)) {
      data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      };
    }

    res.json(data || err);
  });
};

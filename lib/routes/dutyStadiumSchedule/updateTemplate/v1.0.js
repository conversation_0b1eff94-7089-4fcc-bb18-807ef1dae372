const _ = require('lodash');
const async = require('async');
const mongoose = require('mongoose');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const DutyStadiumScheduleModel = require('../../../models/dutyStadiumSchedule');
const DutyShiftModel = require('../../../models/dutyShift');
const UserModel = require('../../../models/user');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  const { _id, templateId, data } = req.body; // _id: ID của schedule, templateId: ID của phần tử trong weeklyScheduleTemplate, data: mảng data mới để cập nhật

  const checkParams = (next) => {
    if (!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    if (!_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp ID lịch Bảo vệ sân bóng đá'
        }
      });
    }

    if (!templateId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp ID của phần tử template'
        }
      });
    }

    if (!Array.isArray(data)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'data phải là một array'
        }
      });
    }

    // Validate cấu trúc data
    const validateData = (dataList) => {
      return dataList.every(dataItem => {
        // Kiểm tra cấu trúc cơ bản
        if (!dataItem.schedule || !Array.isArray(dataItem.schedule)) {
          return false;
        }
        
        // Kiểm tra từng schedule item
        return dataItem.schedule.every(scheduleItem => {
          return typeof scheduleItem.requiredOfficer === 'number' && 
                 scheduleItem.requiredOfficer >= 0;
        });
      });
    };

    if (!validateData(data)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Cấu trúc data không hợp lệ - requiredOfficer trong schedule phải là số >= 0'
        }
      });
    }

    next();
  };

  const validateMaxOfficer = (next) => {
    // Lấy tất cả các unit IDs từ data để validate
    const unitIds = [];
    const unitRequiredOfficers = {};
    
    data.forEach(dataItem => {
      if (dataItem.schedule && Array.isArray(dataItem.schedule)) {
        dataItem.schedule.forEach(scheduleItem => {
          if (scheduleItem.unit) {
            const unitId = scheduleItem.unit.toString();
            if (!unitIds.includes(unitId)) {
              unitIds.push(unitId);
            }
            // Lưu requiredOfficer cao nhất cho mỗi unit
            if (!unitRequiredOfficers[unitId] || unitRequiredOfficers[unitId] < scheduleItem.requiredOfficer) {
              unitRequiredOfficers[unitId] = scheduleItem.requiredOfficer;
            }
          }
        });
      }
    });

    if (unitIds.length === 0) {
      return next();
    }

    // Kiểm tra từng unit
    async.each(unitIds, (unitId, callback) => {
      // Validate ObjectId format trước khi tạo ObjectId
      if (!mongoose.Types.ObjectId.isValid(unitId)) {
        return callback({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: `Unit ID không hợp lệ: ${unitId}`
          }
        });
      }
      
      UserModel.countDocuments({
        units: new mongoose.Types.ObjectId(unitId),
        status: 1
      }, (err, maxOfficer) => {
        if (err) {
          return callback(err);
        }

        const requiredOfficer = unitRequiredOfficers[unitId];
        if (requiredOfficer > maxOfficer) {
          return callback({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: `Unit ${unitId}: Số lượng cán bộ yêu cầu (${requiredOfficer}) không thể vượt quá số lượng cán bộ tối đa của đơn vị (${maxOfficer})`
            }
          });
        }

        callback();
      });
    }, (err) => {
      next(err);
    });
  };



  const updateWeeklyScheduleTemplate = (next) => {
    // Lấy dữ liệu hiện tại
    DutyStadiumScheduleModel.findOne({ _id: _id, status: 1 })
      .lean()
      .exec((err, schedule) => {
        if (err) {
          return next(err);
        }

        if (!schedule) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'lịch Bảo vệ sân bóng đá không tồn tại hoặc đã bị xóa'
            }
          });
        }

        // Tạo bản sao của weeklyScheduleTemplate hiện tại
        let updatedTemplate = [...(schedule.weeklyScheduleTemplate || [])];

        // Tìm và cập nhật phần tử có templateId khớp
        const index = updatedTemplate.findIndex(item => item._id.toString() === templateId);
        if (index === -1) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Không tìm thấy phần tử template với ID đã cung cấp'
            }
          });
        }

        // Cập nhật toàn bộ data, giữ nguyên _id và name
        const currentItem = updatedTemplate[index];
        updatedTemplate[index] = {
          ...currentItem,
          data: data
        };

        // Cập nhật vào database
        DutyStadiumScheduleModel.findOneAndUpdate(
          { _id: _id, status: 1 },
          { 
            $set: { 
              weeklyScheduleTemplate: updatedTemplate,
              updatedAt: Date.now()
            }
          },
          { new: true }
        )
        .lean()
        .exec((err, updatedSchedule) => {
          if (err) {
            return next(err);
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            message: {
              head: 'Thông báo',
              body: 'Cập nhật lịch Bảo vệ sân bóng đá thành công'
            },
            data: updatedSchedule
          });
        });
      });
  };

  async.waterfall([checkParams, validateMaxOfficer, updateWeeklyScheduleTemplate], (err, data) => {
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};

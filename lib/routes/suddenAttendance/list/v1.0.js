const _ = require('lodash');
const async = require('async');
const Joi = require('joi');

const suddenAttendanceService = require('../../../services/suddenAttendanceService');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

/**
 * API lấy danh sách phiên chấm công đột xuất cho cán bộ
 * POST /api/v1.0/sudden-attendance/list
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const {
    status,
    startDate,
    endDate
  } = req.body;

  let result;

  const validateParams = (next) => {
    const schema = Joi.object({
      status: Joi.string().valid('scheduled', 'active', 'completed', 'cancelled').optional(),
      startDate: Joi.string().optional(),
      endDate: Joi.string().optional()
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const getUserSessions = (next) => {
    try {
      const filters = {
        status,
        startDate,
        endDate
      };

      suddenAttendanceService.getUserSessions(userId, filters)
        .then((res) => {
          if (!res || !res.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: res.message || MESSAGES.SYSTEM.ERROR
            });
          }

          result = res;
          next();
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: result.data
    });
  };

  async.waterfall([
    validateParams,
    getUserSessions,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};

const _ = require('lodash')
const async = require('async')
const mongoose = require('mongoose')
const MissionModel = require('../../../models/mission')
const MissionLogModel = require('../../../models/missionLog')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const logger = require('../../../logger')
const MailUtil = require('../../../util/mail')

module.exports = (req, res) => {
  const {
   _id
  } = req.body
  const userId = req.user.id;
  const validateParams = (next) => {
    if (!_id || !_id.trim()) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'ID nhiệm vụ là bắt buộc'
        }
      });
    }

    next();
  }

  const getMission = (next) => {
    MissionModel.findById(_id.trim())
      .populate('createdBy', 'name')
      .populate('assignInfo.unit', 'name')
      .populate('assignInfo.users', 'name phones idNumber avatar')
      .then(mission => {
        if (!mission) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: {
              head: 'Thông báo',
              body: 'Không tìm thấy nhiệm vụ'
            }
          });
        }

        // Kiểm tra trạng thái nhiệm vụ phải là IN_PROGRESS (2)
        if (mission.status !== 2) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: {
              head: 'Thông báo',
              body: 'Chỉ có thể từ chối nhiệm vụ đang thực hiện'
            }
          });
        }

        // Kiểm tra user đã nhận nhiệm vụ chưa - nếu đã nhận thì không thể từ chối
        const hasAccepted = mission.users.some(user => user.toString() === userId.toString());
        if (hasAccepted) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: {
              head: 'Thông báo',
              body: 'Không thể từ chối nhiệm vụ đã nhận'
            }
          });
        }

        next(null, mission);
      })
      .catch(err => next(err));
  }

  const updateMissionRejects = (mission, next) => {
    // Kiểm tra user đã từ chối chưa (so sánh string id)
    const hasRejected = mission.rejects.some(reject => reject.toString() === userId.toString());
    if (hasRejected) {
      return next({
        code: CONSTANTS.CODE.FAIL,
        message: {
          head: 'Thông báo',
          body: 'Bạn đã từ chối nhiệm vụ này rồi'
        }
      });
    }

    // Thêm userId vào danh sách rejects
    const userObjectId = mongoose.Types.ObjectId(userId);
    mission.rejects.push(userObjectId);
    mission.updatedAt = Date.now();

    mission.save()
      .then(updatedMission => {
        next(null, updatedMission);
      })
      .catch(err => next(err));
  }

  const createMissionLog = (mission, next) => {
    const missionLog = new MissionLogModel({
      user: userId,
      mission: mission._id,
      message: 'Từ chối nhiệm vụ',
      action: 3, // Action code cho việc từ chối nhiệm vụ
      data: {
        missionCode: mission.code,
        missionName: mission.name,
        rejectedAt: Date.now()
      }
    });

    missionLog.save()
      .then(() => {
        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          message: {
            head: 'Thông báo',
            body: 'Từ chối nhiệm vụ thành công'
          },
          data: mission
        });
      })
      .catch(err => next(err));
  }

  async.waterfall([
    validateParams,
    getMission,
    updateMissionRejects,
    createMissionLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body)
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    })

    res.json(data || err)
  })
}
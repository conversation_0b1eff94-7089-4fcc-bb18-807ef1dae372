const _ = require('lodash')
const async = require('async')
const MissionModel = require('../../../models/mission')
const MissionLogModel = require('../../../models/missionLog')
const UserModel = require('../../../models/user')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')

module.exports = (req, res) => {
  const {
   _id
  } = req.body
  const userId = req.user.id;
  const validateParams = (next) => {
    if (!_id || !_id.trim()) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'ID nhiệm vụ là bắt buộc'
        }
      });
    }

    next();
  }

  const getMission = (next) => {
    MissionModel.findById(_id.trim())
      .populate('createdBy', 'name')
      .populate('assignInfo.unit', 'name')
      .populate('assignInfo.users', 'name phones idNumber avatar')
      .then(mission => {
        if (!mission) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Không tìm thấy nhiệm vụ'
            }
          });
        }

        // B1: Kiểm tra trạng thái nhiệm vụ có hợp lệ = 2
        if (mission.status !== 2) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Nhiệm vụ không ở trạng thái có thể nhận'
            }
          });
        }

        // Kiểm tra user đã nhận nhiệm vụ chưa
        const userAlreadyAccepted = mission.assignInfo.some(assign => 
          assign.users.some(user => user._id.toString() === userId)
        );

        if (userAlreadyAccepted) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Bạn đã nhận nhiệm vụ này rồi'
            }
          });
        }

        next(null, mission);
      })
      .catch(err => next(err));
  }

  const getUserInfo = (mission, next) => {
    UserModel.findById(userId)
      .populate('units', 'name')
      .then(user => {
        if (!user) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: {
              head: 'Thông báo',
              body: 'Không tìm thấy thông tin người dùng'
            }
          });
        }

        // B2: Lấy thông tin unit của user: user.units[1]
        if (!user.units || !user.units[1]) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Cán bộ không ở tổ nào'
            }
          });
        }

        next(null, mission, user);
      })
      .catch(err => next(err));
  }

  const updateMissionAssign = (mission, user, next) => {
    const userUnit = user.units[1];

    // B3: Cập nhật assignInfo, users của mission
    // Tìm assignInfo cho unit của user
    let assignInfoIndex = mission.assignInfo.findIndex(assign => 
      assign.unit._id.toString() === userUnit._id.toString()
    );

    if (assignInfoIndex === -1) {
      // Nếu assignInfo không có unit của user thì báo lỗi
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Nhiệm vụ này không được phân công cho tổ của bạn'
        }
      });
    }

    // Thêm user vào danh sách users của assignInfo
    mission.assignInfo[assignInfoIndex].users.push(userId);
    mission.assignInfo[assignInfoIndex].numberOfUsers = mission.assignInfo[assignInfoIndex].users.length;

    // Thêm user vào danh sách users chung của mission
    if (!mission.users.includes(userId)) {
      mission.users.push(userId);
    }

    mission.updatedAt = Date.now();

    mission.save()
      .then(updatedMission => {
        next(null, updatedMission, user);
      })
      .catch(err => next(err));
  }

  const createMissionLog = (mission, user, next) => {
    // B4: Ghi vào missionLog
    const missionLog = new MissionLogModel({
      user: userId,
      mission: mission._id,
      message: `${user.name} đã nhận nhiệm vụ "${mission.name}"`,
      action: 1, // 1: accept mission
      data: {
        missionId: mission._id,
        userId: userId,
        userUnit: user.units[1]
      }
    });

    missionLog.save()
      .then(() => {
        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          message: {
            head: 'Thông báo',
            body: 'Nhận nhiệm vụ thành công'
          },
          data: mission
        });
      })
      .catch(err => next(err));
  }

  async.waterfall([
    validateParams,
    getMission,
    getUserInfo,
    updateMissionAssign,
    createMissionLog
  ], (err, data) => {
    if (_.isError(err)) {
      global.logger && global.logger.logError([err], req.originalUrl, req.body)
      global.MailUtil && global.MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    })

    res.json(data || err)
  })
}
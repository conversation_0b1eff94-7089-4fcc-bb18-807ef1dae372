const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
const mongoose = require('mongoose');
const JobTypeModel = require('../../../../models/jobType');
const ReportModel = require('../../../../models/report');
const ReportDetailModel = require('../../../../models/reportDetail');
const AreaModel = require('../../../../models/area');

const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

/**
 * API thống kê theo lĩnh vực trong khu vực
 * POST /api/v1.0/statistics/reports-summary
 *
 * Trả về thống kê theo các lĩnh vực cụ thể trong khu vực
 * Các lĩnh vực: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> thuẫn, <PERSON><PERSON>, TNGT
 * Sử dụng ReportDetailModel và lấy thông tin từ jobType.quickReportTemplate.requiredFields
 * Dữ liệu này được sử dụng để hiển thị dashboard thống kê theo lĩnh vực
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const areaId = req.body.area;

  const {
    type = 'week',
    endTime
  } = req.body;

  let startTime;
  let areaInfo;

  let result;

  /**
   * Validate tham số đầu vào
   */
  const validateParams = (next) => {
    const options = ['3days','7days','week','month','year'];
    
    if(type && !options.includes(type)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: `Tham số type không hợp lệ. Các giá trị hợp lệ là: ${options.join(', ')}`
        }
      });
    }
    if(!endTime) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: 'Tham số endTime là bắt buộc'
        }
      });
    }
    if(!areaId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: 'Tham số area là bắt buộc'
        }
      });
    }
    if(!mongoose.Types.ObjectId.isValid(areaId)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: 'Tham số area không hợp lệ'
        }
      });
    }
    next();
  };

  const checkAreaExists = (next) => {
    // Kiểm tra xem area có tồn tại không
    AreaModel.findById(areaId)
      .then((area) => {
        if (!area) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,    
            message: {
              head: 'Lỗi tham số',
              body: 'Khu vực không tồn tại'
            }
          });
        }
        // Lưu thông tin area để sử dụng sau
        areaInfo = area;
        next();
      })
      .catch((err) => {
        next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: {
            head: 'Lỗi hệ thống',
            body: 'Đã xảy ra lỗi khi kiểm tra khu vực'
          }
        });
      });
  };

  const calculateTimeRange = (next) => {
    const endDate = new Date(endTime);
    
    switch (type) {
      case '3days':
        startTime = new Date(endDate);
        startTime.setDate(endDate.getDate() - 2);
        break;
      case '7days':
        startTime = new Date(endDate);
        startTime.setDate(endDate.getDate() - 6);
        break;
      case 'week':
        startTime = new Date(endDate);
        // Tính ngày Thứ 2 của tuần (1 = Monday)
        const dayOfWeek = endDate.getDay(); // 0=Sunday, 1=Monday, ..., 6=Saturday
        const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Nếu là CN thì lùi 6 ngày, ngược lại lùi (dayOfWeek - 1) ngày
        startTime.setDate(endDate.getDate() - daysToMonday);
        break;
      case 'month':
        startTime = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
        break;
      case 'year':
        startTime = new Date(endDate.getFullYear(), 0, 1);
        break;
      default:
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Lỗi tham số',
            body: `Tham số type không hợp lệ`
          }
        });
    }
    startTime.setHours(0, 0, 0, 0);

    next();
  };

  const getStatisticDocument = (next) => {
    // B1: Query JobTypeModel để lấy các jobType có chartTypes = 'heatmap'
    JobTypeModel.find({ status: 1, 'quickReportTemplate.chartTypes': 'heatmap' }, '_id name quickReportTemplate')
      .lean()
      .then((jobTypes) => {
        if (!jobTypes || jobTypes.length === 0) {
          return next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            message: MESSAGES.SYSTEM.SUCCESS,
            data: {
              metrics: []
            }
          });
        }

        const jobTypeIds = jobTypes.map(jt => jt._id);

        // B2: Query ReportDetailModel để lấy thống kê theo lĩnh vực
        const reportQuery = {
          jobType: { $in: jobTypeIds },
          areas: { $exists: true, $ne: [] },
          'areas.0': mongoose.Types.ObjectId(areaId),
          time: {
            $gte: startTime.getTime(),
            $lte: new Date(endTime).getTime()
          }
        };

        ReportDetailModel.aggregate([
          { $match: reportQuery },
          {
            $group: {
              _id: '$jobType',
              count: { $sum: 1 }
            }
          }
        ])
          .then((groupedResults) => {
            // Khởi tạo các biến đếm
            let totalFireCount = 0;
            let totalConflictCount = 0;
            let totalViolateCount = 0;
            let totalAccidentCount = 0;
            let totalSummaryCount = 0;

            // Xử lý từng jobType để đếm theo lĩnh vực
            groupedResults.forEach(group => {
              const jobTypeId = group._id;
              const count = group.count;
              totalSummaryCount += count;

              // Tìm jobType info từ danh sách đã query
              const jobTypeInfo = jobTypes.find(jt => jt._id.toString() === jobTypeId.toString());
              if (jobTypeInfo) {
                const requiredFields = jobTypeInfo.quickReportTemplate?.requiredFields;
                if (requiredFields && requiredFields.length > 0) {
                  const fieldName = requiredFields[0]; // Đây là string tên trường
                  
                  if (fieldName === 'fireCount') {
                    totalFireCount += count;
                  } else if (fieldName === 'conflictCount') {
                    totalConflictCount += count;
                  } else if (fieldName === 'violateCount') {
                    totalViolateCount += count;
                  } else if (fieldName === 'accidentCount') {
                    totalAccidentCount += count;
                  }
                }
              }
            });

            const stats = {
              totalFireCount,
              totalConflictCount,
              totalViolateCount,
              totalAccidentCount,
              totalSummaryCount
            };

            // Tạo metrics theo các lĩnh vực
            const metrics = [
              {
                category: 'fire',
                categoryName: 'Số vụ cháy',
                count: stats.totalFireCount,
                color: '#D30500'
              },
              {
                category: 'conflict',
                categoryName: 'Số vụ mâu thuẫn',
                count: stats.totalConflictCount,
                color: '#FF9600'
              },
              {
                category: 'violate',
                categoryName: 'Số vụ vi phạm',
                count: stats.totalViolateCount,
                color: '#FFD042'
              },
              {
                category: 'accident',
                categoryName: 'Số vụ TNGT',
                count: stats.totalAccidentCount,
                color: '#007CFE'
              }
            ].sort((a, b) => b.count - a.count); // Sắp xếp theo số vụ việc giảm dần

            // Tính tổng tất cả các lĩnh vực
            const totalByCategory = stats.totalFireCount + stats.totalConflictCount + 
                                   stats.totalViolateCount + stats.totalAccidentCount;

            result = {
              code: CONSTANTS.CODE.SUCCESS,
              data: {
                title: `Thống kê theo lĩnh vực tại ${areaInfo.name}`,
                summary: {
                  totalReports: stats.totalSummaryCount,
                  totalByCategory: totalByCategory,
                  areaName: areaInfo.name
                },
                chartConfig: {
                  colors: {
                    fire: '#D30500',
                    conflict: '#FF9600',
                    violate: '#FFD042',
                    accident: '#007CFE'
                  },
                  labels: {
                    fire: 'Số vụ cháy',
                    conflict: 'Số vụ mâu thuẫn',
                    violate: 'Số vụ vi phạm',
                    accident: 'Số vụ TNGT'
                  }
                },
                metrics,
                timeRange: {
                  startTime: startTime.getTime(),
                  endTime: new Date(endTime).getTime(),
                  type
                }
              }
            };

            next(null, result);
          })
          .catch((err) => {
            next(err);
          });
      })
      .catch((err) => {
        next(err);
      });
  }

  // Thực thi waterfall
  async.waterfall([
    validateParams,
    checkAreaExists,
    calculateTimeRange,
    getStatisticDocument,
  ], (err, data) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Xử lý lỗi hệ thống
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};

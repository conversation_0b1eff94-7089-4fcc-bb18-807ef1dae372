const _ = require('lodash');
const async = require('async');
const Joi = require('joi');

const attendanceService = require('../../../../services/attendanceService');
const CONSTANTS = require('../../../../const');
const StatisticsUtils = require('../../../../utils/statisticsUtils');

/**
 * API thống kê điểm danh chi tiết theo khoảng thời gian
 * POST /api/v1.0/statisticsDetail/attendance/total
 *
 * Input:
 * - type: enum ['week', 'month', 'quarter'] - khoảng thời gian thống kê
 *   + 'week': từ đầu tuần (thứ 2) đến cuối tuần (chủ nhật) hiện tại
 *   + 'month': từ đầu tháng đến cuối tháng hiện tại
 *   + 'quarter': từ đầu quý đến cuối quý hiện tại
 *
 * Output:
 * - summary: tổng quan thống kê
 * - byDay: thống kê theo từng ngày
 * - byUser: thống kê theo từng user
 * - type: thông tin khoảng thời gian đã tính toán
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const { type = 'week' } = req.body;

  const validateAndCalculateTimeRange = (next) => {
    // Validate type
    const schema = Joi.object({
      type: Joi.string().valid('week', 'month', 'quarter').default('week')
    });

    const { error } = schema.validate({ type }, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Tham số không hợp lệ',
          body: error.details[0].message
        }
      });
    }

    try {
      // Tự động tính toán startDate và endDate dựa trên type
      const timeRange = StatisticsUtils.getTimeRange(type);

      // Gán startDate và endDate đã tính toán vào req để sử dụng ở bước tiếp theo
      req.calculatedTimeRange = {
        startDate: timeRange.startDate,
        endDate: timeRange.endDate,
        type: timeRange.type,
        startTimestamp: timeRange.startTimestamp,
        endTimestamp: timeRange.endTimestamp
      };

      next();
    } catch (error) {
      console.error('Error calculating time range:', error);
      next(error);
    }
  };

  const getStatistics = (next) => {
    try {
      const { startDate, endDate } = req.calculatedTimeRange;

      attendanceService.getDetailedAttendanceStatistics(
        type,
        startDate,
        endDate,
        userId
      )
        .then((result) => {
          if (!result || !result.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: result.message || {
                head: 'Lỗi hệ thống',
                body: 'Không thể lấy thống kê điểm danh'
              }
            });
          }

          // Thêm thông tin type đã tính toán vào response
          const responseData = {
            ...result.data,
            type: req.calculatedTimeRange
          };

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: responseData
          });
        })
        .catch((err) => {
          console.error('Error in getDetailedAttendanceStatistics:', err);
          next(err);
        });
    } catch (error) {
      console.error('Error in getStatistics:', error);
      next(error);
    }
  };

  async.waterfall([
    validateAndCalculateTimeRange,
    getStatistics
  ], (err, data) => {
    if (_.isError(err)) {
      console.error('Error in attendance statistics API:', err);
    }

    // Nếu có lỗi và là Error object, trả về lỗi hệ thống
    if (err && _.isError(err)) {
      data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: {
          head: 'Lỗi hệ thống',
          body: 'Đã xảy ra lỗi không mong muốn'
        }
      };
    }

    res.json(data || err);
  });
};



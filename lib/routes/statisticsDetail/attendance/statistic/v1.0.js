const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
const mongoose = require('mongoose');
const JobTypeModel = require('../../../../models/jobType');
const ReportModel = require('../../../../models/report');
const ToolUtil = require('../../../../util/tool');

const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

const AttendanceRecordModel = require('../../../../models/attendanceRecord');
const WorkScheduleModel = require('../../../../models/workSchedule');
const UserModel = require('../../../../models/user');

/**
 * API thống kê tổng quan vụ việc theo khu vực
 * POST /api/v1.0/statistics/reports-summary
 *
 * <PERSON><PERSON><PERSON> về thống kê tổng quan về vụ việc theo khu vực cụ thể
 * Sử dụng trường summary trong ReportModel để đếm số vụ việc
 * Phân chia theo thời gian và JobType
 * Dữ liệu này được sử dụng để hiển thị dashboard thống kê theo khu vực
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const options = [
    'today', '3days','7days','week','month','year',
  ]

  let {
    type = 'week',
    endTime,
    unit
  } = req.body;

  let startTime;

  let result;
  let topAbsentUsers, topLateUsers, topOnTimeUsers = [];
  let unitUserIds = null;
  /**
   * Validate tham số đầu vào
   */
  // B1: Nếu có unit, lấy danh sách user thuộc unit đó
  const validateParams = (next) => {
    if(type && !options.includes(type)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: `Tham số type không hợp lệ. Các giá trị hợp lệ là: ${options.join(', ')}`
        }
      });
    }
    if(!endTime) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: 'Tham số endTime là bắt buộc'
        }
      });
    }
    if(endTime > Date.now()) {
      endTime = Date.now();
    }
    if (unit) {
      UserModel.find({ units: unit, status: 1 }, '_id').lean().exec((err, users) => {
        if (err) return next(err);
        unitUserIds = users.map(u => u._id);
        next();
      });
    } else {
      next();
    }
  };
  
  const getTopOnTimeUsers = (next) => {
    const match = { 'shifts.status': 'on_time', user: { $ne: null } };
    if (unitUserIds) match.user = { $in: unitUserIds };
    WorkScheduleModel.aggregate([
      { $unwind: '$shifts' },
      { $match: match },
      { $group: { _id: '$user', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]).exec((err, onTimeResult) => {
      if (err) return next(err);
      const userIds = onTimeResult.map(u => u._id);
      UserModel.find({ _id: { $in: userIds } }, '_id name idNumber').lean().exec((err, users) => {
        if (err) return next(err);
        const userMap = {};
        users.forEach(u => { userMap[u._id.toString()] = u; });
        topOnTimeUsers = onTimeResult.map(u => ({
          userId: u._id,
          name: userMap[u._id?.toString()]?.name || '',
          idNumber: userMap[u._id?.toString()]?.idNumber || '',
          count: u.count
        }));
        next(null);
      });
    });
  };

  const calculateTimeRange = (next) => {
  
    const endDate = new Date(endTime);
    
    switch (type) {
      case 'today':
        startTime = new Date(endDate);
        startTime.setHours(0, 0, 0, 0);
        break;
      case '3days':
        startTime = new Date(endDate);
        startTime.setDate(endDate.getDate() - 2);
        break;
      case '7days':
        startTime = new Date(endDate);
        startTime.setDate(endDate.getDate() - 6);
        break;
      case 'week':
        startTime = new Date(endDate);
        // Tính ngày Thứ 2 của tuần (1 = Monday)
        const dayOfWeek = endDate.getDay(); // 0=Sunday, 1=Monday, ..., 6=Saturday
        const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Nếu là CN thì lùi 6 ngày, ngược lại lùi (dayOfWeek - 1) ngày
        startTime.setDate(endDate.getDate() - daysToMonday);
        break;
      case 'month':
        startTime = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
        break;
      case 'year':
        startTime = new Date(endDate.getFullYear(), 0, 1);
        break;
      default:
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Lỗi tham số',
            body: `Tham số type không hợp lệ. Các giá trị hợp lệ là: ${options.join(', ')}`
          }
        });
    }
    startTime.setHours(0, 0, 0, 0);
    next();
  };

  // Hàm lấy top 10 user không điểm danh nhiều nhất (group theo WorkScheduleModel.user)
  const getTopAbsentUsers = (next) => {
    const match = { 'shifts.status': 'absent', user: { $ne: null } };
    if (unitUserIds) match.user = { $in: unitUserIds };
    WorkScheduleModel.aggregate([
      { $unwind: '$shifts' },
      { $match: match },
      { $group: { _id: '$user', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]).exec((err, absentResult) => {
      if (err) return next(err);
      const userIds = absentResult.map(u => u._id);
      console.log('Absent userIds:', userIds);
      UserModel.find({ _id: { $in: userIds } }, '_id name idNumber').lean().exec((err, users) => {
        if (err) return next(err);
        const userMap = {};
        users.forEach(u => { userMap[u._id.toString()] = u; });
        topAbsentUsers = absentResult.map(u => ({
          userId: u._id,
          name: userMap[u._id?.toString()]?.name || '',
          idNumber: userMap[u._id?.toString()]?.idNumber || '',
          count: u.count
        }));
        next(null);
      });
    });
  };

  // Hàm lấy top 10 user điểm danh muộn nhiều nhất (group theo WorkScheduleModel.user)
  const getTopLateUsers = (next) => {
    const match = { 'shifts.status': 'late', user: { $ne: null } };
    if (unitUserIds) match.user = { $in: unitUserIds };
    WorkScheduleModel.aggregate([
      { $unwind: '$shifts' },
      { $match: match },
      { $group: { _id: '$user', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]).exec((err, lateResult) => {
      if (err) return next(err);
      const userIds = lateResult.map(u => u._id);
      UserModel.find({ _id: { $in: userIds } }, '_id name idNumber').lean().exec((err, users) => {
        if (err) return next(err);
        const userMap = {};
        users.forEach(u => { userMap[u._id.toString()] = u; });
        topLateUsers = lateResult.map(u => ({
          userId: u._id,
          name: userMap[u._id?.toString()]?.name || '',
          idNumber: userMap[u._id?.toString()]?.idNumber || '',
          count: u.count
        }));
        next(null);
      });
    });
  };


    const generateAllTimePeriods = () => {
      const periods = [];
      const start = new Date(startTime);
      const end = new Date(endTime);
      switch (type) {
        case 'today': {
          const pad = (n) => n.toString().padStart(2, '0');
          periods.push({
            timeLabel: `${pad(start.getDate())}/${pad(start.getMonth() + 1)}`,
            period: {
              year: start.getFullYear(),
              month: start.getMonth() + 1,
              day: start.getDate()
            },
            start: new Date(start.getFullYear(), start.getMonth(), start.getDate(), 0, 0, 0, 0),
            end: new Date(start.getFullYear(), start.getMonth(), start.getDate(), 23, 59, 59, 999)
          });
          break;
        }
        case '3days':
        case '7days': {
          for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
            if (d > end) break;
            periods.push({
              timeLabel: `${d.getDate().toString().padStart(2, '0')}/${(d.getMonth() + 1).toString().padStart(2, '0')}`,
              period: {
                year: d.getFullYear(),
                month: d.getMonth() + 1,
                day: d.getDate()
              },
              start: new Date(d.getFullYear(), d.getMonth(), d.getDate(), 0, 0, 0, 0),
              end: new Date(d.getFullYear(), d.getMonth(), d.getDate(), 23, 59, 59, 999)
            });
          }
          break;
        }
        case 'week': {
          const dayNames = ['T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'CN'];
          let current = new Date(start);
          let i = 0;
          while (current <= end && i < 7) {
            periods.push({
              timeLabel: dayNames[i],
              period: {
                dayOfWeek: i === 6 ? 1 : i + 2
              },
              start: new Date(current.getFullYear(), current.getMonth(), current.getDate(), 0, 0, 0, 0),
              end: new Date(current.getFullYear(), current.getMonth(), current.getDate(), 23, 59, 59, 999)
            });
            current.setDate(current.getDate() + 1);
            i++;
          }
          break;
        }
        case 'month': {
          const firstDay = new Date(start.getFullYear(), start.getMonth(), 1);
          const lastDay = new Date(start.getFullYear(), start.getMonth() + 1, 0);
          const pad = (n) => n.toString().padStart(2, '0');
          let weekNum = 1;
          // Nếu ngày đầu tháng không phải thứ 2, tạo tuần đầu từ ngày đầu tháng đến Chủ nhật đầu tiên
          if (firstDay.getDay() !== 1) {
            // Chủ nhật đầu tiên trong tháng
            let firstSunday = new Date(firstDay);
            firstSunday.setDate(firstDay.getDate() + (7 - firstDay.getDay()));
            if (firstSunday > lastDay) firstSunday = new Date(lastDay);
            if (firstSunday > end) firstSunday = new Date(end);
            const startLabel = `${pad(firstDay.getDate())}/${pad(firstDay.getMonth() + 1)}`;
            const endLabel = `${pad(firstSunday.getDate())}/${pad(firstSunday.getMonth() + 1)}`;
            periods.push({
              timeLabel: `${startLabel}-${endLabel}`,
              period: { week: weekNum },
              start: new Date(firstDay),
              end: new Date(firstSunday.getFullYear(), firstSunday.getMonth(), firstSunday.getDate(), 23, 59, 59, 999)
            });
            weekNum++;
            // Tuần tiếp theo bắt đầu từ thứ 2 tiếp theo
            var weekStart = new Date(firstSunday);
            weekStart.setDate(weekStart.getDate() + 1);
          } else {
            var weekStart = new Date(firstDay);
          }
          // Các tuần tiếp theo bắt đầu từ thứ 2, kết thúc chủ nhật
          while (weekStart <= lastDay && weekStart <= end) {
            let weekEnd = new Date(weekStart);
            weekEnd.setDate(weekEnd.getDate() + 6);
            if (weekEnd > lastDay) weekEnd = new Date(lastDay);
            if (weekEnd > end) weekEnd = new Date(end);
            const startLabel = `${pad(weekStart.getDate())}/${pad(weekStart.getMonth() + 1)}`;
            const endLabel = `${pad(weekEnd.getDate())}/${pad(weekEnd.getMonth() + 1)}`;
            periods.push({
              timeLabel: `${startLabel}-${endLabel}`, 
              period: { week: weekNum },
              start: new Date(weekStart),
              end: new Date(weekEnd.getFullYear(), weekEnd.getMonth(), weekEnd.getDate(), 23, 59, 59, 999)
            });
            weekStart.setDate(weekStart.getDate() + 7);
            weekNum++;
          }
          break;
        }
        case 'year': {
          for (let m = 0; m < 12; m++) {
            const monthStart = new Date(start.getFullYear(), m, 1);
            const monthEnd = new Date(start.getFullYear(), m + 1, 0, 23, 59, 59, 999);
            if (monthStart > end) break;
            if (monthEnd > end) monthEnd.setTime(end.getTime());
            periods.push({
              timeLabel: `T${m + 1}`,
              period: { month: m + 1 },
              start: monthStart,
              end: monthEnd
            });
          }
          break;
        }
      }
      return periods;
    };

  const getStatisticAttendance = (next) => {

    const userCountQuery = { status: 1 };
    if (unitUserIds) userCountQuery._id = { $in: unitUserIds };
    UserModel.countDocuments(userCountQuery, (err, totalUsers) => {
      if (err) return next(err);
      // Thêm tổng số ca (tổng shifts.length), filter theo date
      const formatDate = (d) => `${d.getDate().toString().padStart(2, '0')}-${(d.getMonth()+1).toString().padStart(2, '0')}-${d.getFullYear()}`;
      const workScheduleQuery = {
        ...((unitUserIds) ? { user: { $in: unitUserIds } } : {}),
        workAt: { $gte: startTime.getTime(), $lte: new Date(endTime).getTime() }
      };
      // Lọc shifts theo type (morning/afternoon) dựa vào thời gian hiện tại
      const nowHour = new Date().getHours();
      let shiftTypeFilter = {};
      if (nowHour < 12) {
        shiftTypeFilter = { 'shifts.type': 'morning' };
      } else {
        shiftTypeFilter = { 'shifts.type': 'afternoon' };
      }
      WorkScheduleModel.aggregate([
        { $match: workScheduleQuery },
        { $unwind: '$shifts' },
        { $match: shiftTypeFilter },
        { $group: { _id: null, total: { $sum: 1 } } }
      ]).exec((err, totalShiftResult) => {
        if (err) return next(err);
        const totalShifts = totalShiftResult && totalShiftResult.length > 0 ? totalShiftResult[0].total : 0;
        const allTimePeriods = generateAllTimePeriods();
        const metrics = [];
        let idx = 0;

        // Hàm xử lý tuần tự từng period
        const processPeriod = () => {
          if (idx >= allTimePeriods.length) {
            // Tổng hợp toàn bộ
            const totalOnTime = metrics.reduce((sum, m) => sum + m.summary.onTime, 0);
            const totalLate = metrics.reduce((sum, m) => sum + m.summary.late, 0);
            const totalAbsent = metrics.reduce((sum, m) => sum + m.summary.absent, 0);
            const totalExcused = metrics.reduce((sum, m) => sum + m.summary.excused, 0);
            const totalBusinessTrip = metrics.reduce((sum, m) => sum + m.summary.businessTrip, 0);
           
            // Tính lại tổng số ca, absent, excused, businessTrip cho summary ngoài cùng
            const today = new Date();
            // Tách truy vấn cho hôm nay và các ngày trước
            const todayDate = new Date(today.getFullYear(), today.getMonth(), today.getDate());
            const endDate = new Date(endTime);
            const endDateOnly = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());
            const isToday = endDateOnly.getTime() === todayDate.getTime();
            // Range cho các ngày trước
            const beforeTodayEnd = isToday ? todayDate.getTime() - 1 : endDateOnly.getTime();
            const beforeTodayQuery = {
              ...((unitUserIds) ? { user: { $in: unitUserIds } } : {}),
              workAt: { $gte: startTime.getTime(), $lte: beforeTodayEnd }
            };
            // Range cho hôm nay
            const todayQuery = {
              ...((unitUserIds) ? { user: { $in: unitUserIds } } : {}),
              workAt: { $gte: todayDate.getTime(), $lte: endDate.getTime() }
            };
            let todayShiftType = {};
            if (isToday) {
              if (today.getHours() < 12) {
                todayShiftType = { 'shifts.type': 'morning' };
              } else {
                todayShiftType = { 'shifts.type': 'afternoon' };
              }
            }
            // Tổng ca
            WorkScheduleModel.aggregate([
              { $match: beforeTodayQuery },
              { $unwind: '$shifts' },
              { $group: { _id: null, total: { $sum: 1 } } }
            ]).exec((err, beforeTotalResult) => {
              const beforeTotal = beforeTotalResult && beforeTotalResult.length > 0 ? beforeTotalResult[0].total : 0;
              WorkScheduleModel.aggregate([
                { $match: todayQuery },
                { $unwind: '$shifts' },
                { $match: todayShiftType },
                { $group: { _id: null, total: { $sum: 1 } } }
              ]).exec((err, todayTotalResult) => {
                const todayTotal = todayTotalResult && todayTotalResult.length > 0 ? todayTotalResult[0].total : 0;
                const totalShiftsSummary = beforeTotal + todayTotal;
                // Absent
                let absentBeforeMatch = {
                  'shifts.status': 'absent',
                  workAt: { $gte: startTime.getTime(), $lte: beforeTodayEnd }
                };
                if (unitUserIds) absentBeforeMatch.user = { $in: unitUserIds };
                WorkScheduleModel.aggregate([
                  { $unwind: '$shifts' },
                  { $match: absentBeforeMatch },
                  { $count: 'absentCount' }
                ]).exec((err, absentBeforeResult) => {
                  const absentBefore = absentBeforeResult && absentBeforeResult.length > 0 ? absentBeforeResult[0].absentCount : 0;
                  let absentTodayMatch = {
                    'shifts.status': 'absent',
                    workAt: { $gte: todayDate.getTime(), $lte: endDate.getTime() }
                  };
                  if (isToday) {
                    if (today.getHours() < 12) {
                      absentTodayMatch['shifts.type'] = 'morning';
                    } else {
                      absentTodayMatch['shifts.type'] = 'afternoon';
                    }
                  }
                  if (unitUserIds) absentTodayMatch.user = { $in: unitUserIds };
                  WorkScheduleModel.aggregate([
                    { $unwind: '$shifts' },
                    { $match: absentTodayMatch },
                    { $count: 'absentCount' }
                  ]).exec((err, absentTodayResult) => {
                    const absentToday = absentTodayResult && absentTodayResult.length > 0 ? absentTodayResult[0].absentCount : 0;
                    const totalAbsentSummary = absentBefore + absentToday;
                    // Excused
                    let excusedBeforeMatch = {
                      'shifts.status': 'excused',
                      workAt: { $gte: startTime.getTime(), $lte: beforeTodayEnd }
                    };
                    if (unitUserIds) excusedBeforeMatch.user = { $in: unitUserIds };
                    WorkScheduleModel.aggregate([
                      { $unwind: '$shifts' },
                      { $match: excusedBeforeMatch },
                      { $count: 'excusedCount' }
                    ]).exec((err, excusedBeforeResult) => {
                      const excusedBefore = excusedBeforeResult && excusedBeforeResult.length > 0 ? excusedBeforeResult[0].excusedCount : 0;
                      let excusedTodayMatch = {
                        'shifts.status': 'excused',
                        workAt: { $gte: todayDate.getTime(), $lte: endDate.getTime() }
                      };
                      if (isToday) {
                        if (today.getHours() < 12) {
                          excusedTodayMatch['shifts.type'] = 'morning';
                        } else {
                          excusedTodayMatch['shifts.type'] = 'afternoon';
                        }
                      }
                      if (unitUserIds) excusedTodayMatch.user = { $in: unitUserIds };
                      WorkScheduleModel.aggregate([
                        { $unwind: '$shifts' },
                        { $match: excusedTodayMatch },
                        { $count: 'excusedCount' }
                      ]).exec((err, excusedTodayResult) => {
                        const excusedToday = excusedTodayResult && excusedTodayResult.length > 0 ? excusedTodayResult[0].excusedCount : 0;
                        const totalExcusedSummary = excusedBefore + excusedToday;
                        // Business trip
                        let businessTripBeforeMatch = {
                          'shifts.status': 'business_trip',
                          workAt: { $gte: startTime.getTime(), $lte: beforeTodayEnd }
                        };
                        if (unitUserIds) businessTripBeforeMatch.user = { $in: unitUserIds };
                        WorkScheduleModel.aggregate([
                          { $unwind: '$shifts' },
                          { $match: businessTripBeforeMatch },
                          { $count: 'businessTripCount' }
                        ]).exec((err, businessTripBeforeResult) => {
                          const businessTripBefore = businessTripBeforeResult && businessTripBeforeResult.length > 0 ? businessTripBeforeResult[0].businessTripCount : 0;
                          let businessTripTodayMatch = {
                            'shifts.status': 'business_trip',
                            workAt: { $gte: todayDate.getTime(), $lte: endDate.getTime() }
                          };
                          if (isToday) {
                            if (today.getHours() < 12) {
                              businessTripTodayMatch['shifts.type'] = 'morning';
                            } else {
                              businessTripTodayMatch['shifts.type'] = 'afternoon';
                            }
                          }
                          if (unitUserIds) businessTripTodayMatch.user = { $in: unitUserIds };
                          WorkScheduleModel.aggregate([
                            { $unwind: '$shifts' },
                            { $match: businessTripTodayMatch },
                            { $count: 'businessTripCount' }
                          ]).exec((err, businessTripTodayResult) => {
                            const businessTripToday = businessTripTodayResult && businessTripTodayResult.length > 0 ? businessTripTodayResult[0].businessTripCount : 0;
                            const totalBusinessTripSummary = businessTripBefore + businessTripToday;
                            result = {
                              code: CONSTANTS.CODE.SUCCESS,
                              data: {
                                title: `Thống kê điểm danh cán bộ`,
                                summary: {
                                  totalUsers,
                                  total: totalShiftsSummary,
                                  onTime: totalOnTime,
                                  late: totalLate,
                                  absent: totalAbsentSummary,
                                  excused: totalExcusedSummary,
                                  businessTrip: totalBusinessTripSummary,
                                  nonAttendance: totalShiftsSummary - (totalOnTime + totalLate + totalAbsentSummary + totalExcusedSummary + totalBusinessTripSummary) < 0 ? 0 : totalShiftsSummary - (totalOnTime + totalLate + totalAbsentSummary + totalExcusedSummary + totalBusinessTripSummary)
                                },
                                chartConfig: {
                                  colors: {
                                    onTime: '#4CAF50',
                                    late: '#FFC107',
                                    absent: '#F44336',
                                    excused: '#2196F3',
                                    businessTrip: '#9C27B0'
                                  },
                                  labels: {
                                    onTime: 'Đúng giờ',
                                    late: 'Muộn',
                                    absent: 'Không điểm danh',
                                    excused: 'Xin nghỉ phép',
                                    businessTrip: 'Công tác'
                                  }
                                },
                                metrics,
                                timeRange: {
                                  startTime: startTime.getTime(),
                                  endTime: new Date(endTime).getTime(),
                                  type
                                },
                                topAbsentUsers,
                                topLateUsers,
                                topOnTimeUsers
                              }
                            };
                            return next(null, result);
                          });
                        });
                      });
                    });
                  });
                });
              });
            });
          }
        const period = allTimePeriods[idx];
        if (!period) return; // Prevent accessing properties of undefined
        // AttendanceRecord: onTime, late
        const attendanceOnTimeQuery = {
          status: 'on_time',
          createdAt: { $gte: period.start.getTime(), $lte: period.end.getTime() }
        };
        const attendanceLateQuery = {
          status: 'late',
          createdAt: { $gte: period.start.getTime(), $lte: period.end.getTime() }
        };
        if (unitUserIds) {
          attendanceOnTimeQuery.userId = { $in: unitUserIds };
          attendanceLateQuery.userId = { $in: unitUserIds };
        }
        // Thêm tổng số ca cho từng period
        const workSchedulePeriodQuery = {
          ...((unitUserIds) ? { user: { $in: unitUserIds } } : {}),
          workAt: { $gte: period.start.getTime(), $lte: period.end.getTime() }
        };
        // Lọc shifts theo type cho từng period
        let periodShiftTypeFilter = {};
        // Chỉ lọc type cho ngày hôm nay, các ngày trước lấy cả ngày
        const today = new Date();
        const isToday = period.start.getDate() === today.getDate() && period.start.getMonth() === today.getMonth() && period.start.getFullYear() === today.getFullYear();
        if (type === 'week' && isToday) {
          if (today.getHours() < 12) {
            periodShiftTypeFilter = { 'shifts.type': 'morning' };
          } else {
            periodShiftTypeFilter = { 'shifts.type': 'afternoon' };
          }
        }
        WorkScheduleModel.aggregate([
          { $match: workSchedulePeriodQuery },
          { $unwind: '$shifts' },
          { $match: periodShiftTypeFilter },
          { $group: { _id: null, total: { $sum: 1 } } }
        ]).exec((err, periodShiftResult) => {
          if (err) return next(err);
          const periodTotalShifts = periodShiftResult && periodShiftResult.length > 0 ? periodShiftResult[0].total : 0;
          AttendanceRecordModel.countDocuments(attendanceOnTimeQuery, (err, onTime) => {
            if (err) return next(err);
            AttendanceRecordModel.countDocuments(attendanceLateQuery, (err, late) => {
              if (err) return next(err);
              // Helper: format date to dd/mm/yyyy
              const startDateStr = formatDate(period.start);
              const endDateStr = formatDate(period.end);
              console.log('Processing period:', startDateStr, endDateStr);
              // Xác định có phải hôm nay không
              const today = new Date();
              const isToday = period.start.getDate() === today.getDate() && period.start.getMonth() === today.getMonth() && period.start.getFullYear() === today.getFullYear();
              let absentMatch = {
                'shifts.status': 'absent',
                workAt: { $gte: period.start.getTime(), $lte: period.end.getTime() }
              };
              let excusedMatch = {
                'shifts.status': 'excused',
                workAt: { $gte: period.start.getTime(), $lte: period.end.getTime() }
              };
              let businessTripMatch = {
                'shifts.status': 'business_trip',
                workAt: { $gte: period.start.getTime(), $lte: period.end.getTime() }
              };
              if (isToday) {
                if (today.getHours() < 12) {
                  absentMatch['shifts.type'] = 'morning';
                  excusedMatch['shifts.type'] = 'morning';
                  businessTripMatch['shifts.type'] = 'morning';
                } else {
                  absentMatch['shifts.type'] = 'afternoon';
                  excusedMatch['shifts.type'] = 'afternoon';
                  businessTripMatch['shifts.type'] = 'afternoon';
                }
              }
              if (unitUserIds) {
                absentMatch.user = { $in: unitUserIds };
                excusedMatch.user = { $in: unitUserIds };
                businessTripMatch.user = { $in: unitUserIds };
              }
              WorkScheduleModel.aggregate([
                { $unwind: '$shifts' },
                { $match: absentMatch },
                { $count: 'absentCount' }
              ]).exec((err, absentResult) => {
                if (err) return next(err);
                const absent = absentResult && absentResult.length > 0 ? absentResult[0].absentCount : 0;
                WorkScheduleModel.aggregate([
                  { $unwind: '$shifts' },
                  { $match: excusedMatch },
                  { $count: 'excusedCount' }
                ]).exec((err, excusedResult) => {
                  if (err) return next(err);
                  const excused = excusedResult && excusedResult.length > 0 ? excusedResult[0].excusedCount : 0;
                  WorkScheduleModel.aggregate([
                    { $unwind: '$shifts' },
                    { $match: businessTripMatch },
                    { $count: 'businessTripCount' }
                  ]).exec((err, businessTripResult) => {
                    if (err) return next(err);
                    const businessTrip = businessTripResult && businessTripResult.length > 0 ? businessTripResult[0].businessTripCount : 0;
                    metrics.push({
                      timeLabel: period.timeLabel,
                      period: period.period,
                      summary: {
                        total: periodTotalShifts,
                        onTime,
                        late,
                        absent,
                        excused,
                        businessTrip,
                        nonAttendance: periodTotalShifts - (onTime + late + absent + excused + businessTrip) < 0 ? 0 : periodTotalShifts - (onTime + late + absent + excused + businessTrip)
                      }
                    });
                    idx++;
                    processPeriod();
                  });
                });
              });
            });
          });
        });
      };
      processPeriod();
    });
  })
  };

  async.waterfall([
    validateParams,
    calculateTimeRange,
    getTopAbsentUsers,
    getTopLateUsers,
    getTopOnTimeUsers,
    getStatisticAttendance,
  ], (err, data) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Xử lý lỗi hệ thống
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
}

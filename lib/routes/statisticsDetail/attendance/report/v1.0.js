const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
const mongoose = require('mongoose');
const JobTypeModel = require('../../../../models/jobType');
const ReportModel = require('../../../../models/report');
const ToolUtil = require('../../../../util/tool');

const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

const AttendanceRecordModel = require('../../../../models/attendanceRecord');
const WorkScheduleModel = require('../../../../models/workSchedule');
const UserModel = require('../../../../models/user');


const ExcelJS = require('exceljs');
const path = require('path');
const fs = require('fs');
const UPLOAD_DIR = path.join(__dirname, '../../../../../public/uploads/excel');
if (!fs.existsSync(UPLOAD_DIR)) fs.mkdirSync(UPLOAD_DIR, { recursive: true });

function validateParams({ type, endTime, unit, options }, setUnitUserIds, next) {
  if(type && !options.includes(type)) {
    return next({
      code: CONSTANTS.CODE.WRONG_PARAMS,
      message: {
        head: 'Lỗi tham số',
        body: `Tham số type không hợp lệ. Các giá trị hợp lệ là: ${options.join(', ')}`
      }
    });
  }
  if(!endTime) {
    return next({
      code: CONSTANTS.CODE.WRONG_PARAMS,
      message: {
        head: 'Lỗi tham số',
        body: 'Tham số endTime là bắt buộc'
      }
    });
  }
  if(endTime > Date.now()) {
    endTime = Date.now();
  }
  if (unit) {
    UserModel.find({ units: unit, status: 1 }, '_id').lean().exec((err, users) => {
      if (err) return next(err);
      setUnitUserIds(users.map(u => u._id));
      next();
    });
  } else {
    next();
  }
}

function calculateTimeRange(type, endTime) {
  const endDate = new Date(endTime);
  let startTime;
  switch (type) {
    case 'today':
      startTime = new Date(endDate);
      startTime.setHours(0, 0, 0, 0);
      break;
    case '3days':
      startTime = new Date(endDate);
      startTime.setDate(endDate.getDate() - 2);
      break;
    case '7days':
      startTime = new Date(endDate);
      startTime.setDate(endDate.getDate() - 6);
      break;
    case 'week':
      startTime = new Date(endDate);
      const dayOfWeek = endDate.getDay();
      const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
      startTime.setDate(endDate.getDate() - daysToMonday);
      break;
    case 'month':
      startTime = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
      break;
    case 'year':
      startTime = new Date(endDate.getFullYear(), 0, 1);
      break;
    default:
      throw {
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: `Tham số type không hợp lệ. Các giá trị hợp lệ là: today, 3days, 7days, week, month, year`
        }
      };
  }
  startTime.setHours(0, 0, 0, 0);
  return startTime;
}

function getWorkSchedules({ startTime, endTime, unitUserIds }, next) {
  const query = {
    workAt: { $gte: startTime, $lte: new Date(endTime) },
  };
  if (unitUserIds) query.user = { $in: unitUserIds };
  WorkScheduleModel.find(query)
    .populate({
      path: 'user',
      select: 'name idNumber units',
      populate: { path: 'units', select: 'name' }
    })
    .lean()
    .exec((err, schedules) => {
      if (err) return next(err);
      next(null, schedules);
    });
}

function formatAndExportExcel(schedules, next) {
  const userMap = {};
  const dateSet = new Set();
  const statusMap = {
    scheduled: { text: 'Chưa điểm danh', color: '000000' },
    on_time: { text: 'Đúng giờ', color: '00BF30' },
    late: { text: 'Muộn', color: 'FFC107' },
    absent: { text: 'Không điểm danh', color: 'D30500' },
    excused: { text: 'Xin nghỉ phép', color: '007CFE' },
    business_trip: { text: 'Công tác', color: '007CFE' }
  };
  schedules.forEach(ws => {
    if (!ws.user) return;
    const userId = ws.user._id.toString();
    if (!userMap[userId]) {
      userMap[userId] = {
        name: ws.user.name,
        idNumber: ws.user.idNumber,
        unit: ws.user.units && ws.user.units[1] ? ws.user.units[1].name : '',
        days: {}
      };
    }
    const dateStr = ws.workAt ? new Date(ws.workAt).toLocaleDateString('en-GB') : '';
    dateSet.add(dateStr);
    ws.shifts && ws.shifts.forEach(shift => {
      if (!userMap[userId].days[dateStr]) userMap[userId].days[dateStr] = {};
      userMap[userId].days[dateStr][shift.type] = {
        text: statusMap[shift.status]?.text || shift.status,
        color: statusMap[shift.status]?.color || '000000'
      };
    });
  });
  const dates = Array.from(dateSet).sort((a,b) => {
    const [da,ma,ya] = a.split('/');
    const [db,mb,yb] = b.split('/');
    return new Date(`${ya}-${ma}-${da}`) - new Date(`${yb}-${mb}-${db}`);
  });

  const workbook = new ExcelJS.Workbook();
  const sheet = workbook.addWorksheet('Attendance');

  // Thêm title sau khi đã có dates
  const summaryHeaders = [
    { key: 'scheduled', text: 'Chưa điểm danh', color: '000000' },
    { key: 'on_time', text: 'Đúng giờ', color: '00BF30' },
    { key: 'late', text: 'Muộn', color: 'FFC107' },
    { key: 'absent', text: 'Không điểm danh', color: 'D30500' },
    { key: 'excused', text: 'Xin nghỉ phép', color: '007CFE' },
    { key: 'business_trip', text: 'Công tác', color: '007CFE' }
  ];
  const staticHeaders = ['STT', 'Họ và tên', 'Số hiệu', 'Tổ công tác'];
  const title = `Báo cáo điểm danh từ ${dates[0]} đến ${dates[dates.length-1]}`;
  const totalCols = staticHeaders.length + dates.length * 2 + summaryHeaders.length;
  sheet.addRow([title]);
  sheet.mergeCells(1, 1, 1, totalCols);
  const titleRow = sheet.getRow(1);
  titleRow.height = 28;
  titleRow.getCell(1).font = { bold: true, size: 16 };
  titleRow.getCell(1).alignment = { vertical: 'middle', horizontal: 'center' };
  titleRow.getCell(1).fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'FFFFFFFF' }
  };

  // Header
  const headerRow1 = [...staticHeaders];
  const headerRow2 = ['', '', '', ''];
  dates.forEach(d => {
    headerRow1.push(d, ''); // Ngày, merge 2 cột
    headerRow2.push('Sáng', 'Chiều');
  });
  // Thêm nhóm tổng kết
  headerRow1.push('Tổng kết', ...Array(summaryHeaders.length - 1).fill(''));
  summaryHeaders.forEach(h => headerRow2.push(h.text));
  sheet.addRow(headerRow1);
  sheet.addRow(headerRow2);

  // Merge cells cho ngày
  for (let i = 0; i < dates.length; i++) {
    const colStart = staticHeaders.length + i * 2 + 1;
    const colEnd = colStart + 1;
    sheet.mergeCells(2, colStart, 2, colEnd);
  }
  // Merge static header
  for (let i = 1; i <= staticHeaders.length; i++) {
    sheet.mergeCells(2, i, 3, i);
  }
  // Merge tổng kết
  const summaryStart = staticHeaders.length + dates.length * 2 + 1;
  const summaryEnd = summaryStart + summaryHeaders.length - 1;
  sheet.mergeCells(2, summaryStart, 2, summaryEnd);

  // Style header
  [2,3].forEach(rowNum => {
    const row = sheet.getRow(rowNum);
    row.eachCell((cell, colNumber) => {
      cell.font = { bold: true };
      cell.alignment = { vertical: 'middle', horizontal: 'center' };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFD9D9D9' }
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
      // Style màu cho header tổng kết
      if (rowNum === 3 && colNumber >= summaryStart && colNumber <= summaryEnd) {
        const idx = colNumber - summaryStart;
        cell.font = { bold: true, color: { argb: summaryHeaders[idx].color } };
      }
    });
  });

  // Set width cho các cột
  sheet.getColumn(1).width = 6;
  sheet.getColumn(2).width = 25;
  sheet.getColumn(3).width = 12;
  sheet.getColumn(4).width = 30;
  for (let i = 0; i < dates.length * 2; i++) {
    sheet.getColumn(staticHeaders.length + 1 + i).width = 15;
  }
  for (let i = 0; i < summaryHeaders.length; i++) {
    sheet.getColumn(staticHeaders.length + dates.length * 2 + 1 + i).width = 15;
  }

  // Thêm dữ liệu
  // Sắp xếp theo tổ công tác
  const sortedUsers = Object.values(userMap).sort((a, b) => {
    if (a.unit < b.unit) return -1;
    if (a.unit > b.unit) return 1;
    return 0;
  });

  let idx = 1;
  const dataRows = [];
  sortedUsers.forEach(user => {
    const row = [
      idx++,
      user.name || '',
      user.idNumber || '',
      user.unit || ''
    ];
    // Đếm tổng kết trạng thái
    const summaryCount = {
      scheduled: 0,
      on_time: 0,
      late: 0,
      absent: 0,
      excused: 0,
      business_trip: 0
    };
    dates.forEach(d => {
      ['morning', 'afternoon'].forEach(type => {
        const statusText = user.days[d]?.[type]?.text;
        const statusKey = Object.keys(statusMap).find(k => statusMap[k].text === statusText);
        if (statusKey) summaryCount[statusKey]++;
        row.push(statusText || '');
      });
    });
    // Thêm tổng kết vào cuối dòng
    summaryHeaders.forEach(h => row.push(summaryCount[h.key]));
    if (row.some(val => val !== '')) {
      dataRows.push(row);
      sheet.addRow(row);
    }
  });

  // Thêm border và màu cho tất cả các ô có dữ liệu
  const totalRows = sheet.rowCount;
  const totalColsData = sheet.getRow(3).cellCount;
  for (let r = 4; r <= totalRows; r++) { // bắt đầu từ dòng 4 (dữ liệu)
    const row = sheet.getRow(r);
    for (let c = 1; c <= totalColsData; c++) {
      const cell = row.getCell(c);
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
      // Căn giữa cột STT
      if (c === 1) {
        cell.alignment = { vertical: 'middle', horizontal: 'center' };
      }
      // Tô màu cho các trạng thái
      if (r >= 4 && c > 4 && c < summaryStart) { // chỉ các ô trạng thái
        const dateIdx = Math.floor((c - 5) / 2);
        const type = (c - 5) % 2 === 0 ? 'morning' : 'afternoon';
        const userIdx = r - 4;
        const user = sortedUsers[userIdx];
        const d = dates[dateIdx];
        const statusObj = user?.days?.[d]?.[type];
        if (statusObj && statusObj.color) {
          cell.font = Object.assign({}, cell.font, { color: { argb: statusObj.color } });
        }
      }
      // Tô màu cho tổng kết
      if (r >= 4 && c >= summaryStart && c <= summaryEnd) {
        const idx = c - summaryStart;
        cell.font = Object.assign({}, cell.font, { color: { argb: summaryHeaders[idx].color } });
      }
    }
  }
  const fileName = `attendance_report_${Date.now()}.xlsx`;
  const filePath = path.join(UPLOAD_DIR, fileName);
  workbook.xlsx.writeFile(filePath)
    .then(() => {
      const downloadLink = `/uploads/excel/${fileName}`;
      next(null, { code: CONSTANTS.CODE.SUCCESS, link: downloadLink });
    })
    .catch(next);
}

module.exports = (req, res) => {
  const userId = req.user.id;
  const options = [
    'today', '3days','7days','week','month','year',
  ];
  let { type = 'week', endTime, unit, startTime, users } = req.body;
  let unitUserIds = null;
  let filteredUserIds = null;

  async.waterfall([
    function stepValidateParams(next) {
      validateParams({ type, endTime, unit, options }, ids => { unitUserIds = ids; }, next);
    },
    function stepMergeUserIds(next) {
      // users: mảng id string (optional)
      if (users && Array.isArray(users) && users.length > 0) {
        if (unitUserIds && Array.isArray(unitUserIds)) {
          // Merge (union) users và unitUserIds, loại bỏ trùng lặp
          filteredUserIds = Array.from(new Set([...users, ...unitUserIds]));
        } else {
          filteredUserIds = users;
        }
      } else {
        filteredUserIds = unitUserIds;
      }
      next();
    },
    function stepCalculateTimeRange(next) {
      if (startTime) {
        // Nếu đã có startTime từ body thì dùng luôn
        next(null, new Date(startTime));
      } else {
        try {
          const calculatedStartTime = calculateTimeRange(type, endTime);
          next(null, calculatedStartTime);
        } catch (err) {
          next(err);
        }
      }
    },
    function stepGetWorkSchedules(startTime, next) {
      getWorkSchedules({ startTime, endTime, unitUserIds: filteredUserIds }, next);
    },
    formatAndExportExcel
  ], (err, data) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });
    res.json(data || err);
  });
}

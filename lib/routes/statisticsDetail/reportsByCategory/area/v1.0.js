const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
const mongoose = require('mongoose');
const JobTypeModel = require('../../../../models/jobType');
const ReportModel = require('../../../../models/report');
const ReportDetailModel = require('../../../../models/reportDetail');
const AreaModel = require('../../../../models/area');

const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

/**
 * API thống kê báo cáo theo category (jobType) group theo area
 * POST /api/v1.0/statistics/reports-summary
 *
 * Trả về thống kê báo cáo theo category được group theo area
 * Đầu vào: category (jobType), thời gian
 * Đầu ra: Thống kê số lượng báo cáo theo từng area sử dụng ReportDetailModel
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const categoryId = req.body.category;

  const {
    type = 'week',
    endTime
  } = req.body;

  let startTime;
  let categoryInfo;

  let result;

  /**
   * Validate tham số đầu vào
   */
  const validateParams = (next) => {
    const options = ['3days','7days','week','month','year'];
    
    if(type && !options.includes(type)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: `Tham số type không hợp lệ. Các giá trị hợp lệ là: ${options.join(', ')}`
        }
      });
    }
    if(!endTime) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: 'Tham số endTime là bắt buộc'
        }
      });
    }
    if(!categoryId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: 'Tham số category là bắt buộc'
        }
      });
    }
    if(!mongoose.Types.ObjectId.isValid(categoryId)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: 'Tham số category không hợp lệ'
        }
      });
    }
    next();
  };

  const checkCategoryExists = (next) => {
    // Kiểm tra xem category (jobType) có tồn tại không
    JobTypeModel.findById(categoryId)
      .then((category) => {
        if (!category) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,    
            message: {
              head: 'Lỗi tham số',
              body: 'Category không tồn tại'
            }
          });
        }
        // Lưu thông tin category để sử dụng sau
        categoryInfo = category;
        next();
      })
      .catch((err) => {
        next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: {
            head: 'Lỗi hệ thống',
            body: 'Đã xảy ra lỗi khi kiểm tra category'
          }
        });
      });
  };

  const calculateTimeRange = (next) => {
    const endDate = new Date(endTime);
    
    switch (type) {
      case '3days':
        startTime = new Date(endDate);
        startTime.setDate(endDate.getDate() - 2);
        break;
      case '7days':
        startTime = new Date(endDate);
        startTime.setDate(endDate.getDate() - 6);
        break;
      case 'week':
        startTime = new Date(endDate);
        // Tính ngày Thứ 2 của tuần (1 = Monday)
        const dayOfWeek = endDate.getDay(); // 0=Sunday, 1=Monday, ..., 6=Saturday
        const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Nếu là CN thì lùi 6 ngày, ngược lại lùi (dayOfWeek - 1) ngày
        startTime.setDate(endDate.getDate() - daysToMonday);
        break;
      case 'month':
        startTime = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
        break;
      case 'year':
        startTime = new Date(endDate.getFullYear(), 0, 1);
        break;
      default:
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Lỗi tham số',
            body: `Tham số type không hợp lệ`
          }
        });
    }
    startTime.setHours(0, 0, 0, 0);

    next();
  };

  const getStatisticDocument = (next) => {
    // B1: Lấy tất cả các area có parent: null
    AreaModel.find({ parent: null, status: 1 }, '_id name')
      .lean()
      .then((parentAreas) => {
        if (!parentAreas || parentAreas.length === 0) {
          return next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: {
              title: `Thống kê theo khu vực`,
              summary: {
                categoryName: categoryInfo.shortName,
                totalAreas: 0
              },
              areas: [],
              timeRange: {
                startTime: startTime.getTime(),
                endTime: new Date(endTime).getTime(),
                type
              }
            }
          });
        }

        // B2: Query ReportDetailModel để lấy thống kê theo category (jobType) group theo area
        const reportQuery = {
          time: {
            $gte: startTime.getTime(),
            $lte: new Date(endTime).getTime()
          },
          jobType: mongoose.Types.ObjectId(categoryId),
          areas: { $exists: true, $ne: [] }
        };

        ReportDetailModel.aggregate([
          { $match: reportQuery },
          {
            $group: {
              _id: { $arrayElemAt: ['$areas', 0] }, // Group theo areas[0]
              totalCount: { $sum: 1 } // Đếm số bản ghi
            }
          },
          {
            $match: {
              _id: { $ne: null } // Loại bỏ các group có _id null
            }
          },
          {
            $sort: { totalCount: -1 }
          }
        ])
          .then((aggregateResults) => {
            // Tạo map thống kê theo area ID
            const statsMap = {};
            aggregateResults.forEach(item => {
              statsMap[item._id.toString()] = {
                totalCount: item.totalCount
              };
            });

            // Merge areas với thống kê - chỉ hiển thị areas có parent: null
            const colors = [
              '#00BF30'
            ];
            
            const areas = parentAreas.map((area, index) => {
              const areaId = area._id.toString();
              const stats = statsMap[areaId] || { totalCount: 0 };
              
              return {
                areaId: area._id,
                areaName: area.name,
                count: stats.totalCount,
                color: colors[index % colors.length] // Lặp lại màu nếu có nhiều area hơn màu
              };
            }).sort((a, b) => b.count - a.count); // Sort theo count giảm dần

            result = {
              code: CONSTANTS.CODE.SUCCESS,
              data: {
                title: `Báo cáo ${categoryInfo.shortName} theo khu vực`,
                summary: {
                  categoryName: categoryInfo.shortName,
                  totalAreas: areas.length
                },
                areas,
                timeRange: {
                  startTime: startTime.getTime(),
                  endTime: new Date(endTime).getTime(),
                  type
                }
              }
            };

            next(null, result);
          })
          .catch((err) => {
            next(err);
          });
      })
      .catch((err) => {
        next(err);
      });
  }

  // Thực thi waterfall
  async.waterfall([
    validateParams,
    checkCategoryExists,
    calculateTimeRange,
    getStatisticDocument,
  ], (err, data) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Xử lý lỗi hệ thống
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};

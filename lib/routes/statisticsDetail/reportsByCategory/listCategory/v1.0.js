const _ = require('lodash');
const async = require('async');
const JobTypeModel = require('../../../../models/jobType');

const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

/**
 * API trả về danh sách các vụ việc cần báo cáo
 * POST /api/v1.0/statistics/reports-summary
 *
 * Tr<PERSON> về danh sách các loại vụ việc (JobType) có thể được báo cáo
 * Query JobTypeModel với điều kiện { status: 1, 'quickReportTemplate.chartTypes': 'heatmap' }
 * Không phân trang
 */
module.exports = (req, res) => {
  const userId = req.user.id;

  let result;

  const getJobTypes = (next) => {
    // Query JobTypeModel để lấy các jobType có status = 1 và chartTypes = 'heatmap'
    JobTypeModel.find(
      { status: 1, 'quickReportTemplate.chartTypes': 'heatmap' }, 
      '_id name'
    )
      .lean()
      .sort({ createdAt: -1 })
      .then((jobTypes) => {
        result = {
          code: CONSTANTS.CODE.SUCCESS,
          data: jobTypes
        };

        next(null, result);
      })
      .catch((err) => {
        next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: {
            head: 'Lỗi hệ thống',
            body: 'Đã xảy ra lỗi khi lấy danh sách loại vụ việc'
          }
        });
      });
  };

  // Thực thi waterfall
  async.waterfall([
    getJobTypes,
  ], (err, data) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Xử lý lỗi hệ thống
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};

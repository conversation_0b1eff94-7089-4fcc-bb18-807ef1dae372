const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
const JobTypeModel = require('../../../../models/jobType');
const ReportModel = require('../../../../models/report');

const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const ToolUtil = require('../../../../util/tool');

/**
 * API thống kê tổng quan báo cáo
 * POST /api/v1.0/statistics/reports-summary
 *
 * Trả về thống kê tổng quan về báo cáo trong hệ thống
 * Bao gồm số lượng báo cáo, phân loại theo trạng thái, loại báo cáo, đơn vị và JobType
 * Dữ liệu này đư<PERSON><PERSON> sử dụng để hiển thị dashboard tổng quan
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const options = [
    '3days','7days','week','month','year',
  ]

  const {
    type = 'week',
    endTime
  } = req.body;

  let startTime;

  let result;

  /**
   * Validate tham số đầu vào
   */
  const validateParams = (next) => {

    if(type && !options.includes(type)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: `Tham số type không hợp lệ. Các giá trị hợp lệ là: ${options.join(', ')}`
        }
      });
    }
    if(!endTime) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: 'Tham số endTime là bắt buộc'
        }
      });
    }

    next();
  };

  const calculateTimeRange = (next) => {
    const endDate = new Date(endTime);

    switch (type) {
      case '3days':
        startTime = new Date(endDate);
        startTime.setDate(endDate.getDate() - 2);
        break;
      case '7days':
        startTime = new Date(endDate);
        startTime.setDate(endDate.getDate() - 6);
        break;
      case 'week':
        startTime = new Date(endDate);
        // Tính ngày Thứ 2 của tuần (1 = Monday)
        const dayOfWeek = endDate.getDay(); // 0=Sunday, 1=Monday, ..., 6=Saturday
        const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Nếu là CN thì lùi 6 ngày, ngược lại lùi (dayOfWeek - 1) ngày
        startTime.setDate(endDate.getDate() - daysToMonday);
        break;
      case 'month':
        startTime = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
        break;
      case 'year':
        startTime = new Date(endDate.getFullYear(), 0, 1);
        break;
      default:
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Lỗi tham số',
            body: `Tham số type không hợp lệ. Các giá trị hợp lệ là: ${options.join(', ')}`
          }
        });
    }
    startTime.setHours(0, 0, 0, 0);

    next();
  };

  const getStatisticDocument = (next) => {
    // B1: Query JobTypeModel để lấy các jobType có chartTypes = 'document'
    JobTypeModel.find({
      status: 1,
      'quickReportTemplate.chartTypes': 'document'
    }, '_id name quickReportTemplate')
      .lean()
      .then((jobTypes) => {
        if (!jobTypes || jobTypes.length === 0) {
          return next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            message: MESSAGES.SYSTEM.SUCCESS,
            data: {
              totalReports: 0,
              metrics: []
            }
          });
        }

        const jobTypeIds = jobTypes.map(jt => jt._id);

        // B2: Query ReportModel để lấy thống kê
        const reportQuery = {
          jobType: { $in: jobTypeIds },
          dateTimestamp: {
            $gte: startTime.getTime(),
            $lte: new Date(endTime).getTime()
          }
        };

        // Tạo pipeline aggregate dựa vào type để phân chia thời gian
        let dateGrouping;
        let sortField;

        switch (type) {
          case '3days':
          case '7days':
            // Chia theo từng ngày (cộng 7 giờ để chuyển về GMT+7)
            dateGrouping = {
              year: { $year: { $add: [{ $toDate: '$dateTimestamp' }, 7 * 60 * 60 * 1000] } },
              month: { $month: { $add: [{ $toDate: '$dateTimestamp' }, 7 * 60 * 60 * 1000] } },
              day: { $dayOfMonth: { $add: [{ $toDate: '$dateTimestamp' }, 7 * 60 * 60 * 1000] } }
            };
            sortField = { '_id.year': 1, '_id.month': 1, '_id.day': 1, '_id.jobType': 1 };
            break;
          case 'week':
            // Chia theo từng thứ trong tuần (1=Chủ nhật, 2=Thứ 2, ...)
            dateGrouping = {
              year: { $year: { $add: [{ $toDate: '$dateTimestamp' }, 7 * 60 * 60 * 1000] } },
              week: { $week: { $add: [{ $toDate: '$dateTimestamp' }, 7 * 60 * 60 * 1000] } },
              dayOfWeek: { $dayOfWeek: { $add: [{ $toDate: '$dateTimestamp' }, 7 * 60 * 60 * 1000] } }
            };
            sortField = { '_id.year': 1, '_id.week': 1, '_id.dayOfWeek': 1, '_id.jobType': 1 };
            break;
          case 'month':
            // Chia theo từng tuần trong tháng
            dateGrouping = {
              year: { $year: { $add: [{ $toDate: '$dateTimestamp' }, 7 * 60 * 60 * 1000] } },
              month: { $month: { $add: [{ $toDate: '$dateTimestamp' }, 7 * 60 * 60 * 1000] } },
              week: { $week: { $add: [{ $toDate: '$dateTimestamp' }, 7 * 60 * 60 * 1000] } }
            };
            sortField = { '_id.year': 1, '_id.month': 1, '_id.week': 1, '_id.jobType': 1 };
            break;
          case 'year':
            // Chia theo từng tháng trong năm
            dateGrouping = {
              year: { $year: { $add: [{ $toDate: '$dateTimestamp' }, 7 * 60 * 60 * 1000] } },
              month: { $month: { $add: [{ $toDate: '$dateTimestamp' }, 7 * 60 * 60 * 1000] } }
            };
            sortField = { '_id.year': 1, '_id.month': 1, '_id.jobType': 1 };
            break;
        }

        ReportModel.aggregate([
          { $match: reportQuery },
          {
            $group: {
              _id: {
                jobType: '$jobType',
                ...dateGrouping
              },
              totalReports: { $sum: 1 },
              incomingDocuments: {
                $sum: { $ifNull: ['$metrics.incomingDocuments', 0] }
              },
              outgoingDocuments: {
                $sum: { $ifNull: ['$metrics.outgoingDocuments', 0] }
              },
              unansweredDocuments: {
                $sum: { $ifNull: ['$metrics.unansweredDocuments', 0] }
              }
            }
          },
          { $sort: sortField }
        ])
          .then((reportStats) => {
            // Helper function để tạo tất cả time periods cho type
            const generateAllTimePeriods = () => {
              const periods = [];
              const start = new Date(startTime);
              const end = new Date(endTime);

              switch (type) {
                case '3days':
                case '7days':
                  // Tạo tất cả các ngày trong khoảng thời gian
                  for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
                    periods.push({
                      timeLabel: `${d.getDate().toString().padStart(2, '0')}/${(d.getMonth() + 1).toString().padStart(2, '0')}`,
                      period: {
                        year: d.getFullYear(),
                        month: d.getMonth() + 1,
                        day: d.getDate()
                      }
                    });
                  }
                  break;
                case 'week':
                  // Tạo tất cả các thứ trong tuần (T2 -> CN)
                  const dayNames = ['T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'CN'];
                  for (let i = 0; i < 7; i++) {
                    periods.push({
                      timeLabel: dayNames[i],
                      period: {
                        dayOfWeek: i === 6 ? 1 : i + 2 // T2=2, T3=3, T4=4, T5=5, T6=6, T7=7, CN=1
                      }
                    });
                  }
                  break;
                case 'month':
                  // Tạo tất cả các tuần trong tháng
                  const startWeek = Math.floor((start.getTime() - new Date(start.getFullYear(), 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000)) + 1;
                  const endWeek = Math.floor((end.getTime() - new Date(end.getFullYear(), 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000)) + 1;
                  for (let w = startWeek; w <= endWeek; w++) {
                    periods.push({
                      timeLabel: `Tuần ${w - startWeek + 1}`,
                      period: {
                        week: w
                      }
                    });
                  }
                  break;
                case 'year':
                  // Tạo tất cả các tháng trong năm
                  for (let m = start.getMonth() + 1; m <= end.getMonth() + 1; m++) {
                    periods.push({
                      timeLabel: `T${m}`,
                      period: {
                        month: m
                      }
                    });
                  }
                  break;
              }
              return periods;
            };

            // Helper function để format label thời gian
            const formatTimeLabel = (timeGroup) => {
              switch (type) {
                case '3days':
                case '7days':
                  return `${timeGroup.day.toString().padStart(2, '0')}/${timeGroup.month.toString().padStart(2, '0')}`;
                case 'week':
                  const dayNames = ['', 'CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'];
                  return dayNames[timeGroup.dayOfWeek];
                case 'month':
                  return `Tuần ${timeGroup.week - Math.floor((new Date(timeGroup.year, timeGroup.month - 1, 1).getTime() - new Date(timeGroup.year, 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000)) + 1}`;
                case 'year':
                  return `T${timeGroup.month}`;
                default:
                  return '';
              }
            };

            // Tạo tất cả time periods
            const allTimePeriods = generateAllTimePeriods();

            // Group dữ liệu theo jobType và time periods
            const groupedData = {};

            // Khởi tạo tất cả jobTypes với tất cả time periods (với giá trị 0)
            jobTypes.forEach(jobType => {
              groupedData[jobType._id.toString()] = {
                jobTypeId: jobType._id,
                jobTypeName: jobType.name,
                chartTypes: jobType.quickReportTemplate?.chartTypes,
                timeSeries: allTimePeriods.map(period => {
                  return {
                    timeLabel: period.timeLabel,
                    period: period.period,
                    incomingDocuments: 0,
                    outgoingDocuments: 0,
                    unansweredDocuments: 0
                  };
                })
              };
            });

            // Cập nhật dữ liệu thực tế từ reportStats
            reportStats.forEach(stat => {
              const jobTypeId = stat._id.jobType.toString();
              const timeLabel = formatTimeLabel(stat._id);

              if (groupedData[jobTypeId]) {
                const timeSeriesItem = groupedData[jobTypeId].timeSeries.find(ts => ts.timeLabel === timeLabel);
                if (timeSeriesItem) {
                  timeSeriesItem.incomingDocuments = stat.incomingDocuments;
                  timeSeriesItem.outgoingDocuments = stat.outgoingDocuments;
                  timeSeriesItem.unansweredDocuments = stat.unansweredDocuments;
                }
              }
            });

            // Tính tổng số báo cáo
            const totalIncoming = reportStats.reduce((sum, stat) => sum + stat.incomingDocuments, 0);
            const totalOutgoing = reportStats.reduce((sum, stat) => sum + stat.outgoingDocuments, 0);
            const totalUnanswered = reportStats.reduce((sum, stat) => sum + stat.unansweredDocuments, 0);

            // Convert grouped data thành array chỉ lấy timeSeries
            const metrics = Object.values(groupedData).map(item => item.timeSeries).flat();
            if(type === 'month') {
              metrics.forEach(item => {
                if(item.period.week) {
                    const { weekStart, weekEnd } = ToolUtil.getDateRangeOfISOWeek(item.period.week, startTime.getFullYear());
                    // Đảm bảo tuần nằm trong tháng đang xét
                    const month = startTime.getMonth() + 1;
                    let labelStart = weekStart;
                    let labelEnd = weekEnd;
                    if (weekStart.getMonth() + 1 < month) labelStart = new Date(startTime.getFullYear(), month - 1, 1);
                    if (weekEnd.getMonth() + 1 > month) labelEnd = new Date(startTime.getFullYear(), month, 0);
                    item.timeLabel = `${labelStart.getDate().toString().padStart(2, '0')}/${month.toString().padStart(2, '0')} - ${labelEnd.getDate().toString().padStart(2, '0')}/${month.toString().padStart(2, '0')}`;
                }
              });
            }
            result = {
              code: CONSTANTS.CODE.SUCCESS,
              data: {
                title: "Biểu đồ báo cáo số lượng công văn",
                summary: {
                  totalIncomingDocuments: totalIncoming,
                  totalOutgoingDocuments: totalOutgoing,
                  totalUnansweredDocuments: totalUnanswered
                },
                chartConfig: {
                  colors: {
                    incomingDocuments: '#007CFE',    // Xanh dương sáng - văn bản đến
                    outgoingDocuments: '#FFC107',    // Cam đỏ sáng - văn bản đi
                    unansweredDocuments: '#00BF30'      // Xanh lá sáng - văn bản đã trả lời
                  },
                  labels: {
                    incomingDocuments: 'Số công văn đến',
                    outgoingDocuments: 'Số công văn đi',
                    unansweredDocuments: 'Số công văn chưa trả lời'
                  }
                },
                metrics,
                timeRange: {
                  startTime: startTime.getTime(),
                  endTime: new Date(endTime).getTime(),
                  type
                }
              }
            };

            next(null, result);
          })
          .catch((err) => {
            next(err);
          });
      })
      .catch((err) => {
        next(err);
      });
  }

  // Thực thi waterfall
  async.waterfall([
    validateParams,
    calculateTimeRange,
    getStatisticDocument,
  ], (err, data) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Xử lý lỗi hệ thống
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};

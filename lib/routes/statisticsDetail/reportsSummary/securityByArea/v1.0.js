const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
const JobTypeModel = require('../../../../models/jobType');
const ReportModel = require('../../../../models/report');
const ReportDetailModel = require('../../../../models/reportDetail');
const AreaModel = require('../../../../models/area');

const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

/**
 * API thống kê tổng quan an ninh theo khu vực
 * POST /api/v1.0/statistics/reports-summary
 *
 * Trả về thống kê tổng quan về các vụ việc an ninh theo khu vực
 * Tổng số vụ việc được nhóm theo khu vực: report.details.location.areas[1]
 * Không phân chia theo thời gian hay loại vụ việc chi tiết
 * Dữ liệu này được sử dụng để hiển thị dashboard thống kê an ninh theo khu vực
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const options = [
    '3days','7days','week','month','year',
  ]

  const {
    type = 'week',
    endTime
  } = req.body;

  let startTime;

  let result;

  /**
   * Validate tham số đầu vào
   */
  const validateParams = (next) => {
    
    if(type && !options.includes(type)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: `Tham số type không hợp lệ. Các giá trị hợp lệ là: ${options.join(', ')}`
        }
      });
    }
    if(!endTime) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: 'Tham số endTime là bắt buộc'
        }
      });
    }
    
    next();
  };

  const calculateTimeRange = (next) => {
    const endDate = new Date(endTime);
    
    switch (type) {
      case '3days':
        startTime = new Date(endDate);
        startTime.setDate(endDate.getDate() - 2);
        break;
      case '7days':
        startTime = new Date(endDate);
        startTime.setDate(endDate.getDate() - 6);
        break;
      case 'week':
        startTime = new Date(endDate);
        // Tính ngày Thứ 2 của tuần (1 = Monday)
        const dayOfWeek = endDate.getDay(); // 0=Sunday, 1=Monday, ..., 6=Saturday
        const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Nếu là CN thì lùi 6 ngày, ngược lại lùi (dayOfWeek - 1) ngày
        startTime.setDate(endDate.getDate() - daysToMonday);
        break;
      case 'month':
        startTime = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
        break;
      case 'year':
        startTime = new Date(endDate.getFullYear(), 0, 1);
        break;
      default:
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Lỗi tham số',
            body: `Tham số type không hợp lệ. Các giá trị hợp lệ là: ${options.join(', ')}`
          }
        });
    }
    startTime.setHours(0, 0, 0, 0);

    next();
  };

  const getStatisticDocument = (next) => {
    // B1: Lấy tất cả khu vực với điều kiện status: 1, parent: null
    AreaModel.find({ status: 1, parent: null }, '_id name')
      .lean()
      .then((areas) => {
        if (!areas || areas.length === 0) {
          return next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            message: MESSAGES.SYSTEM.SUCCESS,
            data: {
              metrics: []
            }
          });
        }

        // B2: Query JobTypeModel để lấy các jobType có chartTypes = 'heatmap'
        JobTypeModel.find({ status: 1, 'quickReportTemplate.chartTypes': 'heatmap' }, '_id name quickReportTemplate')
          .lean()
          .then((jobTypes) => {
            if (!jobTypes || jobTypes.length === 0) {
              return next(null, {
                code: CONSTANTS.CODE.SUCCESS,
                message: MESSAGES.SYSTEM.SUCCESS,
                data: {
                  metrics: []
                }
              });
            }

            const jobTypeIds = jobTypes.map(jt => jt._id);
            const areaIds = areas.map(area => area._id);

            // B3: Sử dụng aggregation để thống kê từ ReportDetailModel
            ReportDetailModel.aggregate([
              {
                $match: {
                  jobType: { $in: jobTypeIds },
                  time: {
                    $gte: startTime.getTime(),
                    $lte: new Date(endTime).getTime()
                  },
                  areas: { $exists: true, $ne: [] }, // Đảm bảo areas tồn tại và không rỗng
                  'areas.0': { $in: areaIds } // Kiểm tra phần tử đầu tiên trong mảng areas
                }
              },
              {
                $group: {
                  _id: { $arrayElemAt: ['$areas', 0] }, // Lấy phần tử đầu tiên của mảng areas
                  total: { $sum: 1 } // Đếm số bản ghi (mỗi bản ghi = 1 báo cáo chi tiết)
                }
              },
              {
                $match: {
                  _id: { $ne: null } // Loại bỏ các group có _id null
                }
              }
            ])
            .then((aggregateResults) => {
              // Tạo map để tra cứu nhanh
              const areaCountMap = {};
              aggregateResults.forEach(item => {
                areaCountMap[item._id.toString()] = item.total;
              });

              // Tạo metrics cho tất cả khu vực (kể cả khu vực có count = 0)
              const metrics = areas.map(area => ({
                areaId: area._id,
                areaName: area.name,
                total: areaCountMap[area._id.toString()] || 0
              }));

              // Sắp xếp theo tổng số vụ việc giảm dần
              metrics.sort((a, b) => b.total - a.total);

              // Tạo màu cho mỗi khu vực
              const areaColors = [
                '#FF9600'
              ];

              // Thêm màu vào mỗi metric
              metrics.forEach((metric, index) => {
                const colorIndex = index % areaColors.length;
                metric.color = areaColors[colorIndex];
              });

              // Tính tổng summary
              const summary = {
                total: metrics.reduce((sum, item) => sum + item.total, 0),
                totalAreas: metrics.length
              };

              result = {
                code: CONSTANTS.CODE.SUCCESS,
                data: {
                  title: "Thống kê theo khu vực",
                  summary,
                  metrics,
                  timeRange: {
                    startTime: startTime.getTime(),
                    endTime: new Date(endTime).getTime(),
                    type
                  }
                }
              };

              next(null, result);
            })
            .catch((err) => {
              next(err);
            });
          })
          .catch((err) => {
            next(err);
          });
      })
      .catch((err) => {
        next(err);
      });
  }

  // Thực thi waterfall
  async.waterfall([
    validateParams,
    calculateTimeRange,
    getStatisticDocument,
  ], (err, data) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Xử lý lỗi hệ thống
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};

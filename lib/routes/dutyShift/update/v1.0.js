const _ = require('lodash');
const async = require('async');
const StatisticsTrigger = require('../../../utils/statisticsTrigger');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const DutyShiftModel = require('../../../models/dutyShift');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  const {
    _id,
    startTime,
    endTime,
    officer,
    status,
    name,
    forLeader,
    locationDuty,
    unit,
    description,
    notes,
    hasEquipment
  } = req.body;

  const checkParams = (next) => {
    if (!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    if (!_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp ID ca trực'
        }
      });
    }

    // Validate startTime và endTime nếu có
    if (startTime !== undefined && typeof startTime !== 'number') {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Thời gian bắt đầu phải là số (timestamp)'
        }
      });
    }

    if (endTime !== undefined && typeof endTime !== 'number') {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Thời gian kết thúc phải là số (timestamp)'
        }
      });
    }

    if (startTime !== undefined && endTime !== undefined && startTime >= endTime) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Thời gian bắt đầu phải nhỏ hơn thời gian kết thúc'
        }
      });
    }

    // Validate status nếu có
    if (status !== undefined && ![0, 1, 2].includes(status)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Status phải là 0 (chưa xác nhận), 1 (đã xác nhận) hoặc 2 (hủy bỏ)'
        }
      });
    }

    // Validate forLeader nếu có
    if (forLeader !== undefined && typeof forLeader !== 'boolean') {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Trường forLeader phải là true hoặc false'
        }
      });
    }

    next();
  };

  const updateDutyShift = (next) => {
    const updateData = {
      updatedAt: new Date()
    };

    // Cập nhật các trường nếu có
    if (startTime !== undefined) updateData.startTime = startTime;
    if (endTime !== undefined) updateData.endTime = endTime;
    if (officer !== undefined) updateData.officer = officer;
    if (status !== undefined) updateData.status = status;
    if (name !== undefined) updateData.name = name;
    if (forLeader !== undefined) updateData.forLeader = forLeader;
    if (locationDuty !== undefined) updateData.locationDuty = locationDuty;
    if (unit !== undefined) updateData.unit = unit;
    if (description !== undefined) updateData.description = description;
    if (notes !== undefined) updateData.notes = notes;
    if (hasEquipment !== undefined) updateData.hasEquipment = hasEquipment;

    // Kiểm tra có dữ liệu để cập nhật không (ngoài updatedAt)
    if (Object.keys(updateData).length === 1) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Không có dữ liệu để cập nhật'
        }
      });
    }

    DutyShiftModel.findOneAndUpdate(
      { _id: _id },
      { $set: updateData },
      { new: true }
    )
    .populate('officer', 'name email phone')
    .populate('unit', 'name')
    .populate('assignedBy', 'name')
    .lean()
    .exec((err, shift) => {
      if (err) {
        return next(err);
      }

      if (!shift) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Ca trực không tồn tại'
          }
        });
      }

      // Trigger statistics update
      StatisticsTrigger.triggerDutyShiftUpdate('update', {
        _id: shift._id,
        officer: shift.officer._id,
        startTime: shift.startTime,
        endTime: shift.endTime,
        status: shift.status,
        source: shift.source
      });

      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        message: {
          head: 'Thông báo',
          body: 'Cập nhật ca trực thành công'
        },
        data: shift
      });
    });
  };

  async.waterfall([checkParams, updateDutyShift], (err, data) => {
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};

const _ = require('lodash');
const async = require('async');
const StatisticsTrigger = require('../../../utils/statisticsTrigger');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const DutyShiftModel = require('../../../models/dutyShift');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  const { _id } = req.body;

  const checkParams = (next) => {
    if (!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    if (!_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp ID ca trực'
        }
      });
    }

    next();
  };

  const inactiveDutyShift = (next) => {
    const updateData = {
      status: 0,
      updatedAt: new Date()
    };

    DutyShiftModel.findOneAndUpdate(
      { _id: _id },
      { $set: updateData },
      { new: true }
    )
    .populate('officer', 'name email phone')
    .populate('unit', 'name')
    .populate('assignedBy', 'name')
    .lean()
    .exec((err, shift) => {
      if (err) {
        return next(err);
      }

      if (!shift) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Ca trực không tồn tại'
          }
        });
      }

      // Trigger statistics update
      StatisticsTrigger.triggerDutyShiftUpdate('delete', {
        _id: shift._id,
        officer: shift.officer._id,
        startTime: shift.startTime,
        endTime: shift.endTime,
        status: shift.status,
        source: shift.source
      });

      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        message: {
          head: 'Thông báo',
          body: 'Hủy ca trực thành công'
        },
        data: shift
      });
    });
  };

  async.waterfall([checkParams, inactiveDutyShift], (err, data) => {
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};

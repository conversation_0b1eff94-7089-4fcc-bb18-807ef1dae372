const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Jo<PERSON>);

const scheduleAggregationService = require('../../../services/scheduleAggregationService');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const DateUtils = require('../../../utils/dateUtils');
const UserModel = require('../../../models/user');

/**
 * API lấy lịch tổng hợp được nhóm theo ngày
 * POST /api/v1.0/schedule/grouped
 *
 * Tổng hợp dữ liệu từ 3 loại lịch: work, duty, meeting
 * Nhóm theo ngày và sắp xếp theo thời gian trong ngày
 *
 * @param {string} userId - Id người dùng
 * @param {string} startDate - <PERSON><PERSON><PERSON> bắt đầ<PERSON> (DD-MM-YYYY) - tùy chọn
 * @param {string} endDate - <PERSON><PERSON><PERSON> kết thúc (DD-MM-YYYY) - tùy chọn
 * @param {string} scheduleType - Loại lịch: 'work', 'duty', 'meeting' - tùy chọn
 *
 * Nếu không truyền startDate/endDate: tự động lấy từ thứ 2 đến Chủ nhật của tuần hiện tại
 * Nếu không truyền scheduleType: trả về lịch duty
 */
module.exports = (req, res) => {
  const {
    userId,
    startDate,
    endDate,
    scheduleType = 'duty'
  } = req.body;

  let result;
  let userInfo;

  const validateParams = (next) => {
    const schema = Joi.object({
      // Chỉ chấp nhận định dạng DD-MM-YYYY
      userId: Joi.objectId().required(),
      startDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).optional(),
      endDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).optional(),
      scheduleType: Joi.string().valid('work', 'duty', 'meeting').optional()
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    // Kiểm tra startDate <= endDate nếu có cả hai
    if (startDate && endDate) {
      if (DateUtils.compareDDMMYYYY(startDate, endDate) > 0) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGES.ATTENDANCE.WRONG_DATE
        });
      }
    }

    next();
  };

  const getGroupedSchedule = (next) => {
    try {
      const params = {
        startDate,
        endDate,
        scheduleType
      };

      scheduleAggregationService.getGroupedSchedule(userId, params)
        .then((response) => {
          if (!response || !response.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: response.message || MESSAGES.SYSTEM.ERROR
            });
          }

          result = response;
          next();
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  const getUserInfo = (next) => {
    UserModel
      .findById(userId)
      .select('name avatar idNumber units positions phones email')
      .populate('units', 'name')
      .populate('positions', 'name')
      .lean()
      .exec((err, user) => {
        if (err) {
          return next(err);
        }

        if (!user) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: {
              head: 'Thông báo',
              body: 'Người dùng không tồn tại'
            }
          });
        }

        userInfo = user;
        next();
      });
  };

  const formatResponse = (next) => {
    const { data } = result;

    // Format response theo chuẩn của hệ thống
    const response = {
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        userInfo,
        // Thông tin khoảng thời gian
        dateRange: {
          startDate: data.dateRange.startDate,
          endDate: data.dateRange.endDate,
          type: (startDate && endDate) ? 'custom' : 'current_week'
        },

        // Thống kê tổng quan
        summary: {
          totalSchedules: data.totalSchedules,
          totalDays: data.schedulesByDate.length,
          scheduleTypes: data.summary.byType,
          totalLeaveDays: data.summary.totalLeaveDays || 0,
          averagePerDay: data.schedulesByDate.length > 0
            ? Math.round(data.totalSchedules / data.schedulesByDate.length * 10) / 10
            : 0
        },

        // Lịch được nhóm theo ngày
        schedulesByDate: data.schedulesByDate.map(dayData => ({
          date: dayData.date,
          dayOfWeek: dayData.dayOfWeek,
          totalSchedules: dayData.totalSchedules,
          leaveInfo: dayData.leaveInfo,
          schedules: dayData.schedules.map(schedule => {
            // Format chung cho tất cả loại lịch
            const formattedSchedule = {
              id: schedule.id,
              type: schedule.type,
              title: schedule.title,
              date: schedule.date
            };

            // Thêm thông tin cụ thể theo loại lịch
            switch (schedule.type) {
              case 'work':
                formattedSchedule.shifts = schedule.shifts;
                formattedSchedule.startTime = schedule.sortTime;
                // formattedSchedule.user = schedule.user;
                // formattedSchedule.createdBy = schedule.createdBy;
                break;

              case 'duty':
                formattedSchedule.startTime = schedule.startTime;
                formattedSchedule.endTime = schedule.endTime;
                // formattedSchedule.location = schedule.location;
                // formattedSchedule.unit = schedule.unit;
                // formattedSchedule.officer = schedule.officer;
                // formattedSchedule.assignedBy = schedule.assignedBy;
                // formattedSchedule.status = schedule.status;
                // formattedSchedule.forLeader = schedule.forLeader;
                formattedSchedule.description = schedule.description;
                formattedSchedule.notes = schedule.notes;
                formattedSchedule.hasEquipment = schedule.hasEquipment;
                break;

              case 'meeting':
                formattedSchedule.hasDetails = true;
                formattedSchedule.startTime = schedule.startTime;
                formattedSchedule.endTime = schedule.endTime;
                formattedSchedule.topic = schedule.topic;
                formattedSchedule.content = schedule.content;
                formattedSchedule.address = schedule.address;
                // formattedSchedule.officers = schedule.officers;
                // formattedSchedule.assignedBy = schedule.assignedBy;
                formattedSchedule.attachments = schedule.attachments;
                break;
            }

            formattedSchedule.createdAt = schedule.createdAt;
            return formattedSchedule;
          })
        }))
      }
    };

    next(null, response);
  };

  async.waterfall([
    validateParams,
    getUserInfo,
    getGroupedSchedule,
    formatResponse
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body)
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    })

    res.json(data || err)
  });
};

const _ = require('lodash');
const async = require('async');
const Joi = require('joi');

const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const ScreenshotLogModel = require('../../../models/screenshotLog');

/**
 * API lưu log chụp màn hình
 * POST /api/v1.0/screenshot-log/create
 *
 * Lưu thông tin log khi người dùng thực hiện chụp màn hình
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const {
    device,
    app,
    screen,
    location,
    note
  } = req.body;

  /**
   * Validate tham số đầu vào
   */
  const validateParams = (next) => {
    const schema = Joi.object({
      device: Joi.object({
        type: Joi.string().valid('ios', 'android').required(),
        model: Joi.string().allow(''),
        osVersion: Joi.string().allow('')
      }).required(),
      app: Joi.object({
        version: Joi.string().allow(''),
        buildNumber: Joi.string().allow('')
      }),
      screen: Joi.object({
        name: Joi.string().allow(''),
        data: Joi.object().unknown(true)
      }),
      location: Joi.object({
        lat: Joi.number(),
        lng: Joi.number(),
        address: Joi.string().allow('')
      }),
      note: Joi.string().max(500).allow('')
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  /**
   * Lưu log chụp màn hình
   */
  const saveScreenshotLog = (next) => {
    try {
      // Tạo đối tượng log mới
      const screenshotLog = new ScreenshotLogModel({
        user: userId,
        device,
        app,
        screen,
        location,
        note
      });

      // Lưu vào database
      screenshotLog.save((err, savedLog) => {
        if (err) {
          console.error('Error saving screenshot log:', err);
          return next(err);
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS
        });
      });
    } catch (error) {
      console.error('Exception saving screenshot log:', error);
      return next(error);
    }
  };

  // Thực thi các bước xử lý
  async.waterfall([
    validateParams,
    saveScreenshotLog
  ], (err) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Xử lý lỗi hệ thống
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};

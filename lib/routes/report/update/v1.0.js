const _ = require('lodash')
const async = require('async')
const Joi = require('joi')
Joi.objectId = require('joi-objectid')(Joi)
const Report = require('../../../models/report')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const StatisticsTrigger = require('../../../utils/statisticsTrigger')

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  const {
    reportId,
    caseCode,
    workStatus,
    title,
    description,
    metrics
  } = req.body

  let report;
  let oldReport;

  const validateParams = (next) => {
    const schema = Joi.object().keys({
      reportId: Joi.objectId().optional(),
      caseCode: Joi.string().optional(),
      workStatus: Joi.string().valid('pending', 'in_progress', 'completed', 'cancelled', 'on_hold').optional(),
      title: Joi.string().optional(),
      description: Joi.string().optional().allow(''),
      metrics: Joi.object().optional()
    }).or('reportId', 'caseCode')

    const { error } = schema.validate(req.body, { allowUnknown: true })
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next();
  }

  const updateReport = (next) => {
    const query = {
      deletedAt: { $exists: false }
    }
    if (reportId) {
      query._id = reportId
    }
    if (caseCode) {
      query.caseCode = caseCode
    }

    // Lấy dữ liệu cũ trước khi update
    Report.findOne(query)
    .lean()
    .exec((err, oldReportData) => {
      if (err) {
        return next(err)
      }

      if (!oldReportData) {
        return next({
          code: CONSTANTS.CODE.NOT_FOUND,
          message: {
            head: 'Thông báo',
            body: 'Không tìm thấy báo cáo. Vui lòng kiểm tra lại.'
          }
        })
      }

      oldReport = oldReportData;

      const objUpdate = {
        updatedAt: Date.now()
      }

      if (workStatus) {
        objUpdate.workStatus = workStatus
      }

      if (title) {
        objUpdate.title = title
      }

      if (description) {
        objUpdate.description = description
      }

      if (metrics) {
        objUpdate.metrics = metrics
      }

      // Thực hiện update
      Report.findOneAndUpdate(query, objUpdate, {
        new: true
      })
      .populate({
        path: 'details',
        populate: {
          path: 'areas',
          select: 'name level'
        }
      })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err)
        }

        report = result
        next()
      })
    })
  }

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: report,
      message: {
        head: 'Thông báo',
        body: 'Cập nhật báo cáo thành công'
      }
    })

    // Trigger statistics update cho tất cả reports
    try {
      if (oldReport && report) {
        // Trigger cho document reports (nếu là document)
        StatisticsTrigger.triggerDocumentUpdate('update', report, oldReport);

        // Trigger cho tất cả reports để cập nhật statistics
        StatisticsTrigger.triggerReportUpdate('update', report);
      }
    } catch (error) {
      console.error('Error triggering report update:', error);
    }

    // Log system action (nếu có SystemLogModel)
    if (global.SystemLogModel) {
      SystemLogModel.create({
        user: userId,
        action: 'update_report',
        description: 'Cập nhật báo cáo',
        data: req.body,
        updatedData: report
      }, () => { })
    }
  }

  async.waterfall([
    validateParams,
    updateReport,
    writeLog
  ], (err, data) => {
    console.log('err', err, data)
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body)
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    })

    res.json(data || err)
  })
}
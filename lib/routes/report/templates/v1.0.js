const _ = require('lodash')
const async = require('async')
const JobType = require('../../../models/jobType')
const User = require('../../../models/user')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')

module.exports = (req, res) => {
  const { jobTypeId } = req.body
  const userId = req.user.id;

  const getUserInf = (next) => {
    User.findOne({
      _id: userId,
      status: 1
    }, "jobTypes units")
      .populate('jobTypes', 'name')
      .lean()
      .exec((err, user) => {
        if (err) {
          return next(err)
        }

        if (!user) {
          return next({
            code: CONSTANTS.CODE.NOT_FOUND,
            message: MESSAGES.USER.NOT_EXISTS
          })
        }

        next(null, user);
      })
  }

  const getJobTypes = (user, next) => {
    let query = {
      status: 1,
      deletedAt: { $exists: false }
    }

    // Nếu user có jobTypes cụ thể, chỉ lấy những jobType đó
    if (user && user.jobTypes && user.jobTypes.length > 0) {
      query._id = { $in: user.jobTypes }
    }

    // Nếu user có units cụ thể, chỉ lấy những jobType trong units đó
    if (user && user.units && user.units.length > 0) {
      query.unit = { $in: user.units }
    }

    // Nếu có jobTypeId cụ thể
    if (jobTypeId) {
      query._id = jobTypeId
    }

    JobType
      .find(query, 'name description unit quickReport detailReport quickReportTemplate detailReportTemplate icon')
      .populate('unit', 'name')
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err)
        }

        if (jobTypeId && !results.length) {
          return next({
            code: CONSTANTS.CODE.NOT_FOUND,
            message: 'Không tìm thấy công việc'
          })
        }

        next(null, results)
      })
  }

  const formatResponse = (jobTypes, next) => {
    const formattedJobTypes = jobTypes.map(jobType => ({
      _id: jobType._id,
      name: jobType.name,
      description: jobType.description,
      unit: jobType.unit,
      quickReport: jobType.quickReport,
      detailReport: jobType.detailReport,
      icon: jobType.icon,
      // Template cho báo cáo quick
      quickReportTemplate: jobType.quickReportTemplate || {
        requiredFields: [],
        optionalFields: [],
        requiresLocation: false,
        requiresTime: false,
        chartTypes: ['line', 'bar'],
        metrics: {}
      },

      // Template cho báo cáo detail
      detailReportTemplate: jobType.detailReportTemplate || {
        requiresCaseCode: true,
        allowedStatuses: ['pending', 'in_progress', 'completed', 'cancelled', 'on_hold'],
        requiresLocation: false,
        requiresTime: false,
        additionalFields: {}
      }
    }))

    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: jobTypeId ? formattedJobTypes[0] : formattedJobTypes,
      // message: jobTypeId ? 'Lấy template thành công' : 'Lấy danh sách templates thành công'
    })
  }

  async.waterfall([
    getUserInf,
    getJobTypes,
    formatResponse
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body)
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    })

    res.json(data || err)
  })
}
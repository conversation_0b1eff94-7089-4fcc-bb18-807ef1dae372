const _ = require('lodash')
const async = require('async')
const Joi = require('joi')
Joi.objectId = require('joi-objectid')(Joi)
const Report = require('../../../models/report')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')

module.exports = (req, res) => {
  const {
    reportId,
    date,
    jobType
  } = req.body

  const validateParams = (next) => {
    const schema = Joi.object({
      reportId: Joi.objectId().optional(),
      date: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).optional(),
      jobType: Joi.objectId().optional()
    }).or('reportId', 'date').and('date', 'jobType')

    const { error } = schema.validate(req.body, { allowUnknown: true })
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }

    next()
  }

  const getReport = (next) => {
    let query = { deletedAt: { $exists: false } }

    if (reportId) {
      query._id = reportId
    } else {
      query.date = date
      query.jobType = jobType
    }

    Report
      .findOne(query)
      .populate('jobType', 'name description')
      .populate('units', 'name')
      .populate({
        path: 'details',
        populate: {
          path: 'areas',
          select: 'name level'
        }
      })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err)
        }

        // Đảm bảo details luôn là array, ngay cả khi không có data
        if (result && !result.details) {
          result.details = []
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: result
        })
      })
  }

  async.waterfall([
    validateParams,
    getReport
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body)
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    })

    res.json(data || err)
  })
}
const _ = require('lodash');
const async = require('async');
const Joi = require('joi');

const scheduleService = require('../../../services/scheduleService');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

/**
 * API lấy lịch làm việc của cán bộ
 * POST /api/v1.0/work-schedule/user
 */
module.exports = (req, res) => {
  const userId = req.user.id; // Lấy lịch của chính mình
  const {
    startDate,
    endDate
  } = req.body;

  const validateParams = (next) => {
    const schema = Joi.object({
      // Chỉ chấp nhận định dạng DD-MM-YYYY
      startDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).optional(),
      endDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).optional()
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    // Kiểm tra startDate <= endDate nếu có cả hai (định dạng DD-MM-YYYY)
    if (startDate && endDate) {
      // Import DateUtils để so sánh
      const DateUtils = require('../../../utils/dateUtils');
      if (DateUtils.compareDDMMYYYY(startDate, endDate) > 0) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGES.ATTENDANCE.WRONG_DATE
        });
      }
    }

    next();
  };

  const getUserSchedule = (next) => {
    try {
      scheduleService.getUserSchedule(userId, startDate, endDate)
        .then((res) => {
          if (!res || !res.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: res.message || MESSAGES.SYSTEM.ERROR
            });
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            // message: res.message,
            data: res.data
          });
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  async.waterfall([
    validateParams,
    getUserSchedule
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};
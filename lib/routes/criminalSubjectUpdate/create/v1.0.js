const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Jo<PERSON>);

const CriminalSubjectModel = require('../../../models/criminalSubject');
const CriminalSubjectUpdateModel = require('../../../models/criminalSubjectUpdate');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

/**
 * API tạo bản cập nhật theo dõi đối tượng hình sự
 * POST /api/v1.0/criminal-subject-update/create
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const {
    subjectId,
    contactDate,
    livingCondition,
    populationMovement,
    abnormalSigns,
    attachments = []
  } = req.body;

  let newUpdate;

  // Validation schema
  const schema = Joi.object({
    subjectId: Joi.objectId().required(),
    contactDate: Joi.number().integer().required(),
    livingCondition: Joi.string().trim().max(1000),
    populationMovement: Joi.string().trim().max(1000),
    abnormalSigns: Joi.string().trim().max(1000),
    attachments: Joi.array().items(Joi.string().uri())
  });

  const validateParams = (next) => {
    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const checkSubjectExists = (next) => {
    CriminalSubjectModel.findOne({
      _id: subjectId,
      status: 1
    }, 'name').lean().exec((err, subject) => {
      if (err) {
        return next(err);
      }

      if (!subject) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGES.CRIMINAL_SUBJECT.NOT_FOUND
        });
      }

      next();
    });
  };

  const createUpdate = (next) => {
    const updateData = {
      subjectId,
      contactDate,
      updatedBy: userId,
      createdAt: Date.now()
    };

    // Optional fields
    if (livingCondition && livingCondition.trim()) {
      updateData.livingCondition = livingCondition.trim();
    }
    if (populationMovement && populationMovement.trim()) {
      updateData.populationMovement = populationMovement.trim();
    }
    if (abnormalSigns && abnormalSigns.trim()) {
      updateData.abnormalSigns = abnormalSigns.trim();
    }
    if (attachments && attachments.length > 0) {
      updateData.attachments = attachments;
    }

    const update = new CriminalSubjectUpdateModel(updateData);
    update.save((err, result) => {
      if (err) {
        return next(err);
      }

      newUpdate = result;
      next();
    });
  };

  const updateSubjectTimestamp = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: {
        head: 'Thông báo',
        body: 'Tạo bản cập nhật theo dõi thành công'
      },
      data: newUpdate
    });

    // Cập nhật updatedAt của subject
    CriminalSubjectModel.updateOne(
      { _id: subjectId },
      { $set: { updatedAt: Date.now() } },
      (err) => { }
    );
  };

  // Execute waterfall
  async.waterfall([
    validateParams,
    checkSubjectExists,
    createUpdate,
    updateSubjectTimestamp
  ], (err, data) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Handle system errors
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};

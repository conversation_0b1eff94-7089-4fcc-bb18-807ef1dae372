const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Jo<PERSON>);

const CriminalSubjectModel = require('../../../models/criminalSubject');
const CriminalSubjectUpdateModel = require('../../../models/criminalSubjectUpdate');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

/**
 * API cập nhật bản cập nhật theo dõi đối tượng hình sự
 * POST /api/v1.0/criminal-subject-update/update
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const {
    updateId,
    contactDate,
    livingCondition,
    populationMovement,
    abnormalSigns,
    attachments
  } = req.body;

  let existingUpdate;

  // Validation schema
  const schema = Joi.object({
    updateId: Joi.objectId().required(),
    contactDate: Joi.number().integer().optional().allow(''),
    livingCondition: Joi.string().trim().max(1000).optional().allow(''),
    populationMovement: Joi.string().trim().max(1000).optional().allow(''),
    abnormalSigns: Joi.string().trim().max(1000).optional().allow(''),
    attachments: Joi.array().items(Joi.string().uri()).optional()
  });

  const validateParams = (next) => {
    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const checkUpdateExists = (next) => {
    CriminalSubjectUpdateModel.findOne({
      _id: updateId,
      status: 1
    }, 'subjectId').lean().exec((err, update) => {
      if (err) {
        return next(err);
      }

      if (!update) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGES.CRIMINAL_SUBJECT.UPDATE_NOT_FOUND
        });
      }

      existingUpdate = update;
      next();
    });
  };

  const update = (next) => {
    const updateData = {
      subjectId: existingUpdate.subjectId,
      contactDate: contactDate || existingUpdate.contactDate,
      updatedBy: userId,
      updatedAt: Date.now()
    };

    // Optional fields
    if (livingCondition && livingCondition.trim()) {
      updateData.livingCondition = livingCondition.trim();
    }
    if (populationMovement && populationMovement.trim()) {
      updateData.populationMovement = populationMovement.trim();
    }
    if (abnormalSigns && abnormalSigns.trim()) {
      updateData.abnormalSigns = abnormalSigns.trim();
    }
    if (attachments) {
      updateData.attachments = attachments;
    }

    CriminalSubjectUpdateModel.findOneAndUpdate(
      { _id: updateId },
      updateData,
      { new: true, upsert: true }
    ).exec((err, result) => {
      if (err) {
        return next(err);
      }

      existingUpdate = result;
      next();
    });
  };

  const formatResponse = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: {
        head: 'Thông báo',
        body: 'Cập nhật thành công'
      },
      data: existingUpdate
    });

    // Cập nhật updatedAt của subject
    CriminalSubjectModel.updateOne(
      { _id: existingUpdate.subjectId },
      { $set: { updatedAt: Date.now() } },
      (err) => { }
    );
  };

  // Execute waterfall
  async.waterfall([
    validateParams,
    checkUpdateExists,
    update,
    formatResponse
  ], (err, data) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Handle system errors
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};

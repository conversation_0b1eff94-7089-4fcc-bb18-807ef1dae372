const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Jo<PERSON>);

const CriminalSubjectUpdateModel = require('../../../models/criminalSubjectUpdate');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

/**
 * API lấy thông tin chi tiết bản cập nhật theo dõi
 * POST /api/v1.0/criminal-subject-update/get
 */
module.exports = (req, res) => {
  const { updateId } = req.body;

  let updateInfo;

  // Validation schema
  const schema = Joi.object({
    updateId: Joi.objectId().required()
  });

  const validateParams = (next) => {
    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const getUpdateInfo = (next) => {
    CriminalSubjectUpdateModel.findOne({
      _id: updateId,
      status: 1
    }, '-status -updatedBy')
      .populate({
        path: 'subjectId',
        select: 'name idNumber category dangerLevel photos permanentAddress managingUnit assignedOfficers',
        populate: [
          { path: 'managingUnit', select: 'name' },
          {
            path: 'assignedOfficers',
            select: 'name idNumber avatar units positions',
            populate: [
              {
                path: 'units',
                select: 'name'
              },
              {
                path: 'positions',
                select: 'name'
              }
            ],
          },
        ],
      })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Không tìm thấy bản cập nhật'
            }
          });
        }

        updateInfo = result;
        next();
      });
  };

  const getRelatedUpdates = (next) => {
    if (!updateInfo.subjectId || !updateInfo.subjectId._id) {
      return next();
    }

    // Lấy 5 bản cập nhật gần nhất của cùng đối tượng (không bao gồm bản hiện tại)
    CriminalSubjectUpdateModel.find({
      subjectId: updateInfo.subjectId._id,
      _id: { $ne: updateId },
      status: 1
    }, '-status -updatedBy')
      .sort({ createdAt: -1 })
      .limit(5)
      .lean()
      .exec((err, relatedUpdates) => {
        if (err) {
          logger && logger.logError(['Error getting related updates:', err], req.originalUrl, req.body);
          // Không fail toàn bộ process nếu lỗi này
          relatedUpdates = [];
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: {
            update: updateInfo,
            relatedUpdates: relatedUpdates || []
          }
        });
      });
  };

  // Execute waterfall
  async.waterfall([
    validateParams,
    getUpdateInfo,
    getRelatedUpdates
  ], (err, data) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Handle system errors
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};

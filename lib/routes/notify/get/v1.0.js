const _ = require('lodash')
const async = require('async')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const SavedNotificationModel = require('../../../models/savedNotification')
const UserModel = require('../../../models/user')

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  const notificationId = _.get(req, 'body.id') || _.get(req, 'body._id');
  
  // Validate parameters
  if (!notificationId) {
    return res.json({
      code: CONSTANTS.CODE.WRONG_PARAMS,
      message: MESSAGES.SYSTEM.WRONG_PARAMS
    });
  }

  async.waterfall([
    // Bước 1: Lấy thông tin user để có units
    (callback) => {
      UserModel.findById(userId).select('units').exec((err, user) => {
        if (err) {
          return callback({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          });
        }
        
        if (!user) {
          return callback({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          });
        }
        
        callback(null, user.units || []);
      });
    },
    
    // Bước 2: Lấy thông báo và kiểm tra quyền truy cập
    (userUnits, callback) => {
      SavedNotificationModel
        .findOne({ 
          _id: notificationId,
          status: 1 
        })
        .select('title image data seen createdAt updatedAt type units users')
        .exec((err, notification) => {
          if (err) {
            return callback({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: MESSAGES.SYSTEM.ERROR
            });
          }
          
          if (!notification) {
            return callback({
              code: CONSTANTS.CODE.WRONG_PARAMS,
              message: MESSAGES.SYSTEM.WRONG_PARAMS
            });
          }
          
          // Kiểm tra quyền truy cập thông báo
          let hasAccess = false;
          
          if (notification.type === 'all') {
            hasAccess = true;
          } else if (notification.type === 'user') {
            hasAccess = notification.users.includes(userId);
          } else if (notification.type === 'unit') {
            hasAccess = notification.units.some(unitId => 
              userUnits.includes(unitId.toString())
            );
          }
          
          if (!hasAccess) {
            return callback({
              code: CONSTANTS.CODE.ACCESS_DENINED,
              message: MESSAGES.SYSTEM.WRONG_PARAMS
            });
          }
          
          callback(null, notification);
        });
    },
    
    // Bước 3: Xử lý kết quả và thêm thông tin hasSeen
    (notification, callback) => {
      const notif = notification.toObject();
      notif.hasSeen = notification.seen.includes(userId);
      
      // Xóa các field không cần thiết
      delete notif.seen;
      delete notif.units;
      delete notif.users;
      
      const result = {
        code: CONSTANTS.CODE.SUCCESS,
        data: notif
      };
      
      callback(null, result);
    }
    
  ], (err, data) => {
    if (err && _.isError(err)) {
      data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      };
    }

    res.json(data || err);
  });
}

const _ = require('lodash')
const async = require('async')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const SavedNotificationModel = require('../../../models/savedNotification')
const UserModel = require('../../../models/user')

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  const notificationId = _.get(req, 'body._id');
  
  // Validate parameters
  if (!notificationId) {
    return res.json({
      code: CONSTANTS.CODE.WRONG_PARAMS,
      message: MESSAGES.SYSTEM.WRONG_PARAMS
    });
  }

  async.waterfall([
    // Bước 1: Lấy thông tin user để có units
    (callback) => {
      UserModel.findById(userId).select('units').exec((err, user) => {
        if (err) {
          return callback({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          });
        }
        
        if (!user) {
          return callback({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          });
        }
        
        callback(null, user.units || []);
      });
    },
    
    // Bước 2: Kiểm tra thông báo và quyền truy cập
    (userUnits, callback) => {
      SavedNotificationModel
        .findOne({ 
          _id: notificationId,
          status: 1 
        })
        .select('type units users seen')
        .exec((err, notification) => {
          if (err) {
            return callback({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: MESSAGES.SYSTEM.ERROR
            });
          }
          
          if (!notification) {
            return callback({
              code: CONSTANTS.CODE.WRONG_PARAMS,
              message: MESSAGES.SYSTEM.WRONG_PARAMS
            });
          }
          
          // Kiểm tra quyền truy cập thông báo
          let hasAccess = false;
          
          if (notification.type === 'all') {
            hasAccess = true;
          } else if (notification.type === 'user') {
            hasAccess = notification.users.includes(userId);
          } else if (notification.type === 'unit') {
            hasAccess = notification.units.some(unitId => 
              userUnits.includes(unitId.toString())
            );
          }
          
          if (!hasAccess) {
            return callback({
              code: CONSTANTS.CODE.ACCESS_DENINED,
              message: MESSAGES.SYSTEM.WRONG_PARAMS
            });
          }
          
          callback(null, notification);
        });
    },
    
    // Bước 3: Đánh dấu đã xem
    (notification, callback) => {
      // Kiểm tra xem user đã xem chưa
      if (notification.seen.includes(userId)) {
        // Đã xem rồi, trả về thành công luôn
        return callback(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: {
            alreadySeen: true
          }
        });
      }
      
      // Thêm userId vào mảng seen
      SavedNotificationModel.updateOne(
        { _id: notificationId },
        { 
          $addToSet: { seen: userId },
          $set: { updatedAt: Date.now() }
        },
        (err, result) => {
          if (err) {
            return callback({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: MESSAGES.SYSTEM.ERROR
            });
          }
          
          const response = {
            code: CONSTANTS.CODE.SUCCESS,
            data: {
              marked: true
            }
          };
          
          callback(null, response);
        }
      );
    }
    
  ], (err, data) => {
    if (err && _.isError(err)) {
      data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      };
    }

    res.json(data || err);
  });
}

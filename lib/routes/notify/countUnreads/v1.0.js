const _ = require('lodash')
const async = require('async')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const SavedNotificationModel = require('../../../models/savedNotification')
const UserModel = require('../../../models/user')

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');

  async.waterfall([
    // Bước 1: Lấy thông tin user để có units
    (callback) => {
      UserModel.findById(userId).select('units').exec((err, user) => {
        if (err) {
          return callback({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          });
        }
        
        if (!user) {
          return callback({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          });
        }
        
        callback(null, user.units || []);
      });
    },
    
    // Bước 2: <PERSON>ế<PERSON> số thông báo chưa đọc
    (userUnits, callback) => {
      const query = {
        $and: [
          { status: 1 }, // Thông báo đang hoạt động
          { seen: { $ne: userId } }, // Chưa được user này đọc
          {
            $or: [
              { type: 'all' },
              { type: 'user', users: userId },
              { type: 'unit', units: { $in: userUnits } }
            ]
          }
        ]
      };
      
      SavedNotificationModel.countDocuments(query, (err, count) => {
        if (err) {
          return callback({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          });
        }
        
        const result = {
          code: CONSTANTS.CODE.SUCCESS,
          data: count
        };
        
        callback(null, result);
      });
    }
    
  ], (err, data) => {
    if (err && _.isError(err)) {
      data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      };
    }

    res.json(data || err);
  });
}

const _ = require('lodash')
const async = require('async')

const User = require('../../../models/user')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const GroupPermissionModel = require('../../../models/groupPermission')
const SystemLog = require('../../../models/systemLog')

// Import utility function để xử lý cấu trúc phân cấp areas
const { processAreasHierarchy } = require('../../../util/areasHierarchy');


module.exports = (req, res) => {
  const userId = req.user.id || '';
  const appName = _.get(req, 'body.appName', '');
  const platform = _.get(req, 'body.platform', 'web');
  let role = 'officer'; // Mặc định là officer
  let userInf;
  const checkParams = (next) => {
    if(!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }
    next();
  }

  const getUser = (next) => {
    User
      .findById(userId)
      .populate("permissions", "code active status -_id")
      .populate({
        path: "units",
        select: "name parentPath icon",
        populate: {
          path: "parentPath",
          select: "name",
        },
      })
      .populate("positions", "name unit role isTeamLeader")
      .populate({
        path: "areas",
        select: "name level parent parentPath",
        populate: {
          path: "parent",
          select: "name",
        },
      })
      .select('-password')
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          })
        }
        userInf = result;
        if(userInf.units && userInf.units.length && userInf.units.length === 1) {
          role = 'leader'
        } else if(userInf.positions && userInf.positions.some(pos => pos.isTeamLeader === true)) {
          role = 'team_leader'
        }
        userInf.role = role;
        next()
      })
  }

  const processUserAreas = (next) => {
    // Xử lý cấu trúc phân cấp areas nếu user có areas
    if (userInf && userInf.areas && userInf.areas.length > 0) {
      userInf.areas = processAreasHierarchy(userInf.areas);
    }
    next();
  }

  const findPermissionsInGroup = (next) => {
    GroupPermissionModel
      .find({
        _id:{
          $in: userInf.groupPermissions
        }
      })
      .populate('permissions', 'code -_id')
      .lean()
      .exec((err, results) => {
        if(err) {
          return next(err);
        }
        results.map((groupPermission) => {
          userInf.permissions = userInf.permissions.concat(groupPermission.permissions);
        })
        next();
      })
  }

  const trackUserAccess = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: userInf
    });

    SystemLog.create({
      user: userId,
      action: 'user_app_access',
      description: `User accessed app: ${appName}`,
      data: {
        platform: platform,
        appName: appName,
        userAgent: req.headers['user-agent'] || '',
        ip: req.headers.ip || req.ip || req.connection.remoteAddress || req.headers['x-forwarded-for'] || '',
        device: req.headers.device ? JSON.parse(req.headers.device) : {}
      }
    }, (err) => {
      if (err) {
        console.error('Failed to log user access:', err);
      }
    });
  }


  async.waterfall([
    checkParams,
    getUser,
    processUserAreas,
    findPermissionsInGroup,
    trackUserAccess
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
const _ = require('lodash');
const async = require('async');
const ms = require('ms');
const config = require('config');
const util = require('util');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const bcrypt = require('bcryptjs')

const UserModel = require('../../../models/user');
const GroupPermissionModel = require('../../../models/groupPermission');
const redisConnection = require('../../../connections/redis');
const jwt = require('jsonwebtoken');
// const NotifyManager = require('../../../job/notifyManager');
const SystemLog = require('../../../models/systemLog');

// Import utility function để xử lý cấu trúc phân cấp areas
const { processAreasHierarchy } = require('../../../util/areasHierarchy');

module.exports = (req, res) => {
  let username = req.body.username || '';
  const password = req.body.password || '';
  let userInf;
  let token;
  let appName = '';
  if (req.body.appName) {
    appName = req.body.appName;
  }
  if (req.query.appName) {
    appName = req.query.appName;
  }
  let role = 'officer'; // Mặc định là officer

  let stringToken = 'user';
  if (appName && appName !== 'cms') {
    stringToken = appName;
  }

  const checkParams = (next) => {
    username = username.trim();
    if (!username) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Bạn chưa nhập tên đăng nhập',
        },
      });
    }
    if (!password) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Bạn chưa nhập mật khẩu',
        },
      });
    }
    next();
  };

  const findUser = (next) => {
    UserModel.find({
      username,
    })
      .populate('permissions', 'code active status -_id apps')
      .populate({
        path: 'units',
        select: 'name parentPath',
        populate: {
          path: 'parentPath',
          select: 'name',
        },
      })
      .populate('positions', 'name unit isTeamLeader')
      .populate({
        path: "areas",
        select: "name level parent parentPath",
        populate: {
          path: "parent",
          select: "name",
        },
      })
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }
        if (!results.length) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: {
              head: 'Thông báo',
              body: 'Tên đăng nhập chưa chính xác',
            },
          });
        }
        if (results.length > 1) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR,
          });
        }
        userInf = results[0];
         if(userInf.units && userInf.units.length && userInf.units.length === 1) {
          role = 'leader'
        } else if(userInf.positions && userInf.positions.some(pos => pos.isTeamLeader === true)) {
          role = 'team_leader'
        }
        userInf.role = role;

        if (userInf.status == 0) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.USER.INACTIVE,
          });
        }
        if(userInf.block) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.USER.TEMP_ROLE_BLOCK,
          });
        }
        if (_.get(userInf, 'permissions.length') && _.get(userInf, 'permissions', []).some((item) => item.code.includes('admin'))) {
          return next();
        }
        if (!userInf.apps || (userInf.apps && !userInf.apps.includes(appName))) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.USER.ROLE_BLOCK,
          });
        }
       
        next();
      });
  };

  const processUserAreas = (next) => {
    // Xử lý cấu trúc phân cấp areas nếu user có areas
    if (userInf && userInf.areas && userInf.areas.length > 0) {
      userInf.areas = processAreasHierarchy(userInf.areas);
    }
    next();
  }

  const findPermissionsInGroup = (next) => {
    GroupPermissionModel.find({
      _id: {
        $in: userInf.groupPermissions,
      },
    })
      .populate('permissions', 'code -_id')
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }
        results.map((groupPermission) => {
          userInf.permissions = userInf.permissions.concat(groupPermission.permissions);
        });
        next();
      });
  };

  const checkPassword = (next) => {
    if (!userInf.password) {
      return next({
        code: CONSTANTS.CODE.FAIL,
        message: {
          head: 'Thông báo',
          body: 'Bạn chưa có mật khẩu, vui lòng liên hệ admin để được cấp mật khẩu.',
        },
      });
    }
    bcrypt.compare(password, userInf.password, function (err, res) {
      if (err) {
        return next(err);
      }

      if (!res) {
        return next({
          code: CONSTANTS.CODE.FAIL,
          message: {
            head: 'Thông báo',
            body: 'Mật khẩu không chính xác, vui lòng thử lại. Xin cảm ơn.',
          },
        });
      }
      next();
    });
  };

  const deleteOldToken = (next) => {
    const userId = userInf._id.toHexString();
    redisConnection('master')
      .getConnection()
      .get(`${stringToken}:${userId}`, (err, token) => {
        if (err) {
          return next(err);
        }

        if (token) {
          redisConnection('master')
            .getConnection()
            .del(`${stringToken}:${token}`, (err, result) => {
              if (err) {
                return next(err);
              }
              next();
            });
        } else {
          next();
        }
      });
  };

  const createNewToken = (next) => {
    const token = jwt.sign({ username, password, id: userInf._id }, config.secretKey);

    const userId = userInf._id.toHexString();
    const permissions = userInf.permissions;
    const groupPermissions = userInf.groupPermissions;
    const objSign = {
      id: userId,
      permissions,
      groupPermissions,
      role: userInf.role,
    };

    redisConnection('master')
      .getConnection()
      .multi()
      .set(`${stringToken}:${userId}`, token)
      .set(`${stringToken}:${token}`, JSON.stringify(objSign))
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        const data = _.merge({}, userInf, { token });
        _.unset(data, 'password');
        let deviceName = '';
        let device = {};
        if (req.headers.device) {
          device = JSON.parse(req.headers.device);
          deviceName = `${_.get(device, 'device.brand', '')} ${_.get(device, 'os.name', '')} - ${_.get(device, 'os.version', '')}:${_.get(device, 'client.name', '')} - ${_.get(device, 'client.version', '')}`;
          if (req.headers.ip) {
            device.ip = req.headers.ip;
          }
        }
        // if (deviceName) {
        //   NotifyManager.handleNotify(userId, {
        //     title: 'Thông báo',
        //     message: `Tài khoản của bạn vừa đăng nhập trên thiết bị: ${deviceName}`,
        //     data: {
        //       link: '',
        //     },
        //     eventName: 'noti_update',
        //   });
        // }
        device.appName = appName;
        SystemLog.create(
          {
            user: userInf._id,
            action: 'log_in',
            description: 'Đăng nhập',
            data: device,
          },
          () => {}
        );
        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data,
        });
      });
  };
  async.waterfall([checkParams, findUser, processUserAreas, findPermissionsInGroup, checkPassword, deleteOldToken, createNewToken], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};

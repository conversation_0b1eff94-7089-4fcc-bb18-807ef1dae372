const _ = require('lodash');
const async = require('async');
const mongoose = require('mongoose');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const DutyPatrolScheduleModel = require('../../../models/dutyPatrolSchedule');
const DutyShiftModel = require('../../../models/dutyShift');
const UserModel = require('../../../models/user');
const UnitModel = require('../../../models/unit');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  const { _id, templateId, data } = req.body; // _id: ID của schedule, templateId: ID của phần tử trong weeklyScheduleTemplate, data: array [{unit, requiredOfficer}]

  const checkParams = (next) => {
    if (!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    if (!_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp ID lịch tuần tra'
        }
      });
    }

    if (!templateId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp ID của phần tử template'
        }
      });
    }

    if (!data || !Array.isArray(data) || data.length === 0) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp Ca trực với ít nhất một phần tử'
        }
      });
    }

    // Validate từng phần tử trong data
    for (let i = 0; i < data.length; i++) {
      const item = data[i];
      
      if (!item.unit) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: `Phần tử ${i + 1}: Vui lòng cung cấp ID của Tổ công tác`
          }
        });
      }

      if (typeof item.requiredOfficer !== 'number' || item.requiredOfficer < 0) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: `Phần tử ${i + 1}: Số cán bộ yêu cầu phải là số >= 0`
          }
        });
      }
    }

    next();
  };

  const validateMaxOfficer = (next) => {
    // Validate từng unit trong data
    async.eachSeries(data, (item, callback) => {
      // Lấy thông tin unit và đếm số lượng user thuộc unit
      async.parallel({
        unit: (cb) => {
          UnitModel.findById(item.unit).select('name').lean().exec(cb);
        },
        userCount: (cb) => {
          UserModel.countDocuments({
            units: new mongoose.Types.ObjectId(item.unit),
            status: 1
          }, cb);
        }
      }, (err, results) => {
        if (err) {
          return callback(err);
        }

        const unitName = results.unit ? results.unit.name : item.unit;
        const maxOfficer = results.userCount;

        if (item.requiredOfficer > maxOfficer) {
          return callback({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: `"${unitName}": Số lượng cán bộ yêu cầu (${item.requiredOfficer}) không thể vượt quá số lượng cán bộ tối đa của tổ công tác (${maxOfficer})`
            }
          });
        }

        callback();
      });
    }, (err) => {
      if (err) {
        return next(err);
      }
      next();
    });
  };



  const updateWeeklyScheduleTemplate = (next) => {
    // Lấy dữ liệu hiện tại
    DutyPatrolScheduleModel.findOne({ _id: _id, status: 1 })
      .lean()
      .exec((err, schedule) => {
        if (err) {
          return next(err);
        }

        if (!schedule) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'lịch tuần tra không tồn tại hoặc đã bị xóa'
            }
          });
        }

        // Tạo bản sao của weeklyScheduleTemplate hiện tại
        let updatedTemplate = [...(schedule.weeklyScheduleTemplate || [])];

        // Tìm và cập nhật phần tử có templateId khớp
        const index = updatedTemplate.findIndex(item => item._id.toString() === templateId);
        if (index === -1) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Không tìm thấy tổ công tác đã cung cấp'
            }
          });
        }

        // Tìm và cập nhật các phần tử có unit khớp trong data của template
        const currentItem = updatedTemplate[index];
        const updatedData = currentItem.data.map(shift => {
          // Tìm unit cần cập nhật trong mảng data được gửi lên
          const updateItem = data.find(item => {
            const shiftUnitId = shift.unit && shift.unit._id ? shift.unit._id.toString() : (shift.unit ? shift.unit.toString() : null);
            const targetUnitId = item.unit.toString();
            return shiftUnitId === targetUnitId;
          });
          
          if (updateItem) {
            return {
              ...shift,
              requiredOfficer: updateItem.requiredOfficer
            };
          }
          return shift;
        });

        // Kiểm tra xem có tìm thấy unit để cập nhật không
        const foundUnits = [];
        const notFoundUnits = [];
        
        data.forEach(item => {
          const found = currentItem.data.some(shift => {
            const shiftUnitId = shift.unit && shift.unit._id ? shift.unit._id.toString() : (shift.unit ? shift.unit.toString() : null);
            return shiftUnitId === item.unit.toString();
          });
          
          if (found) {
            foundUnits.push(item.unit);
          } else {
            notFoundUnits.push(item.unit);
          }
        });

        if (notFoundUnits.length > 0) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: `Không tìm thấy các Tổ công tác sau trong template để cập nhật: ${notFoundUnits.join(', ')}`
            }
          });
        }

        updatedTemplate[index] = {
          ...currentItem,
          data: updatedData
        };

        // Cập nhật vào database
        DutyPatrolScheduleModel.findOneAndUpdate(
          { _id: _id, status: 1 },
          { 
            $set: { 
              weeklyScheduleTemplate: updatedTemplate,
              updatedAt: Date.now()
            }
          },
          { new: true }
        )
        .lean()
        .exec((err, updatedSchedule) => {
          if (err) {
            return next(err);
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            message: {
              head: 'Thông báo',
              body: 'Cập nhật lịch tuần tra thành công'
            },
            data: updatedSchedule
          });
        });
      });
  };

  async.waterfall([checkParams, validateMaxOfficer, updateWeeklyScheduleTemplate], (err, data) => {
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};

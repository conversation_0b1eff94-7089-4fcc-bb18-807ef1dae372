const _ = require('lodash');
const async = require('async');
const Joi = require('joi');

const leaveService = require('../../../services/leaveService');
const StatisticsTrigger = require('../../../utils/statisticsTrigger');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const DateUtils = require('../../../utils/dateUtils');

/**
 * API tạo đơn xin nghỉ phép
 * POST /api/v1.0/leave-request/create
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const {
    type,
    startDate,
    endDate,
    shift,
    reason,
    attachments = []
  } = req.body;

  let result;

  const validateParams = (next) => {
    const schema = Joi.object({
      type: Joi.string().valid('leave', 'late_arrival', 'emergency_leave').required(),
      startDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).default(DateUtils.getCurrentDateDDMMYYYY()),
      endDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).optional(),
      shift: Joi.string().valid('morning', 'afternoon', 'both').optional(),
      reason: Joi.string().min(1).max(500).required(),
      attachments: Joi.array().items(Joi.string().uri()).default([])
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    // Validate logic theo loại đơn
    if (type === 'leave' && !endDate) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi ngày nghỉ',
          body: 'Đơn xin nghỉ phép phải có ngày kết thúc'
        }
      });
    }

    if (endDate && DateUtils.compareDDMMYYYY(startDate, endDate) > 0) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi ngày nghỉ',
          body: 'Ngày bắt đầu phải nhỏ hơn hoặc bằng ngày kết thúc'
        }
      });
    }

    if ((type === 'late_arrival' || type === 'emergency_leave') && !shift) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi ca làm việc',
          body: 'Đơn xin đi muộn/nghỉ đột xuất phải chọn ca làm việc'
        }
      });
    }

    next();
  };

  const createRequest = (next) => {
    try {
      const requestData = {
        type,
        startDate,
        endDate,
        shift,
        reason,
        attachments
      };

      leaveService.createLeaveRequest(userId, requestData)
        .then((res) => {
          if (!res || !res.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: res.message || MESSAGES.SYSTEM.ERROR
            });
          }

          result = res;

          next();
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: result.message,
      data: result.data
    });

    // Trigger statistics update khi tạo đơn xin nghỉ
    if (result.success && result.data) {
      try {
        StatisticsTrigger.triggerLeaveRequestUpdate('create', {
          _id: result.data._id,
          user: result.data.user,
          type: result.data.type,
          startDate: result.data.startDate,
          endDate: result.data.endDate,
          status: result.data.status
        });
      } catch (error) {
        console.error('Error triggering leave request update:', error);
      }
    }

    // Log hoạt động
    SystemLogModel && SystemLogModel.create({
      user: userId,
      action: 'create_leave_request',
      description: 'Tạo đơn xin nghỉ phép',
      data: req.body,
      updatedData: result.data
    }, () => {});
  };

  async.waterfall([
    validateParams,
    createRequest,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};
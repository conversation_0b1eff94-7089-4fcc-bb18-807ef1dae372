const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);

const leaveService = require('../../../services/leaveService');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const DateUtils = require('../../../utils/dateUtils');

/**
 * API lấy danh sách đơn xin nghỉ phép
 * POST /api/v1.0/leave-request/list
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const {
    status,
    type,
    startDate,
    endDate,
    page = 1,
    limit = 20
  } = req.body;

  const validateParams = (next) => {
    const schema = Joi.object({
      status: Joi.string().valid('pending', 'approved', 'rejected').optional(),
      type: Joi.string().valid('leave', 'late_arrival', 'emergency_leave').optional(),
      startDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).optional(),
      endDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).optional(),
      page: Joi.number().integer().min(1).default(1),
      limit: Joi.number().integer().min(1).max(100).default(20)
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    // Kiểm tra startDate <= endDate nếu có cả hai (định dạng DD-MM-YYYY)
    if (startDate && endDate && DateUtils.compareDDMMYYYY(startDate, endDate) > 0) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.ATTENDANCE.WRONG_DATE
      });
    }

    next();
  };

  const getRequestList = (next) => {
    try {
      const filters = {
        status,
        type,
        startDate,
        endDate,
        page,
        limit
      };

      leaveService.getLeaveRequests(userId, filters)
        .then((res) => {
          if (!res || !res.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: res.message || MESSAGES.SYSTEM.ERROR
            });
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            // message: res.message,
            data: res.data
          });
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  async.waterfall([
    validateParams,
    getRequestList
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};
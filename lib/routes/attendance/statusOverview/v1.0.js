const _ = require('lodash');
const async = require('async');
const Joi = require('joi');

const attendanceOverviewService = require('../../../services/attendanceOverviewService');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

/**
 * API lấy tổng quan trạng thái điểm danh
 * POST /api/v1.0/attendance/status-overview
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const {
    date,
    startDate,
    endDate,
    period = 'day'
  } = req.body;

  let result;

  const validateParams = (next) => {
    const schema = Joi.object({
      date: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).optional(),
      startDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).optional(),
      endDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).optional(),
      period: Joi.string().valid('day', 'week', 'month').default('day').optional()
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    // Validate logic: nếu có startDate thì phải có endDate và ngược lại, không thể có cả date và startDate/endDate
    if ((startDate && !endDate) || (!startDate && endDate) || (date && (startDate || endDate))) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const getStatusOverview = (next) => {
    try {
      const filters = {
        date,
        startDate,
        endDate,
        period
      };

      attendanceOverviewService.getAttendanceStatusOverview(userId, filters)
        .then((res) => {
          if (!res || !res.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: res.message || MESSAGES.SYSTEM.ERROR
            });
          }

          result = res;
          next();
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: result.data
    });
  };

  async.waterfall([
    validateParams,
    getStatusOverview,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};

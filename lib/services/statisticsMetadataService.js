const _ = require('lodash');
const moment = require('moment');
const redisConnection = require('../connections/redis');
const StatisticsUtils = require('../utils/statisticsUtils');
const statisticsService = require('./statisticsService');
const SocketNotifyManager = require('../socket/socketNotifyManager');

/**
 * Service quản lý metadata và cache cho hệ thống thống kê
 * Xử lý tính toán định kỳ, cache dữ liệu và real-time notifications
 */
class StatisticsMetadataService {
  constructor() {
    this.redis = redisConnection('master').getConnection();
    this.triggeredEvents = new Set(); // Lưu trữ các events cần xử lý
  }

  /**
   * Tính toán và cache tất cả metadata thống kê
   * Chạy định kỳ mỗi 10 phút
   */
  async calculateAllMetadata() {
    try {
      console.log(`[${new Date().toISOString()}] Starting statistics metadata calculation...`);

      const timeRanges = ['day', 'week', 'month'];
      const results = {};

      for (const timeRange of timeRanges) {
        try {
          // Tính toán cho từng loại thống kê
          const [
            onDutyOfficers,
            attendance,
            officerSummary,
            officersByArea,
            documentsSummary
          ] = await Promise.all([
            statisticsService.getOnDutyOfficers({ timeRange }),
            statisticsService.getAttendanceStats({ timeRange }),
            statisticsService.getOfficerSummaryStats({ timeRange }),
            statisticsService.getOfficersByAreaStats({ timeRange }),
            statisticsService.getDocumentsSummaryStats({ timeRange })
          ]);

          // Cache kết quả
          await this.cacheStatisticsData('on_duty_officers', onDutyOfficers.data, timeRange);
          await this.cacheStatisticsData('attendance', attendance.data, timeRange);
          await this.cacheStatisticsData('officer_summary', officerSummary.data, timeRange);
          await this.cacheStatisticsData('officers_by_area', officersByArea.data, timeRange);
          await this.cacheStatisticsData('documents_summary', documentsSummary.data, timeRange);

          results[timeRange] = {
            onDutyOfficers: onDutyOfficers.success,
            attendance: attendance.success,
            officerSummary: officerSummary.success,
            officersByArea: officersByArea.success,
            documentsSummary: documentsSummary.success
          };

        } catch (error) {
          console.error(`Error calculating metadata for ${timeRange}:`, error);
          results[timeRange] = { error: error.message };
        }
      }

      console.log(`[${new Date().toISOString()}] Statistics metadata calculation completed:`, results);
      return { success: true, results };

    } catch (error) {
      console.error(`[${new Date().toISOString()}] Statistics metadata calculation failed:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Cache dữ liệu thống kê vào Redis
   * @param {String} type - Loại thống kê
   * @param {Object} data - Dữ liệu cần cache
   */
  async cacheStatisticsData(type, data, ...extraParams) {
    try {
      const cacheKey = (extraParams && extraParams.length > 0)
        ? StatisticsUtils.generateCacheKey(type, ...extraParams)
        : StatisticsUtils.generateCacheKey(type);

      await this.redis.setex(
        cacheKey,
        global.config?.statistics?.cacheTTL || 300, // 5 phút
        JSON.stringify(data)
      );

      // Cache metadata summary
      const summaryKey = StatisticsUtils.generateCacheKey('summary', type);
      const summary = this.extractSummary(type, data);

      await this.redis.setex(
        summaryKey,
        global.config?.statistics?.cacheTTL || 300,
        JSON.stringify(summary)
      );

    } catch (error) {
      console.error(`Error caching statistics data for ${type}:`, error);
    }
  }

  /**
   * Lấy dữ liệu thống kê từ cache
   * @param {String} type - Loại thống kê
   */
  getCachedStatisticsData(type, ...extraParams) {
    return new Promise((resolve, reject) => {
      try {
        const cacheKey = (extraParams && extraParams.length > 0)
          ? StatisticsUtils.generateCacheKey(type, ...extraParams)
          : StatisticsUtils.generateCacheKey(type);

        this.redis.get(cacheKey, (err, cachedData) => {
          if (err) {
            console.error(`Error getting cached statistics data for ${type}:`, err);
            reject(err);
          }

          resolve(cachedData ? JSON.parse(cachedData) : null);
        })
      } catch (error) {
        console.error(`Error getting cached statistics data for ${type}:`, error);
        reject(error);
      }
    });
  }

  /**
   * Trích xuất summary từ dữ liệu thống kê
   * @param {String} type - Loại thống kê
   * @param {Object} data - Dữ liệu thống kê
   * @returns {Object} Summary data
   */
  extractSummary(type, data) {
    switch (type) {
      case 'on_duty_officers':
        return {
          totalOnDuty: data.summary?.totalOnDuty || 0,
          byDutyType: data.summary?.byDutyType || {},
          lastUpdated: Date.now()
        };

      case 'attendance':
        return {
          totalAttendance: data.summary?.totalAttendance || 0,
          attendanceRate: data.summary?.attendanceRate || 0,
          totalScheduled: data.summary?.totalScheduled || 0,
          lastUpdated: Date.now()
        };

      case 'officer_summary':
        return {
          totalOfficers: data.summary?.totalOfficers || 0,
          activeOfficers: data.summary?.activeOfficers || 0,
          workingOfficers: data.workStatus?.working || 0,
          lastUpdated: Date.now()
        };

      case 'officers_by_area':
        return {
          totalAreas: data.summary?.totalAreas || 0,
          totalOfficers: data.summary?.totalOfficers || 0,
          averageOfficersPerArea: data.summary?.averageOfficersPerArea || 0,
          lastUpdated: Date.now()
        };

      case 'reports_by_area':
        return {
          totalAreas: data.summary?.totalAreas || 0,
          totalIncidents: data.summary?.totalIncidents || 0,
          totalReports: data.summary?.totalReports || 0,
          averageIncidentsPerArea: data.summary?.averageIncidentsPerArea || 0,
          maxIncidentsArea: data.summary?.maxIncidentsArea || null,
          lastUpdated: Date.now()
        };

      case 'reports_summary':
        return {
          totalReports: data.summary?.totalReports || 0,
          totalIncidents: data.summary?.totalIncidents || 0,
          quickReports: data.summary?.quickReports || 0,
          detailReports: data.summary?.detailReports || 0,
          approvalRate: data.summary?.approvalRate || 0,
          completionRate: data.summary?.completionRate || 0,
          lastUpdated: Date.now()
        };

      case 'reports_incidents_highlight':
        return {
          totalSlots: data.summary?.totalSlots || 0,
          totalReports: data.summary?.totalReports || 0,
          totalIncidents: data.summary?.totalIncidents || 0,
          averageIncidentsPerSlot: data.summary?.averageIncidentsPerSlot || 0,
          trend: data.summary?.trend || 'stable',
          trendPercentage: data.summary?.trendPercentage || 0,
          lastUpdated: Date.now()
        };

      case 'reports_incidents_other':
        return {
          totalSlots: data.summary?.totalSlots || 0,
          totalReports: data.summary?.totalReports || 0,
          totalIncidents: data.summary?.totalIncidents || 0,
          averageIncidentsPerSlot: data.summary?.averageIncidentsPerSlot || 0,
          trend: data.summary?.trend || 'stable',
          trendPercentage: data.summary?.trendPercentage || 0,
          lastUpdated: Date.now()
        };

      case 'reports_status':
        return {
          totalReports: data.statusStats?.total || 0,
          approvalRate: data.workflowStats?.approvalRate || 0,
          completionRate: data.workflowStats?.completionRate || 0,
          averageProcessingTime: data.processingTime?.averageProcessingTime || 0,
          workflowEfficiency: data.workflowStats?.workflowEfficiency || 0,
          lastUpdated: Date.now()
        };

      case 'documents_summary':
        return {
          totalDocuments: data.summary?.totalDocuments || 0,
          incomingDocuments: data.summary?.incomingDocuments || 0,
          outgoingDocuments: data.summary?.outgoingDocuments || 0,
          replyDocuments: data.summary?.replyDocuments || 0,
          completionRate: data.summary?.completionRate || 0,
          overdueDocs: data.processingStats?.overdueDocs || 0,
          averageProcessingTime: data.processingStats?.averageProcessingTime || 0,
          lastUpdated: Date.now()
        };

      default:
        return { lastUpdated: Date.now() };
    }
  }

  /**
   * Đăng ký trigger event
   * @param {String} eventType - Loại event
   * @param {Object} eventData - Dữ liệu event
   */
  registerTriggerEvent(eventType, eventData = {}) {
    const event = {
      type: eventType,
      data: eventData,
      timestamp: Date.now()
    };

    this.triggeredEvents.add(JSON.stringify(event));
    console.log(`[${new Date().toISOString()}] Registered trigger event: ${eventType}`);
  }

  /**
   * Xử lý các trigger events đã đăng ký
   * Chạy định kỳ mỗi phút
   */
  async processTriggeredEvents() {
    if (this.triggeredEvents.size === 0) {
      return { success: true, processed: 0 };
    }

    try {
      console.log(`[${new Date().toISOString()}] Processing ${this.triggeredEvents.size} triggered events...`);

      const events = Array.from(this.triggeredEvents).map(eventStr => JSON.parse(eventStr));
      this.triggeredEvents.clear();

      // Nhóm events theo loại để xử lý hiệu quả
      const eventGroups = _.groupBy(events, 'type');

      const affectedTypes = new Set();

      // Xác định loại thống kê nào cần cập nhật
      Object.keys(eventGroups).forEach(eventType => {
        switch (eventType) {
          case 'duty_shift_updated':
          case 'duty_schedule_updated':
            affectedTypes.add('on_duty_officers');
            break;

          case 'attendance_updated':
          case 'work_schedule_updated':
            affectedTypes.add('attendance');
            affectedTypes.add('officer_summary');
            affectedTypes.add('officers_by_area');
            break;

          case 'user_updated':
          case 'leave_request_updated':
            affectedTypes.add('officer_summary');
            affectedTypes.add('officers_by_area');
            break;

          case 'area_updated':
            affectedTypes.add('officers_by_area');
            affectedTypes.add('reports_by_area');
            break;

          case 'report_updated':
            affectedTypes.add('reports_by_area');
            affectedTypes.add('reports_summary');
            affectedTypes.add('reports_incidents_highlight');
            affectedTypes.add('reports_incidents_other');
            affectedTypes.add('reports_status');
            affectedTypes.add('latest_incidents');
            break;

          case 'document_updated':
            affectedTypes.add('documents_summary');
            break;
        }
      });

      // Tính toán lại metadata cho các loại bị ảnh hưởng
      for (const type of affectedTypes) {
        await this.recalculateMetadataForType(type);
      }

      // Gửi real-time notifications
      await this.sendRealTimeNotifications(Array.from(affectedTypes), events);

      console.log(`[${new Date().toISOString()}] Processed triggered events. Affected types: ${Array.from(affectedTypes).join(', ')}`);

      return {
        success: true,
        processed: events.length,
        affectedTypes: Array.from(affectedTypes)
      };

    } catch (error) {
      console.error(`[${new Date().toISOString()}] Error processing triggered events:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Tính toán lại metadata cho một loại thống kê cụ thể
   * @param {String} type - Loại thống kê
   */
  async recalculateMetadataForType(type) {
    try {
      const timeRanges = ['day', 'week', 'month'];

      for (const timeRange of timeRanges) {
        let result;

        switch (type) {
          case 'on_duty_officers':
            result = await statisticsService.getOnDutyOfficers({ timeRange });
            break;
          case 'attendance':
            result = await statisticsService.getAttendanceStats({ timeRange });
            break;
          case 'officer_summary':
            result = await statisticsService.getOfficerSummaryStats({ timeRange });
            break;
          case 'officers_by_area':
            result = await statisticsService.getOfficersByAreaStats({ timeRange });
            break;
          case 'reports_by_area':
            result = await statisticsService.getReportsByAreaStats({ timeRange });
            break;
          case 'reports_summary':
            result = await statisticsService.getReportsSummaryStats({ timeRange });
            break;
          case 'reports_incidents_highlight':
            result = await statisticsService.getIncidentsHighlightStats({ timeRange });
            break;
          case 'reports_incidents_other':
            result = await statisticsService.getIncidentsOtherStats({ timeRange });
            break;
          case 'reports_status':
            result = await statisticsService.getReportsStatusStats({ timeRange });
            break;
          case 'latest_incidents':
            result = await statisticsService.getLatestIncidents({ timeRange });
            break;
          case 'documents_summary':
            result = await statisticsService.getDocumentsSummaryStats({ timeRange });
            break;
        }

        if (result && result.success) {
          await this.cacheStatisticsData(type, result.data, timeRange);
        }
      }

    } catch (error) {
      console.error(`Error recalculating metadata for ${type}:`, error);
    }
  }

  /**
   * Gửi real-time notifications cho clients
   * @param {Array} affectedTypes - Các loại thống kê bị ảnh hưởng
   * @param {Array} events - Danh sách events
   */
  async sendRealTimeNotifications(affectedTypes, events) {
    try {
      // Gửi notification sử dụng socket manager mới
      await SocketNotifyManager.sendStatisticsUpdated(affectedTypes, events);

      console.log(`[${new Date().toISOString()}] Sent real-time notifications for: ${affectedTypes.join(', ')}`);

    } catch (error) {
      console.error('Error sending real-time notifications:', error);
    }
  }

  /**
   * Invalidate cache cho một loại thống kê
   * @param {String} type - Loại thống kê
   */
  async invalidateCache(type) {
    try {
      // Xoá toàn bộ key theo pattern để đơn giản hoá và đảm bảo bao phủ mọi biến thể
      const patterns = [
        `statistics:${type}:*`,
        `statistics:summary:${type}:*`
      ];

      const scanAndDelete = (pattern) => new Promise((resolve, reject) => {
        const keysToDelete = [];
        const iterate = (cursor) => {
          this.redis.scan(cursor, 'MATCH', pattern, 'COUNT', 1000, (err, res) => {
            if (err) return reject(err);
            const nextCursor = res[0];
            const keys = res[1] || [];
            if (keys.length) keysToDelete.push(...keys);
            if (nextCursor === '0') {
              if (keysToDelete.length === 0) return resolve(0);
              this.redis.del(keysToDelete, (delErr, delCount) => {
                if (delErr) return reject(delErr);
                resolve(delCount || 0);
              });
            } else {
              iterate(nextCursor);
            }
          });
        };
        iterate('0');
      });

      const results = await Promise.all(patterns.map(p => scanAndDelete(p)));
      console.log(`[${new Date().toISOString()}] Invalidated cache for ${type}:`, {
        deletedDataKeys: results[0],
        deletedSummaryKeys: results[1]
      });

    } catch (error) {
      console.error(`Error invalidating cache for ${type}:`, error);
    }
  }
}

module.exports = new StatisticsMetadataService();

const mongoose = require('mongoose');
const ReportDetail = require('../models/reportDetail');
const Report = require('../models/report');

/**
 * Service xử lý logic cho ReportDetail
 * Cung cấp các phương thức CRUD và business logic
 */
class ReportDetailService {

  /**
   * Tạo ReportDetails từ details array
   * @param {String} reportId - ID của Report
   * @param {Array} detailsArray - Array các detail objects
   * @returns {Promise<Array>} Array các ReportDetail đã tạo
   */
  async createReportDetails(reportId, detailsArray, jobTypeId) {
    if (!detailsArray || detailsArray.length === 0) {
      return []
    }

    const reportDetailsData = detailsArray.map(detail => ({
      reportId: mongoose.Types.ObjectId(reportId),
      time: detail.time,
      location: {
        address: detail.location?.address,
        coordinates: detail.location?.coordinates,
        lat: detail.location?.lat || detail.location?.coordinates?.[1],
        lng: detail.location?.lng || detail.location?.coordinates?.[0]
      },
      areas: detail.location?.areas || [], // Vẫn lưu cả area level 1 và 2
      jobType: jobTypeId,
      createdAt: Date.now(),
      updatedAt: Date.now()
    }))

    const createdDetails = await ReportDetail.insertMany(reportDetailsData)

    // Cập nhật detailsCount trong Report
    await Report.findByIdAndUpdate(reportId, {
      detailsCount: createdDetails.length,
      updatedAt: Date.now()
    })

    return createdDetails
  }

  /**
   * Lấy details theo reportId
   * @param {String} reportId - ID của Report
   * @param {Object} options - Options cho query
   * @returns {Promise<Array>} Array các ReportDetail
   */
  async getDetailsByReportId(reportId, options = {}) {
    const query = ReportDetail.find({ reportId: mongoose.Types.ObjectId(reportId) })

    if (options.populate) {
      query.populate('areas', 'name level')
    }

    if (options.sort) {
      query.sort(options.sort)
    } else {
      query.sort({ time: 1 })
    }

    if (options.lean !== false) {
      query.lean()
    }

    return await query.exec()
  }

  /**
   * Xóa details theo reportId
   * @param {String} reportId - ID của Report
   * @returns {Promise<Object>} Kết quả xóa
   */
  async deleteDetailsByReportId(reportId) {
    const result = await ReportDetail.deleteMany({
      reportId: mongoose.Types.ObjectId(reportId)
    })

    // Cập nhật detailsCount trong Report
    await Report.findByIdAndUpdate(reportId, {
      detailsCount: 0,
      updatedAt: Date.now()
    })

    return result
  }

  /**
   * Lọc chỉ lấy areas level 1 từ danh sách areas
   * @param {Array} areaIds - Array các area IDs
   * @returns {Promise<Array>} Array chỉ chứa area level 1 IDs
   */
  async filterLevel1Areas(areaIds) {
    if (!areaIds || areaIds.length === 0) {
      return []
    }

    const Area = require('../models/area')

    // Lấy thông tin areas và filter chỉ level 1
    const areas = await Area.find({
      _id: { $in: areaIds },
      level: 1 // Chỉ lấy area level 1
    }).select('_id').lean()

    return areas.map(area => area._id)
  }

  /**
   * Lọc chỉ lấy area level 1 từ danh sách areas
   * @param {Array} areaIds - Array các area IDs (có thể là level 1 hoặc 2)
   * @returns {Promise<Array>} Array chỉ chứa area level 1 IDs
   */
  async getLevel1AreasFromMixed(areaIds) {
    if (!areaIds || areaIds.length === 0) {
      return []
    }

    const Area = require('../models/area')

    // Chỉ lấy areas có level = 1
    const level1Areas = await Area.find({
      _id: { $in: areaIds },
      level: 1
    }).select('_id').lean()

    return level1Areas.map(area => area._id)
  }

  /**
   * Tính summary từ ReportDetails (chỉ theo area level 1)
   * @param {String} reportId - ID của Report
   * @returns {Promise<Array>} Array summary objects
   */
  async calculateSummaryFromDetails(reportId) {
    const pipeline = [
      { $match: { reportId: mongoose.Types.ObjectId(reportId) } },
      { $unwind: '$areas' },
      // Lookup để lấy thông tin area
      {
        $lookup: {
          from: 'areas',
          localField: 'areas',
          foreignField: '_id',
          as: 'areaInfo'
        }
      },
      { $unwind: '$areaInfo' },
      // Chỉ lấy area level 1, bỏ qua area level 2
      {
        $match: {
          'areaInfo.level': 1
        }
      },
      // Group theo area level 1
      {
        $group: {
          _id: '$areaInfo._id',
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          area: '$_id',
          count: 1,
          _id: 0
        }
      }
    ]

    return await ReportDetail.aggregate(pipeline)
  }

  /**
   * Cập nhật summary cho Report dựa trên ReportDetails
   * @param {String} reportId - ID của Report
   * @returns {Promise<Array>} Summary đã cập nhật
   */
  async updateReportSummary(reportId) {
    const summary = await this.calculateSummaryFromDetails(reportId)

    await Report.findByIdAndUpdate(reportId, {
      summary,
      updatedAt: Date.now()
    })

    return summary
  }

  /**
   * Lấy thống kê ReportDetail theo khu vực
   * @param {Object} filters - Filters cho query
   * @returns {Promise<Array>} Thống kê theo area
   */
  async getStatsByArea(filters = {}) {
    const matchStage = {}

    if (filters.timeRange) {
      matchStage.time = {
        $gte: filters.timeRange.start,
        $lte: filters.timeRange.end
      }
    }

    if (filters.areas && filters.areas.length > 0) {
      matchStage.areas = { $in: filters.areas.map(id => mongoose.Types.ObjectId(id)) }
    }

    const pipeline = [
      { $match: matchStage },
      { $unwind: '$areas' },
      {
        $group: {
          _id: '$areas',
          count: { $sum: 1 },
          reports: { $addToSet: '$reportId' }
        }
      },
      {
        $project: {
          area: '$_id',
          detailCount: '$count',
          reportCount: { $size: '$reports' },
          _id: 0
        }
      },
      { $sort: { detailCount: -1 } }
    ]

    return await ReportDetail.aggregate(pipeline)
  }

  /**
   * Cleanup orphaned ReportDetails (không có Report tương ứng)
   * @returns {Promise<Object>} Kết quả cleanup
   */
  async cleanupOrphanedDetails() {
    const orphanedDetails = await ReportDetail.aggregate([
      {
        $lookup: {
          from: 'reports',
          localField: 'reportId',
          foreignField: '_id',
          as: 'report'
        }
      },
      { $match: { report: { $size: 0 } } },
      { $project: { _id: 1 } }
    ])

    if (orphanedDetails.length > 0) {
      const orphanedIds = orphanedDetails.map(d => d._id)
      const result = await ReportDetail.deleteMany({
        _id: { $in: orphanedIds }
      })

      return {
        deletedCount: result.deletedCount,
        orphanedIds
      }
    }

    return { deletedCount: 0, orphanedIds: [] }
  }

  /**
   * Rebuild summary cho tất cả Reports từ ReportDetails
   * @param {String} reportId - ID của Report cụ thể (optional)
   * @returns {Promise<Number>} Số lượng Reports đã update
   */
  async rebuildAllSummaries(reportId = null) {
    const matchStage = reportId ?
      { _id: mongoose.Types.ObjectId(reportId) } :
      { deletedAt: { $exists: false } }

    const reports = await Report.find(matchStage, '_id').lean()
    let updatedCount = 0

    for (const report of reports) {
      try {
        await this.updateReportSummary(report._id.toString())
        updatedCount++
      } catch (error) {
        console.error(`Error updating summary for report ${report._id}:`, error)
      }
    }

    return updatedCount
  }
}

module.exports = new ReportDetailService()
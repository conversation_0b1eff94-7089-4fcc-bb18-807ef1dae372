/**
 * Service quản lý lịch nghỉ phép / công tác cho chỉ huy
 * CRUD đầy đủ, filter/sort/pagination theo yêu cầu
 */

const _ = require('lodash');
const mongoose = require('mongoose');
const LeaveRequest = require('../models/leaveRequest');
const scheduleSyncService = require('./scheduleSyncService');
const { change_alias } = require('../util/tool');
const MESSAGES = require('../message');

class LeaveService {
  /**
   * Tạo lịch nghỉ/công tác (chỉ huy tạo mặc định approved)
   * @param {Object} requestData - Dữ liệu lịch
   * @returns {Object}
   */
  async createLeaveRequest(requestData) {
    try {
      const {
        user,
        type,
        startDate,
        endDate,
        reason,
        attachments = []
      } = requestData;

      // Validate cơ bản
      const validType = ['leave', 'business_trip'].includes(type);
      if (!validType) {
        return { success: false, message: { head: 'Dữ liệu không hợp lệ', body: '<PERSON><PERSON><PERSON> lịch không hợp lệ' }, data: null };
      }
      if (!user || !mongoose.Types.ObjectId.isValid(user)) {
        return { success: false, message: { head: 'Dữ liệu không hợp lệ', body: 'Thiếu hoặc sai user' }, data: null };
      }
      if (!startDate || !endDate) {
        return { success: false, message: { head: 'Dữ liệu không hợp lệ', body: 'Thiếu ngày bắt đầu/kết thúc' }, data: null };
      }
      // Kiểm tra endDate >= startDate (DD-MM-YYYY)
      const start = startDate.split('-').reverse().join('-');
      const end = endDate.split('-').reverse().join('-');
      if (new Date(end).getTime() < new Date(start).getTime()) {
        return { success: false, message: { head: 'Dữ liệu không hợp lệ', body: 'Ngày kết thúc không được nhỏ hơn ngày bắt đầu' }, data: null };
      }

      // Tính toán timestamp kiểm tra trùng lặp
      const [sDay, sMonth, sYear] = startDate.split('-').map(Number);
      const [eDay, eMonth, eYear] = endDate.split('-').map(Number);
      const newStartTime = new Date(sYear, sMonth - 1, sDay, 0, 0, 0, 0).getTime();
      const newEndTime = new Date(eYear, eMonth - 1, eDay + 1, 0, 0, 0, 0).getTime(); // bao gồm cả ngày cuối

      // Kiểm tra trùng lặp với các lịch đã phê duyệt (không phân biệt type)
      const conflicts = await LeaveRequest.find({
        user,
        status: 'approved',
        startTime: { $lt: newEndTime },
        endTime: { $gt: newStartTime }
      }).select('_id type startDate endDate status');

      if (conflicts && conflicts.length > 0) {
        return {
          success: false,
          message: MESSAGES.LEAVE_REQUEST && MESSAGES.LEAVE_REQUEST.DUPLICATED
            ? MESSAGES.LEAVE_REQUEST.DUPLICATED
            : { head: 'Xung đột lịch', body: 'Khoảng thời gian yêu cầu bị trùng với các lịch đã phê duyệt' },
          data: {
            requested: { user, type, startDate, endDate },
            conflicts: conflicts.map(c => ({ _id: c._id, type: c.type, startDate: c.startDate, endDate: c.endDate, status: c.status }))
          }
        };
      }

      // Tạo lịch nghỉ phép/công tác
      const leaveRequestData = {
        user,
        type,
        startDate,
        endDate,
        reason,
        attachments,
        status: 'approved'
      };

      const leaveRequest = await LeaveRequest.create(leaveRequestData);

      // Đồng bộ với Work Schedule
      try {
        const syncResult = await scheduleSyncService.updateWorkScheduleFromLeave(leaveRequest, 'create');
        console.log('Sync work schedule result:', syncResult);
      } catch (syncError) {
        console.error('Error syncing work schedule:', syncError);
        // Không fail toàn bộ process nếu sync lỗi
      }

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Tạo lịch thành công'
        },
        data: leaveRequest
      };

    } catch (error) {
      console.error('Error creating leave request:', error);
      return {
        success: false
      };
    }
  }

  /**
   * Cập nhật lịch
   * @param {String} id
   * @param {Object} payload
   */
  async updateLeaveRequest(id, payload) {
    try {
      // Chỉ lấy các trường có giá trị (không null/undefined)
      const updatable = {};
      if (payload.user !== undefined) updatable.user = payload.user;
      if (payload.type !== undefined) updatable.type = payload.type;
      if (payload.startDate !== undefined) updatable.startDate = payload.startDate;
      if (payload.endDate !== undefined) updatable.endDate = payload.endDate;
      if (payload.reason !== undefined) updatable.reason = payload.reason;
      if (payload.attachments !== undefined) updatable.attachments = payload.attachments;

      // Validate các trường được gửi lên
      if (updatable.type && !['leave', 'business_trip'].includes(updatable.type)) {
        return { success: false, message: { head: 'Dữ liệu không hợp lệ', body: 'Loại lịch không hợp lệ' }, data: null };
      }
      if (updatable.user && !mongoose.Types.ObjectId.isValid(updatable.user)) {
        return { success: false, message: { head: 'Dữ liệu không hợp lệ', body: 'Người dùng không hợp lệ' }, data: null };
      }
      if (updatable.startDate && updatable.endDate) {
        const s = updatable.startDate.split('-').reverse().join('-');
        const e = updatable.endDate.split('-').reverse().join('-');
        if (new Date(e).getTime() < new Date(s).getTime()) {
          return { success: false, message: { head: 'Dữ liệu không hợp lệ', body: 'Ngày kết thúc không được nhỏ hơn ngày bắt đầu' }, data: null };
        }
      }

      // Chỉ update nếu có trường nào được gửi lên
      if (Object.keys(updatable).length === 0) {
        return { success: false, message: { head: 'Dữ liệu không hợp lệ', body: 'Không có trường nào để cập nhật' }, data: null };
      }

      updatable.updatedAt = Date.now();

      // Lấy dữ liệu cũ trước khi update để so sánh
      const oldLeaveRequest = await LeaveRequest.findOne({_id: id, status: { $ne: 'deleted' }}).lean();
      if (!oldLeaveRequest) {
        return { success: false, message: { head: 'Không tìm thấy', body: 'Không tìm thấy lịch' }, data: null };
      }

      // Xác định dữ liệu mục tiêu để kiểm tra trùng lặp
      const targetUser = updatable.user || oldLeaveRequest.user;
      const targetType = updatable.type || oldLeaveRequest.type;
      const targetStartDate = updatable.startDate || oldLeaveRequest.startDate;
      const targetEndDate = updatable.endDate || oldLeaveRequest.endDate;

      // Tính timestamp theo target
      const [tsDay, tsMonth, tsYear] = targetStartDate.split('-').map(Number);
      const [teDay, teMonth, teYear] = targetEndDate.split('-').map(Number);
      const targetStartTime = new Date(tsYear, tsMonth - 1, tsDay, 0, 0, 0, 0).getTime();
      const targetEndTime = new Date(teYear, teMonth - 1, teDay + 1, 0, 0, 0, 0).getTime();

      // Kiểm tra trùng lặp với lịch đã phê duyệt khác (loại trừ bản ghi hiện tại)
      const conflicts = await LeaveRequest.find({
        _id: { $ne: id },
        user: targetUser,
        status: 'approved',
        startTime: { $lt: targetEndTime },
        endTime: { $gt: targetStartTime }
      }).select('_id type startDate endDate status');

      if (conflicts && conflicts.length > 0) {
        return {
          success: false,
          message: MESSAGES.LEAVE_REQUEST && MESSAGES.LEAVE_REQUEST.DUPLICATED
            ? MESSAGES.LEAVE_REQUEST.DUPLICATED
            : { head: 'Xung đột lịch', body: 'Khoảng thời gian yêu cầu bị trùng với các lịch đã phê duyệt' },
          data: {
            requested: { user: targetUser, type: targetType, startDate: targetStartDate, endDate: targetEndDate },
            conflicts: conflicts.map(c => ({ _id: c._id, type: c.type, startDate: c.startDate, endDate: c.endDate, status: c.status }))
          }
        };
      }

      const updated = await LeaveRequest.findOneAndUpdate({_id: id, status: { $ne: 'deleted' }}, { $set: updatable }, { new: true });
      if (!updated) {
        return { success: false, message: { head: 'Không tìm thấy', body: 'Không tìm thấy lịch' }, data: null };
      }

      // Đồng bộ với Work Schedule nếu có thay đổi về ngày tháng hoặc loại lịch
      try {
        const dateChanged = (updatable.startDate && updatable.startDate !== oldLeaveRequest.startDate) ||
                           (updatable.endDate && updatable.endDate !== oldLeaveRequest.endDate);
        const typeChanged = updatable.type && updatable.type !== oldLeaveRequest.type;

        if (dateChanged || typeChanged) {
          // Nếu ngày thay đổi, cần reset work schedule cũ và cập nhật work schedule mới
          if (dateChanged) {
            // Reset work schedule cho khoảng thời gian cũ
            await scheduleSyncService.updateWorkScheduleFromLeave(oldLeaveRequest, 'delete');
          }

          // Cập nhật work schedule cho khoảng thời gian mới
          const syncResult = await scheduleSyncService.updateWorkScheduleFromLeave(updated, 'update');
          console.log('Sync work schedule result:', syncResult);
        }
      } catch (syncError) {
        console.error('Error syncing work schedule:', syncError);
        // Không fail toàn bộ process nếu sync lỗi
      }

      return { success: true, message: { head: 'Thành công', body: 'Cập nhật lịch thành công' }, data: updated };
    } catch (error) {
      return { success: false };
    }
  }

  /**
   * Xóa lịch (soft delete - thay đổi trạng thái)
   */
  async deleteLeaveRequest(id) {
    try {
      // Lấy dữ liệu trước khi xóa để đồng bộ work schedule
      const leaveRequest = await LeaveRequest.findOne({_id: id, status: { $ne: 'deleted' }}).lean();
      if (!leaveRequest) {
        return { success: false, message: { head: 'Không tìm thấy', body: 'Không tìm thấy lịch' }, data: null };
      }

      const updated = await LeaveRequest.findOneAndUpdate(
        {_id: id, status: { $ne: 'deleted' }},
        {
          $set: {
            status: 'deleted',
            updatedAt: Date.now()
          }
        },
        { new: true }
      );
      if (!updated) {
        return { success: false, message: { head: 'Không tìm thấy', body: 'Không tìm thấy lịch' }, data: null };
      }

      // Đồng bộ với Work Schedule - reset về trạng thái scheduled
      try {
        const syncResult = await scheduleSyncService.updateWorkScheduleFromLeave(leaveRequest, 'delete');
        console.log('Sync work schedule result:', syncResult);
      } catch (syncError) {
        console.error('Error syncing work schedule:', syncError);
        // Không fail toàn bộ process nếu sync lỗi
      }

      return { success: true, message: { head: 'Thành công', body: 'Xóa lịch thành công' }, data: updated };
    } catch (error) {
      return { success: false };
    }
  }

  /**
   * Danh sách lịch với filter/sort/pagination
   * @param {Object} filters
   * @param {Object} pagination
   */
  async listLeaveRequests(filters = {}, pagination = {}) {
    try {
      const { type, unit, user, startDate, endDate, textSearch } = filters;
      const { page = 1, limit = 20, sortBy = 'name', sortOrder = 'asc' } = pagination;

      const match = { status: { $ne: 'deleted' } }; // Loại bỏ các bản ghi đã bị xóa
      if (type) match.type = type;
      if (unit) match.unit = unit;
      if (user) match.user = mongoose.Types.ObjectId.isValid(user) ? new mongoose.Types.ObjectId(user) : null;

      // TextSearch chỉ hoạt động khi không có user filter
      let textSearchUserIds = null;
      if (!user && textSearch && textSearch.trim()) {
        const UserModel = require('../models/user');
        const nameAliasSearch = change_alias(textSearch.trim());

        const searchUsers = await UserModel.find({
          $or: [
            { phones: textSearch.trim() },
            { idNumber: textSearch.trim() },
            { nameAlias: new RegExp(nameAliasSearch, 'i') }
          ]
        }).select('_id').lean();

        if (searchUsers && searchUsers.length > 0) {
          textSearchUserIds = searchUsers.map(u => u._id);
        } else {
          // Nếu không tìm thấy user nào, trả về kết quả rỗng
          return {
            success: true,
            message: { head: 'Thành công', body: 'Lấy danh sách lịch thành công' },
            data: {
              requests: [],
              pagination: { page, limit, total: 0, pages: 0 },
              summary: { total: 0, typeCounts: { leave: 0, business_trip: 0 } }
            }
          };
        }
      }

      if (startDate && endDate) {
        // Convert DD-MM-YYYY to timestamp để so sánh chính xác
        const [startDay, startMonth, startYear] = startDate.split('-').map(Number);
        const [endDay, endMonth, endYear] = endDate.split('-').map(Number);

        const startTimestamp = new Date(startYear, startMonth - 1, startDay, 0, 0, 0, 0).getTime();
        const endTimestamp = new Date(endYear, endMonth - 1, endDay + 1, 0, 0, 0, 0).getTime(); // +1 ngày để bao gồm cả ngày cuối

        // Check: lịch bắt đầu trước ngày kết thúc filter VÀ lịch kết thúc sau ngày bắt đầu filter
        match.$and = [
          { startTime: { $lt: endTimestamp } }, // Lịch bắt đầu trước cuối ngày filter
          { endTime: { $gt: startTimestamp } }  // Lịch kết thúc sau đầu ngày filter
        ];
      }

      const sort = {};
      const order = sortOrder === 'desc' ? -1 : 1;

      // Pipeline aggregation để filter theo unit và sort theo tên/đơn vị
      const pipeline = [
        { $match: match },
        {
          $lookup: {
            from: 'users',
            let: { userId: '$user' },
            pipeline: [
              { $match: { $expr: { $eq: ['$_id', '$$userId'] } } },
              { $project: { name: 1, idNumber: 1, avatar: 1, phones: 1, email: 1, units: 1, positions: 1 } }
            ],
            as: 'user'
          }
        },
        { $unwind: '$user' },
      ];

      if (unit && mongoose.Types.ObjectId.isValid(unit)) {
        pipeline.push({ $match: { 'user.units': new mongoose.Types.ObjectId(unit) } });
      }

      // Filter theo textSearch nếu có
      if (textSearchUserIds && textSearchUserIds.length > 0) {
        pipeline.push({ $match: { 'user._id': { $in: textSearchUserIds } } });
      }

      // Populate units và positions với name
      pipeline.push(
        { $lookup: { from: 'units', localField: 'user.units', foreignField: '_id', as: 'userUnits' } },
        { $lookup: { from: 'positions', localField: 'user.positions', foreignField: '_id', as: 'userPositions' } }
      );

      // Join Unit để sort theo tên đơn vị nếu cần
      if (sortBy === 'unit') {
        pipeline.push({ $addFields: { unitName: { $ifNull: [{ $arrayElemAt: ['$userUnits.name', 0] }, ''] } } });
        sort.unitName = order;
      } else {
        // Sort theo từ cuối cùng của tên
        pipeline.push({
          $addFields: {
            lastName: {
              $arrayElemAt: [{ $split: ['$user.name', ' '] }, -1]
            }
          }
        });
        sort.lastName = order;
      }

      const countPipeline = [...pipeline, { $count: 'total' }];
      const skip = (page - 1) * limit;
      const dataPipeline = [
        ...pipeline,
        { $sort: sort },
        { $skip: skip },
        { $limit: limit },
        {
          $project: {
            user: {
              _id: '$user._id',
              name: '$user.name',
              idNumber: '$user.idNumber',
              avatar: '$user.avatar',
              phones: '$user.phones',
              email: '$user.email',
              units: { $map: { input: '$userUnits', as: 'unit', in: { _id: '$$unit._id', name: '$$unit.name', parentPath: '$$unit.parentPath' } } },
              positions: { $map: { input: '$userPositions', as: 'pos', in: { _id: '$$pos._id', name: '$$pos.name' } } }
            },
            type: 1,
            startDate: 1,
            endDate: 1,
            reason: 1,
            attachments: 1,
            status: 1,
            createdAt: 1,
            updatedAt: 1
          }
        }
      ];

      // Tạo match cho summary (áp dụng tất cả filter TRỪ filter type)
      const summaryMatch = { status: { $ne: 'deleted' } };
      if (unit) summaryMatch.unit = unit;
      if (user) summaryMatch.user = mongoose.Types.ObjectId.isValid(user) ? new mongoose.Types.ObjectId(user) : null;
      if (startDate && endDate) {
        // Convert DD-MM-YYYY to timestamp để so sánh chính xác
        const [startDay, startMonth, startYear] = startDate.split('-').map(Number);
        const [endDay, endMonth, endYear] = endDate.split('-').map(Number);

        const startTimestamp = new Date(startYear, startMonth - 1, startDay, 0, 0, 0, 0).getTime();
        const endTimestamp = new Date(endYear, endMonth - 1, endDay + 1, 0, 0, 0, 0).getTime(); // +1 ngày để bao gồm cả ngày cuối

        // Check: lịch bắt đầu trước ngày kết thúc filter VÀ lịch kết thúc sau ngày bắt đầu filter
        summaryMatch.$and = [
          { startTime: { $lt: endTimestamp } }, // Lịch bắt đầu trước cuối ngày filter
          { endTime: { $gt: startTimestamp } }  // Lịch kết thúc sau đầu ngày filter
        ];
      }

      // Pipeline để đếm summary (không áp dụng filter type)
      let summaryCountPipeline = [
        { $match: summaryMatch }
      ];

      // Nếu có textSearch, cần thêm lookup user và filter
      if (textSearchUserIds && textSearchUserIds.length > 0) {
        summaryCountPipeline.push(
          {
            $lookup: {
              from: 'users',
              let: { userId: '$user' },
              pipeline: [
                { $match: { $expr: { $eq: ['$_id', '$$userId'] } } },
                { $project: { _id: 1 } }
              ],
              as: 'user'
            }
          },
          { $unwind: '$user' },
          { $match: { 'user._id': { $in: textSearchUserIds } } }
        );
      }

      summaryCountPipeline.push({ $group: { _id: '$type', count: { $sum: 1 } } });

      const [countRes, items, summaryCountRes] = await Promise.all([
        LeaveRequest.aggregate(countPipeline),
        LeaveRequest.aggregate(dataPipeline),
        LeaveRequest.aggregate(summaryCountPipeline)
      ]);

      const total = countRes.length ? countRes[0].total : 0;

      // Format summary counts
      const summaryTypeCounts = {};
      summaryCountRes.forEach(item => {
        summaryTypeCounts[item._id] = item.count;
      });

      // Tính tổng summary
      const summaryTotal = Object.values(summaryTypeCounts).reduce((sum, count) => sum + count, 0);

      return {
        success: true,
        message: { head: 'Thành công', body: 'Lấy danh sách lịch thành công' },
        data: {
          requests: items,
          pagination: {
            page,
            limit,
            total,
            pages: Math.ceil(total / limit)
          },
          summary: {
            total: summaryTotal,
            typeCounts: {
              leave: summaryTypeCounts.leave || 0,
              business_trip: summaryTypeCounts.business_trip || 0
            }
          }
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Lấy chi tiết lịch
   */
  async getLeaveRequestDetail(requestId) {
    try {
      const leaveRequest = await LeaveRequest.findOne({_id: requestId, status: { $ne: 'deleted' }})
        .populate({
          path: 'user',
          select: 'name idNumber avatar units positions',
          populate: [
            {
              path: 'units',
              select: 'name parentPath'
            },
            {
              path: 'positions',
              select: 'name'
            }
          ]
        })
        .lean();

      if (!leaveRequest) {
        return {
          success: false,
          message: {
            head: 'Không tìm thấy',
            body: 'Không tìm thấy lịch'
          },
          data: null
        };
      }

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy chi tiết lịch thành công'
        },
        data: leaveRequest
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }
}

module.exports = new LeaveService();
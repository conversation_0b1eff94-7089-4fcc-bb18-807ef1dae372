/**
 * Service xử lý logic chấm công đột xuất
 * Quản lý phiên chấm công đột xuất và xử lý chấm công
 */

const _ = require('lodash');
const SuddenAttendanceSession = require('../models/suddenAttendanceSession');
const SuddenAttendanceRecord = require('../models/suddenAttendanceRecord');
const User = require('../models/user');
const Unit = require('../models/unit');
const SavedNotificationModel = require('../models/savedNotification');
const WorkSchedule = require('../models/workSchedule');
const DateUtils = require('../utils/dateUtils');
const CONSTANTS = require('../const');
const moment = require("moment");

class SuddenAttendanceService {
  /**
   * Tạo phiên chấm công đột xuất
   * @param {Object} sessionData - Dữ liệu phiên chấm công
   * @param {String} creatorId - ID người tạo
   * @returns {Object} Kết quả tạo phiên
   */
  async createSession(sessionData, creatorId, notification) {
    try {
      const {
        title,
        description,
        startTime,
        validDurationMinutes = 10,
        targetUnits = [],
        headquarters = 'main'
      } = sessionData;

      // Validate thời gian bắt đầu
      const now = Date.now();
      if (startTime <= now) {
        return {
          success: false,
          message: {
            head: 'Lỗi thời gian',
            body: 'Thời gian bắt đầu phải lớn hơn thời gian hiện tại'
          }
        };
      }

      // Xác định danh sách cán bộ áp dụng
      let allUsers = [];
      if (targetUnits.length > 0) {
        // Lấy cán bộ theo đơn vị
        const users = await User.find({
          units: { $in: targetUnits },
          status: 1
        }).select('_id').lean();
        allUsers = users.map(u => u._id);
      } else {
        // Lấy tất cả cán bộ active
        const users = await User.find({ status: 1 }).select('_id').lean();
        allUsers = users.map(u => u._id);
      }

      // Lấy danh sách cán bộ được miễn điểm danh (công tác/nghỉ phép)
      const exemptedUsersData = await this.getExemptedUsers(startTime, allUsers);
      const exemptedUserIds = exemptedUsersData.map(item => item.user.toString());

      // Loại trừ cán bộ được miễn điểm danh
      const targetUsers = allUsers.filter(userId =>
        !exemptedUserIds.includes(userId.toString())
      );

      const savedNotification = await SavedNotificationModel.create({
        title: notification.title,
        description: notification.message,
        users: targetUsers,
        type: 'user',
        data: notification.notificationData,
        createdAt: new Date(),
        updatedAt: new Date()
      });

      // Tạo phiên chấm công
      const session = await SuddenAttendanceSession.create({
        title,
        description,
        startTime,
        validDurationMinutes,
        targetUnits,
        targetUsers,
        exemptedUsers: exemptedUsersData,
        createdBy: creatorId,
        headquarters,
        statistics: {
          totalTargetUsers: allUsers.length,
          totalCheckedIn: 0,
          totalUnchecked: targetUsers.length,
          onTimeCount: 0,
          lateCount: 0,
          absentCount: 0,
          exemptedCount: exemptedUsersData.length
        },
        savedNotification: savedNotification._id
      });

      // Populate thông tin cần thiết
      await session.populate([
        { path: 'createdBy', select: 'name idNumber' },
        { path: 'targetUnits', select: 'name' }
      ]);

      return {
        success: true,
        message: {
          head: 'Tạo thành công',
          body: `Đã tạo phiên chấm công đột xuất "${title}" cho ${targetUsers.length} cán bộ tại ${headquarters === 'main' ? 'trụ sở chính' : 'trụ sở phụ'}`
        },
        data: session
      };

    } catch (error) {
      console.error('Error creating sudden attendance session:', error);
      return {
        success: false
      };
    }
  }

  /**
   * Cập nhật phiên chấm công đột xuất
   * @param {String} sessionId - ID phiên chấm công
   * @param {Object} updateData - Dữ liệu cập nhật
   * @param {String} updaterId - ID người cập nhật
   * @returns {Object} Kết quả cập nhật
   */
  async updateSession(sessionId, updateData, updaterId) {
    try {
      const session = await SuddenAttendanceSession.findById(sessionId);
      if (!session) {
        return {
          success: false,
          message: {
            head: 'Không tìm thấy',
            body: 'Không tìm thấy phiên chấm công đột xuất'
          }
        };
      }

      // Kiểm tra quyền chỉnh sửa
      if (!session.canEdit) {
        return {
          success: false,
          message: {
            head: 'Không thể chỉnh sửa',
            body: 'Bạn không thể chỉnh sửa phiên chấm công đã bắt đầu'
          }
        };
      }

      // Cập nhật dữ liệu
      const allowedFields = ['title', 'description', 'startTime', 'validDurationMinutes', 'targetUnits', 'headquarters'];
      const updateFields = _.pick(updateData, allowedFields);

      // Nếu có thay đổi targetUnits, cần cập nhật lại targetUsers
      if (updateFields.targetUnits) {
        let allUsers = [];
        if (updateFields.targetUnits.length > 0) {
          const users = await User.find({
            units: { $in: updateFields.targetUnits },
            status: 1
          }).select('_id').lean();
          allUsers = users.map(u => u._id);
        } else {
          const users = await User.find({ status: 1 }).select('_id').lean();
          allUsers = users.map(u => u._id);
        }

        // Lấy danh sách cán bộ được miễn điểm danh
        const exemptedUsersData = await this.getExemptedUsers(session.startTime, allUsers);
        const exemptedUserIds = exemptedUsersData.map(item => item.user.toString());

        // Loại trừ cán bộ được miễn điểm danh
        const targetUsers = allUsers.filter(userId =>
          !exemptedUserIds.includes(userId.toString())
        );

        updateFields.targetUsers = targetUsers;
        updateFields.exemptedUsers = exemptedUsersData;
        updateFields['statistics.totalTargetUsers'] = allUsers.length;

        // Tính lại totalUnchecked dựa trên số người đã chấm công
        const currentCheckedIn = session.statistics.totalCheckedIn || 0;
        updateFields['statistics.totalUnchecked'] = targetUsers.length - currentCheckedIn;
      }

      const updatedSession = await SuddenAttendanceSession.findByIdAndUpdate(
        sessionId,
        updateFields,
        { new: true }
      ).populate([
        { path: 'createdBy', select: 'name idNumber' },
        { path: 'targetUnits', select: 'name' }
      ]);

      return {
        success: true,
        message: {
          head: 'Cập nhật thành công',
          body: 'Đã cập nhật phiên chấm công đột xuất'
        },
        data: updatedSession
      };

    } catch (error) {
      console.error('Error updating sudden attendance session:', error);
      return {
        success: false
      };
    }
  }

  /**
   * Xóa phiên chấm công đột xuất
   * @param {String} sessionId - ID phiên chấm công
   * @param {String} deleterId - ID người xóa
   * @returns {Object} Kết quả xóa
   */
  async deleteSession(sessionId, deleterId) {
    try {
      const session = await SuddenAttendanceSession.findById(sessionId);
      if (!session) {
        return {
          success: false,
          message: {
            head: 'Không tìm thấy',
            body: 'Không tìm thấy phiên chấm công đột xuất'
          }
        };
      }

      // Kiểm tra quyền xóa (chỉ có thể xóa khi chưa bắt đầu)
      if (!session.canEdit) {
        return {
          success: false,
          message: {
            head: 'Không thể xóa',
            body: 'Bạn không thể xóa phiên chấm công đã bắt đầu'
          }
        };
      }

      // Xóa phiên chấm công
      await SuddenAttendanceSession.findByIdAndUpdate(sessionId, {
        status: 'cancelled'
      });

      return {
        success: true,
        message: {
          head: 'Xóa thành công',
          body: 'Đã hủy phiên chấm công đột xuất'
        }
      };

    } catch (error) {
      console.error('Error deleting sudden attendance session:', error);
      return {
        success: false
      };
    }
  }

  /**
   * Lấy danh sách phiên chấm công đột xuất
   * @param {Object} filters - Bộ lọc
   * @param {Object} pagination - Phân trang
   * @returns {Object} Danh sách phiên chấm công
   */
  async getSessionsList(filters = {}, pagination = {}) {
    try {
      const {
        status,
        createdBy,
        startDate,
        endDate,
        search
      } = filters;

      const {
        page = 1,
        limit = 20,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = pagination;

      // Xây dựng query
      const query = {
        status: { $ne: 'cancelled' }
      };

      // if (status) {
      //   query.status = status;
      // }

      if (createdBy) {
        query.createdBy = createdBy;
      }
      if (startDate || endDate) {
        query.startTime = {};
        if (startDate) {
          query.startTime.$gte = moment(startDate, "DD-MM-YYYY").valueOf()
        }
        if (endDate) {
          query.startTime.$lte = moment(endDate, "DD-MM-YYYY").add(1, 'day').valueOf();
        }
      }

      if (search) {
        query.$or = [
          { title: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } }
        ];
      }

      // Thực hiện query
      const skip = (page - 1) * limit;
      const sort = {};
      sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

      const [sessions, total] = await Promise.all([
        SuddenAttendanceSession.find(query)
          .select('-targetUsers -validDurationMinutes -createdBy')
          .populate('targetUnits', 'name')
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean(),
        SuddenAttendanceSession.countDocuments(query)
      ]);

      return {
        success: true,
        data: {
          sessions,
          pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit)
          }
        }
      };

    } catch (error) {
      console.error('Error getting sessions list:', error);
      return {
        success: false
      };
    }
  }

  /**
   * Chấm công đột xuất cho cán bộ
   * @param {String} sessionId - ID phiên chấm công
   * @param {String} userId - ID cán bộ
   * @param {Object} location - Vị trí chấm công (optional)
   * @returns {Object} Kết quả chấm công
   */
  async checkin(sessionId, userId, location = null) {
    try {
      // Tìm phiên chấm công
      const session = await SuddenAttendanceSession.findById(sessionId)
        .select('-createdBy')
        .lean();

      if (!session) {
        return {
          success: false,
          message: {
            head: 'Không tìm thấy',
            body: 'Không tìm thấy phiên chấm công đột xuất'
          }
        };
      }

      // Kiểm tra cán bộ có thuộc targetUnits không
      const isUserEligible = await this.checkUserEligibility(userId, session.targetUnits);
      if (!isUserEligible) {
        return {
          success: false,
          message: {
            head: 'Không có quyền',
            body: 'Bạn không thuộc danh sách cán bộ được chấm công trong phiên này'
          }
        };
      }

      // Kiểm tra đã chấm công chưa
      const existingRecord = await SuddenAttendanceRecord.findOne({
        session: sessionId,
        user: userId
      });

      if (existingRecord) {
        return {
          success: false,
          message: {
            head: 'Đã chấm công',
            body: 'Bạn đã chấm công cho phiên này rồi'
          }
        };
      }

      const currentTime = new Date();
      const currentTimestamp = currentTime.getTime();

      // Xác định trạng thái chấm công
      let status;
      const validEndTime = session.startTime + (session.validDurationMinutes * 60 * 1000);
      const lateEndTime = session.startTime + (60 * 60 * 1000); // 1 giờ sau giờ bắt đầu

      if (currentTimestamp < session.startTime) {
        return {
          success: false,
          message: {
            head: 'Chưa đến giờ',
            body: 'Phiên chấm công chưa bắt đầu'
          }
        };
      } else if (currentTimestamp <= validEndTime) {
        status = CONSTANTS.SUDDEN_ATTENDANCE.CHECKIN_STATUS.ON_TIME;
      } else if (currentTimestamp <= lateEndTime) {
        status = CONSTANTS.SUDDEN_ATTENDANCE.CHECKIN_STATUS.LATE;
      } else {
        return {
          success: false,
          message: {
            head: 'Hết thời gian',
            body: 'Đã hết thời gian chấm công cho phiên này'
          }
        };
      }

      // Tạo bản ghi chấm công
      const recordData = {
        session: sessionId,
        user: userId,
        checkinTime: currentTimestamp,
        status,
        location
      };

      const attendanceRecord = await SuddenAttendanceRecord.create(recordData);

      // Cập nhật thống kê phiên
      const updateStats = {};
      updateStats['statistics.totalCheckedIn'] = 1;
      updateStats['statistics.totalUnchecked'] = -1; // Giảm số người chưa chấm công
      if (status === CONSTANTS.SUDDEN_ATTENDANCE.CHECKIN_STATUS.ON_TIME) {
        updateStats['statistics.onTimeCount'] = 1;
      } else if (status === CONSTANTS.SUDDEN_ATTENDANCE.CHECKIN_STATUS.LATE) {
        updateStats['statistics.lateCount'] = 1;
      }

      await SuddenAttendanceSession.findByIdAndUpdate(sessionId, {
        $inc: updateStats
      });

      return {
        success: true,
        message: status === CONSTANTS.SUDDEN_ATTENDANCE.CHECKIN_STATUS.ON_TIME ? {
          head: 'Chấm công thành công',
          body: `Bạn đã chấm công đúng giờ cho "${session.title}"`
        } : {
          head: 'Chấm công muộn',
          body: `Bạn đã chấm công muộn cho "${session.title}"`
        },
        data: {
          attendanceRecord,
          session: {
            _id: session._id,
            title: session.title,
            startTime: session.startTime
          },
          status,
          checkinTime: currentTimestamp
        }
      };

    } catch (error) {
      console.error('Error in sudden attendance checkin:', error);
      return {
        success: false
      };
    }
  }

  /**
   * Lấy danh sách phiên chấm công đột xuất cho cán bộ
   * @param {String} userId - ID cán bộ
   * @param {Object} filters - Bộ lọc
   * @returns {Object} Danh sách phiên chấm công
   */
  async getUserSessions(userId, filters = {}) {
    try {
      const {
        status,
        startDate,
        endDate
      } = filters;

      // Lấy thông tin user để biết các đơn vị của user
      const user = await User.findById(userId).select('units').lean();
      if (!user) {
        return {
          success: false,
          message: {
            head: 'Lỗi',
            body: 'Không tìm thấy thông tin cán bộ'
          }
        };
      }

      // Xây dựng query - tìm sessions có targetUnits rỗng hoặc chứa đơn vị của user
      const query = {
        status: { $ne: 'cancelled' },
        $or: [
          { targetUnits: { $size: 0 } }, // Sessions áp dụng cho tất cả
          { targetUnits: { $in: user.units } } // Sessions áp dụng cho đơn vị của user
        ]
      };

      // if (status) {
      //   query.status = status;
      // }

      if (startDate || endDate) {
        query.startTime = {};
        if (startDate) {
          query.startTime.$gte = moment(startDate, "DD-MM-YYYY").valueOf()
        }
        if (endDate) {
          query.startTime.$lte = moment(endDate, "DD-MM-YYYY").valueOf();
        }
      }

      // Lấy danh sách phiên
      const sessions = await SuddenAttendanceSession.find(query)
        .select('-targetUsers -validDurationMinutes -createdBy')
        .sort({ startTime: -1 })
        .lean();

      // Lấy thông tin chấm công của user cho từng phiên
      const sessionIds = sessions.map(s => s._id);
      const userRecords = await SuddenAttendanceRecord.find({
        session: { $in: sessionIds },
        user: userId
      }).lean();

      // Map records theo session
      const recordsMap = {};
      userRecords.forEach(record => {
        recordsMap[record.session.toString()] = record;
      });

      // Kết hợp dữ liệu
      const result = sessions.map(session => {
        const record = recordsMap[session._id.toString()];
        return {
          ...session,
          userRecord: record || null,
          canCheckin: this.canUserCheckin(session, record)
        };
      });

      return {
        success: true,
        data: result
      };

    } catch (error) {
      console.error('Error getting user sessions:', error);
      return {
        success: false
      };
    }
  }

  /**
   * Kiểm tra cán bộ có thể chấm công không
   * @param {Object} session - Phiên chấm công
   * @param {Object} record - Bản ghi chấm công (nếu có)
   * @returns {Boolean} Có thể chấm công không
   */
  canUserCheckin(session, record) {
    if (record) return false; // Đã chấm công rồi

    const now = Date.now();
    const lateEndTime = session.startTime + (60 * 60 * 1000); // 1 giờ sau giờ bắt đầu

    return now >= session.startTime && now <= lateEndTime && session.status === 'active';
  }

  /**
   * Lấy thống kê tổng quan
   */
  async getOverallStatistics(filters) {
    const query = {};

    if (filters.status) {
      query.status = filters.status;
    }

    if (filters.createdBy) {
      query.createdBy = filters.createdBy;
    }

    if (filters.startDate || filters.endDate) {
      query.startTime = {};
      if (filters.startDate) {
        query.startTime.$gte = moment(filters.startDate, "DD-MM-YYYY").valueOf();
      }
      if (filters.endDate) {
        query.startTime.$lte = moment(filters.endDate, "DD-MM-YYYY").valueOf();
      }
    }

    // Lấy danh sách phiên
    const sessions = await SuddenAttendanceSession.find(query)
      .select('statistics status')
      .sort({ startTime: -1 })
      .lean();

    // Tính toán thống kê tổng quan
    const totalSessions = sessions.length;
    const totalTargetUsers = sessions.reduce((sum, s) => sum + s.statistics.totalTargetUsers, 0);
    const totalCheckedIn = sessions.reduce((sum, s) => sum + s.statistics.totalCheckedIn, 0);
    const totalUnchecked = sessions.reduce((sum, s) => sum + (s.statistics.totalUnchecked || 0), 0);
    const totalOnTime = sessions.reduce((sum, s) => sum + s.statistics.onTimeCount, 0);
    const totalLate = sessions.reduce((sum, s) => sum + s.statistics.lateCount, 0);
    const totalAbsent = sessions.reduce((sum, s) => sum + s.statistics.absentCount, 0);

    // Thống kê theo trạng thái phiên
    const statusBreakdown = {
      scheduled: sessions.filter(s => s.status === 'scheduled').length,
      active: sessions.filter(s => s.status === 'active').length,
      completed: sessions.filter(s => s.status === 'completed').length,
      cancelled: sessions.filter(s => s.status === 'cancelled').length
    };

    // Thống kê theo người tạo
    // const creatorStats = {};
    // sessions.forEach(session => {
    //   const creatorId = session.createdBy._id.toString();
    //   if (!creatorStats[creatorId]) {
    //     creatorStats[creatorId] = {
    //       creator: session.createdBy,
    //       sessionCount: 0,
    //       totalTargetUsers: 0,
    //       totalCheckedIn: 0
    //     };
    //   }
    //   creatorStats[creatorId].sessionCount++;
    //   creatorStats[creatorId].totalTargetUsers += session.statistics.totalTargetUsers;
    //   creatorStats[creatorId].totalCheckedIn += session.statistics.totalCheckedIn;
    // });

    return {
      success: true,
      data: {
        summary: {
          totalSessions,
          totalTargetUsers,
          totalCheckedIn,
          totalUnchecked,
          totalOnTime,
          totalLate,
          totalAbsent,
          overallCheckinRate: totalTargetUsers > 0
            ? Math.round((totalCheckedIn / totalTargetUsers) * 100)
            : 0
        },
        statusBreakdown,
        // creatorStats: Object.values(creatorStats),
        // recentSessions: sessions.slice(0, 10) // 10 phiên gần nhất
      }
    };
  }

  /**
   * Lấy thông tin chi tiết phiên chấm công đột xuất
   * @param {String} sessionId - ID phiên chấm công
   * @returns {Object} Thông tin chi tiết phiên
   */
  async getSessionDetail(sessionId) {
    try {
      const session = await SuddenAttendanceSession.findById(sessionId)
        .populate({ path: 'targetUnits', select: 'name' })
        .lean();

      if (!session) {
        return {
          success: false,
          message: {
            head: 'Không tìm thấy',
            body: 'Không tìm thấy phiên chấm công đột xuất'
          }
        };
      }

      return {
        success: true,
        data: session
      };

    } catch (error) {
      console.error('Error getting session detail:', error);
      return {
        success: false
      };
    }
  }

  /**
   * Lấy thống kê chi tiết cho một phiên cụ thể
   */
  async getSessionDetailStatistics(sessionId) {
    const session = await SuddenAttendanceSession.findById(sessionId)
      .select('-targetUsers -validDurationMinutes -createdBy')
      .populate('targetUnits', 'name')
      .lean();

    if (!session) {
      return { success: false, message: 'Không tìm thấy phiên chấm công' };
    }

    // Lấy tất cả bản ghi chấm công của phiên này
    const records = await SuddenAttendanceRecord.find({ session: sessionId })
      .populate({
        path: 'user',
        select: 'name idNumber units',
        populate: {
          path: 'units',
          select: 'name'
        }
      })
      .sort({ checkinTime: 1 })
      .lean();

    // Phân tích dữ liệu
    const analysis = {
      session: session,
      summary: {
        totalTargetUsers: session.statistics.totalTargetUsers,
        totalCheckedIn: session.statistics.totalCheckedIn,
        totalUnchecked: session.statistics.totalUnchecked || 0,
        onTimeCount: session.statistics.onTimeCount,
        lateCount: session.statistics.lateCount,
        absentCount: session.statistics.absentCount,
        checkinRate: session.statistics.totalTargetUsers > 0
          ? Math.round((session.statistics.totalCheckedIn / session.statistics.totalTargetUsers) * 100)
          : 0
      },
      records: records,
      // timeline: this.generateCheckinTimeline(records, session)
    };

    return {
      success: true,
      data: analysis
    };
  }

  /**
   * Tạo timeline chấm công
   */
  generateCheckinTimeline(records, session) {
    const timeline = [];
    const startTime = session.startTime;
    const validEndTime = startTime + (session.validDurationMinutes * 60 * 1000);
    const lateEndTime = startTime + (60 * 60 * 1000);

    // Nhóm records theo khoảng thời gian 5 phút
    const timeSlots = {};

    records.forEach(record => {
      const slotTime = Math.floor((record.checkinTime - startTime) / (5 * 60 * 1000)) * 5;
      if (!timeSlots[slotTime]) {
        timeSlots[slotTime] = {
          timeSlot: slotTime,
          count: 0,
          onTimeCount: 0,
          lateCount: 0
        };
      }
      timeSlots[slotTime].count++;
      if (record.status === 'on_time') {
        timeSlots[slotTime].onTimeCount++;
      } else if (record.status === 'late') {
        timeSlots[slotTime].lateCount++;
      }
    });

    return Object.values(timeSlots).sort((a, b) => a.timeSlot - b.timeSlot);
  }

  /**
   * Kiểm tra xem user có đủ điều kiện tham gia session không
   * @param {String} userId - ID cán bộ
   * @param {Array} targetUnits - Danh sách đơn vị target
   * @returns {Boolean} True nếu user đủ điều kiện
   */
  async checkUserEligibility(userId, targetUnits) {
    try {
      // Nếu không có targetUnits (áp dụng cho tất cả), user luôn đủ điều kiện
      if (!targetUnits || targetUnits.length === 0) {
        return true;
      }

      // Kiểm tra user có thuộc các targetUnits không
      const user = await User.findById(userId).select('units status').lean();
      if (!user || user.status !== 1) {
        return false;
      }

      // Kiểm tra xem có unit nào của user trùng với targetUnits không
      const hasMatchingUnit = user.units.some(userUnit =>
        targetUnits.some(targetUnit =>
          userUnit.toString() === targetUnit.toString()
        )
      );

      return hasMatchingUnit;
    } catch (error) {
      console.error('Error checking user eligibility:', error);
      return false;
    }
  }

  /**
   * Lấy danh sách cán bộ được miễn điểm danh đột xuất
   * @param {Number} sessionStartTime - Thời gian bắt đầu phiên chấm công
   * @param {Array} userIds - Danh sách ID cán bộ cần kiểm tra
   * @returns {Array} Danh sách cán bộ được miễn điểm danh
   */
  async getExemptedUsers(sessionStartTime, userIds) {
    try {
      const sessionDate = new Date(sessionStartTime);
      const dateString = DateUtils.convertYYYYMMDDtoDDMMYYYY(
        sessionDate.toISOString().split('T')[0]
      );

      // Tìm lịch làm việc của ngày đó cho các cán bộ
      const workSchedules = await WorkSchedule.find({
        date: dateString,
        user: { $in: userIds },
        status: 1
      }).populate('user', 'name idNumber').lean();

      const exemptedUsers = [];

      for (const schedule of workSchedules) {
        // Kiểm tra xem có ca nào có trạng thái business_trip hoặc excused không
        const hasBusinessTrip = schedule.shifts.some(shift =>
          shift.status === CONSTANTS.ATTENDANCE.WORK_SCHEDULE_STATUS.BUSINESS_TRIP
        );
        const hasExcused = schedule.shifts.some(shift =>
          shift.status === CONSTANTS.ATTENDANCE.WORK_SCHEDULE_STATUS.EXCUSED
        );

        if (hasBusinessTrip) {
          exemptedUsers.push({
            user: schedule.user._id,
            reason: CONSTANTS.SUDDEN_ATTENDANCE.EXEMPTION_REASONS.BUSINESS_TRIP,
            note: `Cán bộ đang đi công tác ngày ${dateString}`
          });
        } else if (hasExcused) {
          exemptedUsers.push({
            user: schedule.user._id,
            reason: CONSTANTS.SUDDEN_ATTENDANCE.EXEMPTION_REASONS.EXCUSED,
            note: `Cán bộ đang nghỉ phép ngày ${dateString}`
          });
        }
      }

      return exemptedUsers;

    } catch (error) {
      console.error('Error getting exempted users:', error);
      return [];
    }
  }
}

module.exports = new SuddenAttendanceService();

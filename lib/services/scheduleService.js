/**
 * Service xử lý logic lịch làm việc
 * <PERSON>u<PERSON>n lý tạo, c<PERSON><PERSON>, x<PERSON><PERSON> lịch làm việc cho cán bộ
 */

const _ = require('lodash');
const moment = require('moment');
const WorkSchedule = require('../models/workSchedule');
const User = require('../models/user');
const attendancePermission = require('../util/attendancePermission');
const DateUtils = require('../utils/dateUtils');
const attendanceCache = require('../util/attendanceCache');
const scheduleSyncService = require('./scheduleSyncService');

class ScheduleService {
  /**
   * Tạo lịch làm việc cho cán bộ
   * @param {String} creatorId - ID người tạo lịch
   * @param {Array} userIds - Danh sách ID cán bộ
   * @param {Object} dateRange - <PERSON><PERSON><PERSON><PERSON> thờ<PERSON> gian { startDate, endDate }
   * @param {Array} shifts - <PERSON>h sách ca làm việc ['morning', 'afternoon']
   * @returns {Object} Kết quả tạo lịch
   */
  async createWorkSchedule(creatorId, userIds, dateRange, shifts) {
    try {
      // Kiểm tra quyền tạo lịch
      const permissionCheck = await attendancePermission.checkSchedulePermission(creatorId, userIds);

      if (!permissionCheck.canCreate) {
        return {
          success: false,
          message: {
            head: 'Không có quyền',
            body: permissionCheck.message
          },
          data: null
        };
      }

      const allowedUserIds = permissionCheck.allowedUsers;
      const { startDate, endDate } = dateRange;

      // Validate shifts
      const validShifts = shifts.filter(shift => ['morning', 'afternoon'].includes(shift));
      if (validShifts.length === 0) {
        return {
          success: false,
          message: {
            head: 'Lỗi ca làm việc',
            body: 'Phải chọn ít nhất một ca làm việc'
          },
          data: null
        };
      }

      // Validate định dạng startDate và endDate
      if (!DateUtils.isValidDDMMYYYY(startDate)) {
        return {
          success: false,
          message: {
            head: 'Lỗi định dạng ngày',
            body: 'startDate phải có định dạng DD-MM-YYYY'
          },
          data: null
        };
      }

      if (!DateUtils.isValidDDMMYYYY(endDate)) {
        return {
          success: false,
          message: {
            head: 'Lỗi định dạng ngày',
            body: 'endDate phải có định dạng DD-MM-YYYY'
          },
          data: null
        };
      }

      // Tạo danh sách ngày làm việc (định dạng DD-MM-YYYY)
      const workDates = DateUtils.generateDateRangeDDMMYYYY(startDate, endDate);
      const createdSchedules = [];
      const errors = [];

      // Tạo lịch cho từng user và từng ngày
      for (const userId of allowedUserIds) {
        for (const date of workDates) {
          try {
            // Kiểm tra lịch đã tồn tại
            const existingSchedule = await WorkSchedule.findOne({
              user: userId,
              date: date,
              status: 1
            });

            if (existingSchedule) {
              // Cập nhật lịch hiện có
              const updatedShifts = this.mergeShifts(existingSchedule.shifts, validShifts);
              existingSchedule.shifts = updatedShifts;
              existingSchedule.updatedAt = Date.now();
              await existingSchedule.save();
              createdSchedules.push(existingSchedule);
            } else {
              // Tạo lịch mới
              const scheduleData = {
                user: userId,
                date: date,
                workAt: DateUtils.convertDDMMYYYYtoTimestamp(date), // Thêm timestamp của ngày
                shifts: validShifts.map(shiftType => ({
                  type: shiftType,
                  startTime: shiftType === 'morning' ? '08:00' : '14:00',
                  status: 'scheduled'
                })),
                createdBy: creatorId,
                status: 1
              };

              // Kiểm tra leave request và cập nhật status nếu cần
              try {
                const leaveConflict = await scheduleSyncService.checkLeaveConflict(userId, date);
                if (leaveConflict.hasConflict) {
                  // Cập nhật status cho tất cả shifts
                  scheduleData.shifts = scheduleData.shifts.map(shift => ({
                    ...shift,
                    status: leaveConflict.suggestedStatus
                  }));
                }
              } catch (syncError) {
                console.error('Error checking leave conflict:', syncError);
                // Tiếp tục tạo lịch với status mặc định nếu có lỗi
              }

              const newSchedule = await WorkSchedule.create(scheduleData);
              createdSchedules.push(newSchedule);
            }
          } catch (error) {
            errors.push({
              userId,
              date,
              error: error.message
            });
          }
        }
      }

      if (createdSchedules.length > 0) {
        // Invalidate cache cho các user có lịch mới
        const affectedUserIds = [...new Set(createdSchedules.map(s => s.user))];
        for (const userId of affectedUserIds) {
          attendanceCache.invalidateUserSchedule(userId);
        }
      }

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: `Đã tạo lịch làm việc cho ${createdSchedules.length} ca làm việc`
        },
        data: {
          created: createdSchedules.length,
          errors: errors,
          schedules: createdSchedules
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Tạo lịch làm việc linh hoạt (khác nhau theo từng ngày)
   * @param {String} creatorId - ID người tạo lịch
   * @param {Array} schedules - Mảng lịch: [{ date, userIds, shifts }]
   * @returns {Object} Kết quả tạo lịch
   */
  async createFlexibleWorkSchedule(creatorId, schedules) {
    try {
      const createdSchedules = [];
      const errors = [];

      // Kiểm tra duplicate dates trong input
      const dateCount = {};
      for (const schedule of schedules) {
        const { date } = schedule;
        if (dateCount[date]) {
          return {
            success: false,
            message: {
              head: 'Lỗi dữ liệu đầu vào',
              body: `Ngày ${date} bị trùng lặp trong danh sách lịch. Mỗi ngày chỉ được xuất hiện một lần.`
            },
            data: null
          };
        }
        dateCount[date] = true;
      }

      const allUserIds = [...new Set(schedules.flatMap(s => s.userIds))];

      // Kiểm tra quyền tạo lịch cho tất cả users
      const permissionCheck = await attendancePermission.checkSchedulePermission(creatorId, allUserIds);

      if (!permissionCheck.canCreate) {
        return {
          success: false,
          message: {
            head: 'Không có quyền',
            body: permissionCheck.message
          },
          data: null
        };
      }

      const allowedUserIds = permissionCheck.allowedUsers;

      // Tối ưu: Tạo bulk operations thay vì loop tuần tự
      return await this.createSchedulesBulk(creatorId, schedules, allowedUserIds);
    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Tạo schedules với bulk operations để tối ưu performance
   * @param {String} creatorId - ID người tạo
   * @param {Array} schedules - Danh sách schedules
   * @param {Array} allowedUserIds - Users được phép
   * @returns {Object} Kết quả
   */
  async createSchedulesBulk(creatorId, schedules, allowedUserIds) {
    const startTime = Date.now();

    // Bước 1: Chuẩn bị dữ liệu bulk
    const schedulesToCreate = [];
    const userDatePairs = [];

    for (const daySchedule of schedules) {
      const { date, userIds, shifts } = daySchedule;
      const validUserIds = userIds.filter(userId => allowedUserIds.includes(userId));

      // Xử lý shifts - có thể là string array (format cũ) hoặc object array (format mới)
      const validShifts = shifts.filter(shift => {
        const shiftType = typeof shift === 'string' ? shift : shift.type;
        return ['morning', 'afternoon'].includes(shiftType);
      });

      if (validShifts.length === 0) continue;

      for (const userId of validUserIds) {
        userDatePairs.push({ userId, date });
        schedulesToCreate.push({
          user: userId,
          date: date,
          workAt: DateUtils.convertDDMMYYYYtoTimestamp(date), // Thêm timestamp của ngày
          shifts: validShifts.map(shift => {
            // Xử lý cả format cũ (string) và format mới (object)
            if (typeof shift === 'string') {
              return {
                type: shift,
                startTime: shift === 'morning' ? '08:00' : '14:00',
                status: 'scheduled'
              };
            } else {
              return {
                type: shift.type,
                startTime: shift.type === 'morning' ? '08:00' : '14:00',
                status: shift.status || 'scheduled'
              };
            }
          }),
          createdBy: creatorId,
          status: 1,
          createdAt: Date.now(),
          updatedAt: Date.now()
        });
      }
    }

    // Bước 2: Kiểm tra existing schedules với 1 query duy nhất
    const existingSchedules = await WorkSchedule.find({
      $or: userDatePairs.map(pair => ({
        user: pair.userId,
        date: pair.date,
        status: 1
      }))
    }).lean();

    // Tạo map để lookup nhanh
    const existingMap = new Map();
    existingSchedules.forEach(schedule => {
      const key = `${schedule.user}_${schedule.date}`;
      existingMap.set(key, schedule);
    });

    // Bước 3: Phân loại schedules cần tạo mới vs update
    const toCreate = [];
    const toUpdate = [];

    schedulesToCreate.forEach(schedule => {
      const key = `${schedule.user}_${schedule.date}`;
      const existing = existingMap.get(key);

      if (existing) {
        toUpdate.push({
          _id: existing._id,
          shifts: schedule.shifts,
          updatedAt: Date.now()
        });
      } else {
        toCreate.push(schedule);
      }
    });

    // Bước 4: Bulk operations
    const results = [];

    // Bulk insert cho schedules mới
    if (toCreate.length > 0) {
      // Kiểm tra leave request và cập nhật status trước khi insert
      const schedulesWithLeaveCheck = await scheduleSyncService.checkAndSetWorkScheduleStatus(toCreate);
      const insertResult = await WorkSchedule.insertMany(schedulesWithLeaveCheck, { ordered: false });
      results.push(...insertResult);
    }

    // Bulk update cho schedules existing
    if (toUpdate.length > 0) {
      const bulkOps = toUpdate.map(update => ({
        updateOne: {
          filter: { _id: update._id },
          update: {
            $set: {
              shifts: update.shifts,
              updatedAt: update.updatedAt
            }
          }
        }
      }));

      await WorkSchedule.bulkWrite(bulkOps);

      // Lấy updated schedules
      const updatedSchedules = await WorkSchedule.find({
        _id: { $in: toUpdate.map(u => u._id) }
      }).lean();
      results.push(...updatedSchedules);
    }

    const endTime = Date.now();

    // Bước 5: Notifications và cache invalidation
    if (results.length > 0) {
      // Async cache invalidation (không chờ)
      setImmediate(() => {
        const affectedUserIds = [...new Set(results.map(s => s.user))];
        for (const userId of affectedUserIds) {
          attendanceCache.invalidateUserSchedule(userId);
        }
      });
    }

    return {
      success: true,
      message: {
        head: 'Thành công',
        body: `Đã tạo lịch làm việc cho ${results.length} ca làm việc`
      },
      data: {
        created: results.length,
        errors: [],
        schedules: results,
        performance: {
          totalTime: endTime - startTime,
          schedulesProcessed: schedulesToCreate.length,
          newCreated: toCreate.length,
          updated: toUpdate.length
        }
      }
    };
  }

  /**
   * Legacy method - giữ để backward compatibility
   */
  async createFlexibleWorkScheduleLegacy(creatorId, schedules) {
    try {
      const createdSchedules = [];
      const errors = [];

      // Tạo lịch cho từng ngày
      let totalExpected = 0;
      let totalExisting = 0;
      let totalCreated = 0;

      for (const daySchedule of schedules) {
        const { date, userIds, shifts } = daySchedule;

        // Lọc chỉ những user được phép
        const validUserIds = userIds.filter(userId => allowedUserIds.includes(userId));
        totalExpected += validUserIds.length;

        if (validUserIds.length === 0) {
          errors.push({
            date,
            error: 'Không có user nào được phép tạo lịch'
          });
          continue;
        }

        // Validate shifts
        const validShifts = shifts.filter(shift => ['morning', 'afternoon'].includes(shift));
        if (validShifts.length === 0) {
          errors.push({
            date,
            error: 'Không có ca làm việc hợp lệ'
          });
          continue;
        }

        // Tạo lịch cho từng user trong ngày này
        for (const userId of validUserIds) {
          try {
            // Kiểm tra lịch đã tồn tại
            const existingSchedule = await WorkSchedule.findOne({
              user: userId,
              date: date,
              status: 1
            });

            if (existingSchedule) {
              // Cập nhật lịch hiện có
              totalExisting++;
              const updatedShifts = this.mergeShifts(existingSchedule.shifts, validShifts);
              existingSchedule.shifts = updatedShifts;
              existingSchedule.updatedAt = Date.now();
              await existingSchedule.save();
              createdSchedules.push(existingSchedule);
            } else {
              // Tạo lịch mới
              totalCreated++;
              const scheduleData = {
                user: userId,
                date: date,
                workAt: DateUtils.convertDDMMYYYYtoTimestamp(date), // Thêm timestamp của ngày
                shifts: validShifts.map(shiftType => ({
                  type: shiftType,
                  startTime: shiftType === 'morning' ? '08:00' : '14:00',
                  status: 'scheduled'
                })),
                createdBy: creatorId,
                status: 1
              };

              const newSchedule = await WorkSchedule.create(scheduleData);
              createdSchedules.push(newSchedule);
            }
          } catch (error) {
            errors.push({
              date,
              userId,
              error: error.message
            });
          }
        }
      }

      if (createdSchedules.length > 0) {
        // Invalidate cache cho các user có lịch mới
        const affectedUserIds = [...new Set(createdSchedules.map(s => s.user))];
        for (const userId of affectedUserIds) {
          attendanceCache.invalidateUserSchedule(userId);
        }
      }

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: `Đã tạo lịch làm việc cho ${createdSchedules.length} ca làm việc`
        },
        data: {
          created: createdSchedules.length,
          errors: errors,
          schedules: createdSchedules
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Lấy lịch làm việc của cán bộ
   * @param {String} userId - ID cán bộ
   * @param {String} startDate - Ngày bắt đầu (YYYY-MM-DD)
   * @param {String} endDate - Ngày kết thúc (YYYY-MM-DD)
   * @returns {Array} Danh sách lịch làm việc
   */
  async getUserSchedule(userId, startDate, endDate) {
    try {
      // Kiểm tra cache trước
      if (startDate && endDate) {
        const cached = await attendanceCache.getUserSchedule(userId, startDate, endDate);
        if (cached) {
          return {
            success: true,
            message: {
              head: 'Thành công',
              body: 'Lấy lịch làm việc thành công'
            },
            data: cached
          };
        }
      }

      const query = {
        user: userId,
        status: 1
      };

      if (startDate && endDate) {
        query.date = {
          $gte: startDate,
          $lte: endDate
        };
      } else if (startDate) {
        query.date = { $gte: startDate };
      } else if (endDate) {
        query.date = { $lte: endDate };
      }

      const schedules = await WorkSchedule.find(query)
        .populate('user', 'name idNumber')
        .populate('createdBy', 'name')
        .sort({ date: 1 })
        .lean();

      // Cache kết quả nếu có startDate và endDate
      if (startDate && endDate) {
        attendanceCache.cacheUserSchedule(userId, startDate, endDate, schedules);
      }

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy lịch làm việc thành công'
        },
        data: schedules
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Cập nhật lịch làm việc
   * @param {String} scheduleId - ID lịch làm việc
   * @param {Object} updates - Dữ liệu cập nhật
   * @returns {Object} Kết quả cập nhật
   */
  async updateWorkSchedule(scheduleId, updates) {
    try {
      const schedule = await WorkSchedule.findById(scheduleId);

      if (!schedule) {
        return {
          success: false,
          message: {
            head: 'Không tìm thấy',
            body: 'Không tìm thấy lịch làm việc'
          },
          data: null
        };
      }

      // Cập nhật các trường được phép
      const allowedFields = ['shifts', 'status'];
      const updateData = _.pick(updates, allowedFields);
      updateData.updatedAt = Date.now();

      const updatedSchedule = await WorkSchedule.findByIdAndUpdate(
        scheduleId,
        updateData,
        { new: true }
      ).populate('user', 'name idNumber');

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Cập nhật lịch làm việc thành công'
        },
        data: updatedSchedule
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Cập nhật nhiều lịch làm việc cùng lúc
   * @param {Array} scheduleIds - Danh sách ID lịch làm việc
   * @param {Object} updates - Dữ liệu cập nhật
   * @returns {Object} Kết quả cập nhật
   */
  async updateMultipleWorkSchedules(scheduleIds, updates) {
    try {
      const results = [];
      const errors = [];

      for (const scheduleId of scheduleIds) {
        try {
          const result = await this.updateWorkSchedule(scheduleId, updates);
          if (result.success) {
            results.push(result.data);
          } else {
            errors.push({
              scheduleId,
              error: result.message.body
            });
          }
        } catch (error) {
          errors.push({
            scheduleId,
            error: error.message
          });
        }
      }

      return {
        success: true,
        message: {
          head: 'Cập nhật hoàn tất',
          body: `Đã cập nhật ${results.length}/${scheduleIds.length} lịch làm việc`
        },
        data: {
          updated: results.length,
          total: scheduleIds.length,
          errors: errors,
          schedules: results
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Xóa lịch làm việc (soft delete)
   * @param {String} scheduleId - ID lịch làm việc
   * @returns {Object} Kết quả xóa
   */
  async deleteWorkSchedule(scheduleId) {
    try {
      const schedule = await WorkSchedule.findByIdAndUpdate(
        scheduleId,
        {
          status: 0,
          updatedAt: Date.now()
        },
        { new: true }
      );

      if (!schedule) {
        return {
          success: false,
          message: {
            head: 'Không tìm thấy',
            body: 'Không tìm thấy lịch làm việc'
          },
          data: null
        };
      }

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Xóa lịch làm việc thành công'
        },
        data: schedule
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Lấy danh sách lịch làm việc được phân trang theo users (cho admin)
   * @param {String} viewerId - ID người xem
   * @param {Object} filters - Bộ lọc
   * @returns {Object} Danh sách lịch làm việc nhóm theo user
   */
  async getScheduleListByUsers(viewerId, filters = {}) {
    try {
      // Lấy danh sách user có thể quản lý
      // let managedUserIds = await attendancePermission.getManagedUsers(viewerId);
      let managedUserIds = [];

      // Lọc theo đơn vị nếu có
      if (filters.unitId) {
        const unitUsers = await User.find({
          units: filters.unitId,
          status: 1
        }).select('_id').lean();
        const unitUserIds = unitUsers.map(u => u._id.toString());
        managedUserIds = unitUserIds
      }

      // Lọc theo userId cụ thể nếu có
      if (filters.userIds && filters.userIds.length) {
        managedUserIds = filters.userIds;
      }

      // Tạo query cơ bản cho schedules
      const scheduleQuery = {
        status: 1
      };
      if(filters.status) {
        scheduleQuery['shifts.status'] = filters.status;
      }
      if (managedUserIds.length) {
        scheduleQuery.user = { $in: managedUserIds };
      }

      // Áp dụng filters theo ngày (định dạng DD-MM-YYYY)
      if (filters.startDate && filters.endDate) {
        const dateRange = DateUtils.generateDateRangeDDMMYYYY(filters.startDate, filters.endDate);
        scheduleQuery.date = { $in: dateRange };
      } else if (filters.startDate) {
        const startDateYYYYMMDD = DateUtils.convertDDMMYYYYtoYYYYMMDD(filters.startDate);
        const currentDateYYYYMMDD = DateUtils.getCurrentDateYYYYMMDD();
        const endDateDDMMYYYY = DateUtils.convertYYYYMMDDtoDDMMYYYY(currentDateYYYYMMDD);
        const dateRange = DateUtils.generateDateRangeDDMMYYYY(filters.startDate, endDateDDMMYYYY);
        scheduleQuery.date = { $in: dateRange };
      } else if (filters.endDate) {
        const endDateYYYYMMDD = DateUtils.convertDDMMYYYYtoYYYYMMDD(filters.endDate);
        const currentDateYYYYMMDD = DateUtils.getCurrentDateYYYYMMDD();
        const startDateDDMMYYYY = DateUtils.convertYYYYMMDDtoDDMMYYYY(currentDateYYYYMMDD);
        const dateRange = DateUtils.generateDateRangeDDMMYYYY(startDateDDMMYYYY, filters.endDate);
        scheduleQuery.date = { $in: dateRange };
      }

      // Lấy danh sách users có schedules (distinct users)
      const usersWithSchedules = await WorkSchedule.distinct('user', scheduleQuery);

      // Populate thông tin user và sắp xếp theo tên
      const users = await User.find({
        _id: { $in: usersWithSchedules },
        status: 1
      })
      .select('name idNumber units positions')
      .sort({ name: 1 }) // Sắp xếp theo tên alphabetical
      .lean();

      // Tính toán pagination cho users
      const page = parseInt(filters.page) || 1;
      const limit = parseInt(filters.limit) || 20;
      const totalUsers = users.length;
      const totalPages = Math.ceil(totalUsers / limit);
      const skip = (page - 1) * limit;

      // Lấy users cho trang hiện tại
      const usersForCurrentPage = users.slice(skip, skip + limit);
      const userIdsForCurrentPage = usersForCurrentPage.map(u => u._id.toString());

      // Lấy tất cả schedules của users trong trang hiện tại
      const schedulesQuery = {
        ...scheduleQuery,
        user: { $in: userIdsForCurrentPage }
      };

      const [schedules, totalSchedules] = await Promise.all([
        WorkSchedule.find(schedulesQuery)
          .populate({
            path: 'user',
            select: 'name idNumber units positions',
            populate: [
              {
                path: 'units',
                select: 'name'
              },
              {
                path: 'positions',
                select: 'name'
              }
            ]
          })
          .populate('createdBy', 'name')
          .sort({ user: 1, date: -1 }) // Sắp xếp theo user, sau đó theo ngày (mới nhất trước)
          .lean(),
        WorkSchedule.countDocuments(scheduleQuery) // Tổng số schedules (tất cả users)
      ]);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy danh sách lịch làm việc theo user thành công'
        },
        data: {
          schedules,
          pagination: {
            page,
            limit,
            total: totalSchedules, // Tổng số schedules
            totalUsers: totalUsers, // Tổng số users có schedules
            pages: totalPages,
            usersInCurrentPage: usersForCurrentPage.length
          }
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Lấy danh sách lịch làm việc (cho admin) - Method cũ, giữ lại để backward compatibility
   * @param {String} viewerId - ID người xem
   * @param {Object} filters - Bộ lọc
   * @returns {Object} Danh sách lịch làm việc
   */
  async getScheduleList(viewerId, filters = {}) {
    try {
      // Lấy danh sách user có thể quản lý
      let managedUserIds = await attendancePermission.getManagedUsers(viewerId);

      // Lọc theo đơn vị nếu có
      if (filters.unitId) {
        const unitUsers = await User.find({
          units: filters.unitId,
          status: 1
        }).select('_id').lean();
        const unitUserIds = unitUsers.map(u => u._id.toString());
        managedUserIds = managedUserIds.filter(id => unitUserIds.includes(id));
      }

      const query = {
        user: { $in: managedUserIds },
        status: 1
      };

      // Áp dụng filters (định dạng DD-MM-YYYY)
      if (filters.startDate && filters.endDate) {
        // Tạo range query cho date với định dạng DD-MM-YYYY
        // Sử dụng string comparison vì định dạng DD-MM-YYYY không sort được trực tiếp
        // Cần convert sang YYYY-MM-DD để so sánh hoặc dùng regex

        // Tạo danh sách tất cả ngày trong khoảng
        const dateRange = DateUtils.generateDateRangeDDMMYYYY(filters.startDate, filters.endDate);
        query.date = { $in: dateRange };
      } else if (filters.startDate) {
        // Chỉ có startDate - lấy từ ngày đó trở đi
        const startDateYYYYMMDD = DateUtils.convertDDMMYYYYtoYYYYMMDD(filters.startDate);
        const currentDateYYYYMMDD = DateUtils.getCurrentDateYYYYMMDD();
        const endDateDDMMYYYY = DateUtils.convertYYYYMMDDtoDDMMYYYY(currentDateYYYYMMDD);

        const dateRange = DateUtils.generateDateRangeDDMMYYYY(filters.startDate, endDateDDMMYYYY);
        query.date = { $in: dateRange };
      } else if (filters.endDate) {
        // Chỉ có endDate - lấy đến ngày đó
        const endDateYYYYMMDD = DateUtils.convertDDMMYYYYtoYYYYMMDD(filters.endDate);
        const startDate = '01-01-2020'; // Ngày xa trong quá khứ

        const dateRange = DateUtils.generateDateRangeDDMMYYYY(startDate, filters.endDate);
        query.date = { $in: dateRange };
      }

      if (filters.userId) {
        query.user = filters.userId;
      }

      const page = parseInt(filters.page) || 1;
      const limit = parseInt(filters.limit) || 20;
      const skip = (page - 1) * limit;

      const [schedules, total] = await Promise.all([
        WorkSchedule.find(query)
          .populate('user', 'name idNumber units')
          .populate('createdBy', 'name')
          .sort({ date: -1, createdAt: -1 })
          .skip(skip)
          .limit(limit)
          .lean(),
        WorkSchedule.countDocuments(query)
      ]);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy danh sách lịch làm việc thành công'
        },
        data: {
          schedules,
          pagination: {
            page,
            limit,
            total,
            pages: Math.ceil(total / limit)
          }
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Tạo danh sách ngày từ startDate đến endDate
   * @param {String} startDate - Ngày bắt đầu (DD-MM-YYYY)
   * @param {String} endDate - Ngày kết thúc (DD-MM-YYYY)
   * @returns {Array} Danh sách ngày (DD-MM-YYYY)
   * @deprecated Sử dụng DateUtils.generateDateRangeDDMMYYYY thay thế
   */
  generateDateRange(startDate, endDate) {
    // Sử dụng trực tiếp DateUtils
    return DateUtils.generateDateRangeDDMMYYYY(startDate, endDate);
  }

  /**
   * Merge shifts mới với shifts hiện có
   * @param {Array} existingShifts - Shifts hiện có
   * @param {Array} newShifts - Shifts mới (có thể là string array hoặc object array)
   * @returns {Array} Shifts đã merge
   */
  mergeShifts(existingShifts, newShifts) {
    const shiftMap = {};

    // Giữ lại shifts hiện có
    existingShifts.forEach(shift => {
      shiftMap[shift.type] = shift;
    });

    // Thêm shifts mới
    newShifts.forEach(shift => {
      let shiftType, shiftStatus;

      // Xử lý cả format cũ (string) và format mới (object)
      if (typeof shift === 'string') {
        shiftType = shift;
        shiftStatus = 'scheduled';
      } else {
        shiftType = shift.type;
        shiftStatus = shift.status || 'scheduled';
      }

      // Cập nhật hoặc thêm mới shift
      shiftMap[shiftType] = {
        type: shiftType,
        startTime: shiftType === 'morning' ? '08:00' : '14:00',
        status: shiftStatus
      };
    });

    return Object.values(shiftMap);
  }

  /**
   * Tạo lịch làm việc cho system job (không có permission check)
   * @param {String} creatorId - ID người tạo lịch
   * @param {Array} userIds - Danh sách ID cán bộ
   * @param {Object} dateRange - Khoảng thời gian { startDate, endDate }
   * @param {Array} shifts - Danh sách ca làm việc ['morning', 'afternoon']
   * @returns {Object} Kết quả tạo lịch
   */
  async createWorkScheduleForSystem(creatorId, userIds, dateRange, shifts) {
    try {
      const { startDate, endDate } = dateRange;

      // Validate shifts
      const validShifts = shifts.filter(shift => ['morning', 'afternoon'].includes(shift));
      if (validShifts.length === 0) {
        return {
          success: false,
          message: {
            head: 'Lỗi ca làm việc',
            body: 'Phải chọn ít nhất một ca làm việc'
          },
          data: null
        };
      }

      // Validate định dạng startDate và endDate
      if (!DateUtils.isValidDDMMYYYY(startDate)) {
        return {
          success: false,
          message: {
            head: 'Lỗi định dạng ngày',
            body: 'startDate phải có định dạng DD-MM-YYYY'
          },
          data: null
        };
      }

      if (!DateUtils.isValidDDMMYYYY(endDate)) {
        return {
          success: false,
          message: {
            head: 'Lỗi định dạng ngày',
            body: 'endDate phải có định dạng DD-MM-YYYY'
          },
          data: null
        };
      }

      // Tạo danh sách ngày trong khoảng thời gian
      const dateRangeList = DateUtils.generateDateRangeDDMMYYYY(startDate, endDate);
      const createdSchedules = [];
      const errors = [];
      let totalShiftsCreated = 0; // Đếm tổng số ca làm việc được tạo

      // Tạo lịch cho từng ngày
      for (const date of dateRangeList) {
        try {
          // Tạo lịch cho từng user trong ngày đó
          for (const userId of userIds) {
            const existingSchedule = await WorkSchedule.findOne({
              user: userId,
              date: date
            });

            if (existingSchedule) {
              // Merge shifts nếu đã có lịch
              const originalShiftCount = existingSchedule.shifts.length;
              const mergedShifts = this.mergeShifts(existingSchedule.shifts, validShifts);
              const newShiftCount = mergedShifts.length;
              const addedShifts = newShiftCount - originalShiftCount;

              await WorkSchedule.updateOne(
                { _id: existingSchedule._id },
                {
                  $set: {
                    shifts: mergedShifts,
                    updatedAt: Date.now()
                  }
                }
              );

              // Chỉ đếm số ca mới được thêm vào
              totalShiftsCreated += addedShifts;

              createdSchedules.push({
                _id: existingSchedule._id,
                user: userId,
                date: date,
                shifts: mergedShifts,
                addedShifts: addedShifts,
                isUpdate: true
              });
            } else {
              // Tạo lịch mới
              const newSchedule = new WorkSchedule({
                user: userId,
                date: date,
                shifts: validShifts.map(shiftType => ({
                  type: shiftType,
                  startTime: shiftType === 'morning' ? '08:00' : '14:00',
                  status: 'scheduled'
                })),
                createdBy: creatorId,
                status: 1
              });

              const savedSchedule = await newSchedule.save();
              totalShiftsCreated += validShifts.length; // Đếm tất cả ca mới tạo

              createdSchedules.push({
                _id: savedSchedule._id,
                user: userId,
                date: date,
                shifts: savedSchedule.shifts,
                addedShifts: validShifts.length,
                isUpdate: false
              });
            }
          }
        } catch (error) {
          errors.push({
            date,
            error: error.message
          });
        }
      }

      return {
        success: true,
        message: {
          head: 'Tạo lịch thành công',
          body: `Đã tạo ${totalShiftsCreated} ca làm việc mới`
        },
        data: {
          created: totalShiftsCreated, // Số ca làm việc thực tế được tạo
          schedulesProcessed: createdSchedules.length, // Số schedule records được xử lý
          failed: errors.length,
          schedules: createdSchedules,
          errors: errors
        }
      };

    } catch (error) {
      return {
        success: false,
        message: {
          head: 'Lỗi hệ thống',
          body: error.message
        },
        data: null
      };
    }
  }

  /**
   * Tạo lịch làm việc tự động cho tuần tiếp theo
   * Chạy vào 22h thứ 6 hàng tuần
   * @returns {Object} Kết quả tạo lịch tự động
   */
  async createAutoWeeklySchedule() {
    try {
      // Tính toán tuần tiếp theo (từ thứ 2 đến chủ nhật)
      const nextMonday = moment().add(1, 'week').startOf('isoWeek');
      const nextSunday = moment().add(1, 'week').endOf('isoWeek');

      const startDate = nextMonday.format('DD-MM-YYYY');
      const endDate = nextSunday.format('DD-MM-YYYY');

      // Lấy tất cả cán bộ active
      const allUsers = await User.find({ status: 1 }).select('_id').lean();
      const userIds = allUsers.map(user => user._id.toString());

      if (userIds.length === 0) {
        return {
          success: false,
          message: {
            head: 'Không có dữ liệu',
            body: 'Không tìm thấy cán bộ nào để tạo lịch'
          },
          data: null
        };
      }

      // Tạo lịch cho tất cả cán bộ làm cả 2 ca (không có permission check)
      const result = await this.createWorkScheduleForSystem(
        '676535bb4cfaf83fa0c05d42', // System tạo tự động
        userIds,
        { startDate, endDate },
        ['morning', 'afternoon']
      );

      return {
        success: result.success,
        message: {
          head: 'Tạo lịch tự động',
          body: `Đã tạo lịch tự động cho tuần ${startDate} đến ${endDate}: ${result.data?.created || 0} ca làm việc`
        },
        data: {
          ...result.data,
          period: { startDate, endDate },
          totalUsers: userIds.length
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Tìm tuần tiếp theo chưa có lịch làm việc nào
   * @returns {Object} Thông tin tuần tiếp theo { startDate, endDate, weekNumber }
   */
  async findNextAvailableWeek() {
    try {
      let weekOffset = 0;
      let foundAvailableWeek = false;
      let targetWeek = null;

      // Tìm tuần tiếp theo chưa có lịch (tối đa kiểm tra 12 tuần)
      while (!foundAvailableWeek && weekOffset < 12) {
        // Tính toán tuần cần kiểm tra
        const monday = moment().add(weekOffset, 'week').startOf('isoWeek');
        const sunday = moment().add(weekOffset, 'week').endOf('isoWeek');

        const startDateYYYYMMDD = monday.format('YYYY-MM-DD');
        const endDateYYYYMMDD = sunday.format('YYYY-MM-DD');

        // Chuyển đổi sang định dạng DD-MM-YYYY để kiểm tra database
        const startDateDDMMYYYY = DateUtils.convertYYYYMMDDtoDDMMYYYY(startDateYYYYMMDD);
        const endDateDDMMYYYY = DateUtils.convertYYYYMMDDtoDDMMYYYY(endDateYYYYMMDD);

        // Kiểm tra xem tuần này đã có lịch làm việc nào chưa
        const existingSchedules = await WorkSchedule.countDocuments({
          date: {
            $gte: startDateDDMMYYYY,
            $lte: endDateDDMMYYYY
          },
          status: 1
        });

        if (existingSchedules === 0) {
          // Tìm thấy tuần trống
          foundAvailableWeek = true;
          targetWeek = {
            startDate: startDateDDMMYYYY,
            endDate: endDateDDMMYYYY,
            startDateYYYYMMDD: startDateYYYYMMDD,
            endDateYYYYMMDD: endDateYYYYMMDD,
            weekNumber: weekOffset + 1,
            mondayDate: monday.format('DD/MM/YYYY'),
            sundayDate: sunday.format('DD/MM/YYYY')
          };
        } else {
          weekOffset++;
        }
      }

      if (!foundAvailableWeek) {
        return {
          success: false,
          message: {
            head: 'Không tìm thấy tuần trống',
            body: 'Không tìm thấy tuần nào trong 12 tuần tới chưa có lịch làm việc'
          },
          data: null
        };
      }

      return {
        success: true,
        message: {
          head: 'Tìm thấy tuần trống',
          body: `Tuần từ ${targetWeek.mondayDate} đến ${targetWeek.sundayDate} chưa có lịch làm việc`
        },
        data: targetWeek
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Tạo lịch làm việc cho tất cả cán bộ theo tuần với các tùy chọn khác nhau
   * @param {String} creatorId - ID người tạo lịch
   * @param {String} scheduleType - Loại lịch: 'morning_only', 'afternoon_only', 'full_day', 'inherit_previous'
   * @returns {Object} Kết quả tạo lịch tuần
   */
  async generateWeeklyScheduleForAllUsers(creatorId, scheduleType) {
    try {
      // Validate schedule type
      const validTypes = ['morning_only', 'afternoon_only', 'full_day', 'inherit_previous'];
      if (!validTypes.includes(scheduleType)) {
        return {
          success: false,
          message: {
            head: 'Loại lịch không hợp lệ',
            body: `Loại lịch phải là một trong: ${validTypes.join(', ')}`
          },
          data: null
        };
      }

      // Tìm tuần tiếp theo chưa có lịch
      const weekResult = await this.findNextAvailableWeek();
      if (!weekResult.success) {
        return weekResult;
      }

      const targetWeek = weekResult.data;

      // Lấy tất cả cán bộ active
      const allUsers = await User.find({ status: 1 }).select('_id name idNumber').lean();
      if (allUsers.length === 0) {
        return {
          success: false,
          message: {
            head: 'Không có dữ liệu',
            body: 'Không tìm thấy cán bộ nào để tạo lịch'
          },
          data: null
        };
      }

      const userIds = allUsers.map(user => user._id.toString());

      // Tạo danh sách ngày trong tuần (DD-MM-YYYY)
      const weekDates = DateUtils.generateDateRangeDDMMYYYY(targetWeek.startDate, targetWeek.endDate);

      let schedules = [];
      let shifts = [];

      // Xác định ca làm việc dựa trên loại lịch
      switch (scheduleType) {
        case 'morning_only':
          shifts = ['morning'];
          break;
        case 'afternoon_only':
          shifts = ['afternoon'];
          break;
        case 'full_day':
          shifts = ['morning', 'afternoon'];
          break;
        case 'inherit_previous':
          // Xử lý kế thừa từ tuần trước - sử dụng method riêng
          return await this.createInheritedWeeklySchedule(creatorId, userIds, weekDates, targetWeek);
      }

      // Tạo lịch cho các loại khác inherit_previous
      if (scheduleType !== 'inherit_previous') {
        for (const date of weekDates) {
          schedules.push({
            date: date,
            userIds: userIds,
            shifts: shifts
          });
        }
      }

      // Validation: Kiểm tra số lượng schedules hợp lý
      if (schedules.length > 1000) {
        return {
          success: false,
          message: {
            head: 'Quá nhiều lịch',
            body: `Số lượng lịch cần tạo (${schedules.length}) vượt quá giới hạn cho phép (1000). Vui lòng kiểm tra lại dữ liệu.`
          },
          data: null
        };
      }

      // Tạo lịch sử dụng service hiện có
      const result = await this.createFlexibleWorkSchedule(creatorId, schedules);

      if (result.success) {
        return {
          success: true,
          message: {
            head: 'Tạo lịch tuần thành công',
            body: `Đã tạo lịch ${this.getScheduleTypeDescription(scheduleType)} cho tuần từ ${targetWeek.mondayDate} đến ${targetWeek.sundayDate}. Tổng cộng: ${result.data.created} ca làm việc cho ${allUsers.length} cán bộ.`
          },
          data: {
            ...result.data,
            scheduleType: scheduleType,
            scheduleTypeDescription: this.getScheduleTypeDescription(scheduleType),
            weekInfo: targetWeek,
            totalUsers: allUsers.length,
            weekDates: weekDates
          }
        };
      } else {
        return result;
      }

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Tạo lịch làm việc kế thừa từ tuần trước - Tối ưu hiệu suất
   * @param {String} creatorId - ID người tạo lịch
   * @param {Array} userIds - Danh sách ID cán bộ
   * @param {Array} weekDates - Danh sách ngày trong tuần (DD-MM-YYYY)
   * @param {Object} targetWeek - Thông tin tuần đích
   * @returns {Object} Kết quả tạo lịch kế thừa
   */
  async createInheritedWeeklySchedule(creatorId, userIds, weekDates, targetWeek) {
    try {
      const overallStartTime = Date.now();

      // Tối ưu: Kiểm tra quyền tạo lịch
      const attendancePermission = require('../util/attendancePermission');
      const permissionCheck = await attendancePermission.checkSchedulePermission(creatorId, userIds);

      if (!permissionCheck.canCreate) {
        return {
          success: false,
          message: {
            head: 'Không có quyền',
            body: permissionCheck.message
          },
          data: null
        };
      }

      const allowedUserIds = permissionCheck.allowedUsers;

      // Tối ưu: Tính toán tuần trước một lần
      const previousWeekStart = moment(targetWeek.startDateYYYYMMDD).subtract(1, 'week').startOf('isoWeek');
      const previousWeekEnd = moment(targetWeek.startDateYYYYMMDD).subtract(1, 'week').endOf('isoWeek');

      const prevStartDate = DateUtils.convertYYYYMMDDtoDDMMYYYY(previousWeekStart.format('YYYY-MM-DD'));
      const prevEndDate = DateUtils.convertYYYYMMDDtoDDMMYYYY(previousWeekEnd.format('YYYY-MM-DD'));

      // Tối ưu: Query previous schedules với index tối ưu
      const queryStartTime = Date.now();
      const previousSchedules = await WorkSchedule.find({
        user: { $in: allowedUserIds },
        date: {
          $gte: prevStartDate,
          $lte: prevEndDate
        },
        status: 1
      })
      .select('user date shifts') // Chỉ select fields cần thiết
      .lean();

      // Tối ưu: Nhóm lịch theo user và ngày với Map để tăng tốc lookup
      const processingStartTime = Date.now();
      const schedulesByUser = new Map();

      // Tối ưu: Pre-calculate date mappings để tránh tính toán lặp lại
      const dateMappings = new Map();
      for (let i = 1; i <= 7; i++) {
        const newDate = moment(targetWeek.startDateYYYYMMDD).isoWeekday(i);
        const newDateDDMMYYYY = DateUtils.convertYYYYMMDDtoDDMMYYYY(newDate.format('YYYY-MM-DD'));
        dateMappings.set(i, newDateDDMMYYYY);
      }

      previousSchedules.forEach(schedule => {
        const userId = schedule.user.toString();
        if (!schedulesByUser.has(userId)) {
          schedulesByUser.set(userId, new Map());
        }

        // Tối ưu: Tính toán ngày tương ứng với pre-calculated mappings
        const prevDate = moment(DateUtils.convertDDMMYYYYtoYYYYMMDD(schedule.date));
        const dayOfWeek = prevDate.isoWeekday(); // 1 = Monday, 7 = Sunday
        const newDateDDMMYYYY = dateMappings.get(dayOfWeek);

        schedulesByUser.get(userId).set(newDateDDMMYYYY, schedule.shifts.map(shift => shift.type));
      });

      // Tối ưu: Chuẩn bị dữ liệu bulk với logic đơn giản hơn
      const bulkStartTime = Date.now();
      const schedulesToCreate = [];
      const userDatePairs = [];

      // Tối ưu: Sử dụng nested loop với Map lookup
      for (const date of weekDates) {
        for (const userId of allowedUserIds) {
          let userShifts = [];

          // Tối ưu: Sử dụng Map.get() thay vì object property access
          const userScheduleMap = schedulesByUser.get(userId);
          if (userScheduleMap && userScheduleMap.has(date)) {
            userShifts = userScheduleMap.get(date);
          }

          // Nếu user không có lịch hoặc không có ca nào, mặc định full day
          if (!userShifts || userShifts.length === 0) {
            userShifts = ['morning', 'afternoon'];
          }

          userDatePairs.push({ userId, date });
          schedulesToCreate.push({
            user: userId,
            date: date,
            workAt: DateUtils.convertDDMMYYYYtoTimestamp(date), // Thêm timestamp của ngày
            shifts: userShifts.map(shiftType => ({
              type: shiftType,
              startTime: shiftType === 'morning' ? '08:00' : '14:00',
              status: 'scheduled'
            })),
            createdBy: creatorId,
            status: 1,
            createdAt: Date.now(),
            updatedAt: Date.now()
          });
        }
      }

      // Tối ưu: Kiểm tra existing schedules với query tối ưu hơn
      const existingCheckStartTime = Date.now();
      const existingSchedules = await WorkSchedule.find({
        user: { $in: allowedUserIds },
        date: { $in: weekDates },
        status: 1
      })
      .select('_id user date') // Chỉ select fields cần thiết
      .lean();

      // Tối ưu: Tạo map để lookup nhanh với Set cho performance
      const existingMap = new Map();
      existingSchedules.forEach(schedule => {
        const key = `${schedule.user}_${schedule.date}`;
        existingMap.set(key, schedule);
      });

      // Tối ưu: Phân loại schedules với logic đơn giản hơn
      const classifyStartTime = Date.now();
      const toCreate = [];
      const toUpdate = [];
      const errors = []; // Sửa lỗi: Khởi tạo biến errors

      schedulesToCreate.forEach(schedule => {
        const key = `${schedule.user}_${schedule.date}`;
        const existing = existingMap.get(key);

        if (existing) {
          toUpdate.push({
            _id: existing._id,
            shifts: schedule.shifts,
            updatedAt: Date.now()
          });
        } else {
          toCreate.push(schedule);
        }
      });

      // Tối ưu: Bulk operations với error handling
      const bulkOperationStartTime = Date.now();
      const createdSchedules = [];

      try {
        // Bulk insert cho schedules mới
        if (toCreate.length > 0) {
          const insertStartTime = Date.now();
          const insertResult = await WorkSchedule.insertMany(toCreate, {
            ordered: false,
            rawResult: false // Tối ưu: Không cần raw result
          });
          createdSchedules.push(...insertResult);
        }

        // Bulk update cho schedules existing
        if (toUpdate.length > 0) {
          const updateStartTime = Date.now();
          const bulkOps = toUpdate.map(update => ({
            updateOne: {
              filter: { _id: update._id },
              update: {
                $set: {
                  shifts: update.shifts,
                  updatedAt: update.updatedAt
                }
              }
            }
          }));

          const bulkResult = await WorkSchedule.bulkWrite(bulkOps, { ordered: false });

          // Tối ưu: Lấy updated schedules với select tối thiểu
          const updatedSchedules = await WorkSchedule.find({
            _id: { $in: toUpdate.map(u => u._id) }
          })
          .select('_id user date shifts createdBy status createdAt updatedAt')
          .lean();
          createdSchedules.push(...updatedSchedules);
        }
      } catch (bulkError) {
        console.error('Bulk operation error:', bulkError);
        errors.push(`Lỗi bulk operation: ${bulkError.message}`);
      }

      // Tối ưu: Post-processing với async operations
      const postProcessStartTime = Date.now();

      if (createdSchedules.length > 0) {
        try {
          // Invalidate cache cho các user có lịch mới
          const affectedUserIds = [...new Set(createdSchedules.map(s => s.user))];
          for (const userId of affectedUserIds) {
            attendanceCache.invalidateUserSchedule(userId);
          }
        } catch (postError) {
          console.error('Post-processing error:', postError);
          errors.push(`Lỗi post-processing: ${postError.message}`);
        }
      }

      const totalTime = Date.now() - overallStartTime;

      return {
        success: true,
        message: {
          head: 'Tạo lịch tuần thành công',
          body: `Đã tạo lịch kế thừa từ tuần ${previousWeekStart.format('DD/MM/YYYY')} - ${previousWeekEnd.format('DD/MM/YYYY')} cho tuần từ ${targetWeek.mondayDate} đến ${targetWeek.sundayDate}. Tổng cộng: ${createdSchedules.length} ca làm việc cho ${allowedUserIds.length} cán bộ. (Thời gian xử lý: ${totalTime}ms)`
        },
        data: {
          created: createdSchedules.length,
          errors: errors,
          schedules: createdSchedules,
          scheduleType: 'inherit_previous',
          scheduleTypeDescription: this.getScheduleTypeDescription('inherit_previous'),
          weekInfo: targetWeek,
          totalUsers: allowedUserIds.length,
          weekDates: weekDates,
          previousWeekInfo: {
            startDate: prevStartDate,
            endDate: prevEndDate,
            totalPreviousSchedules: previousSchedules.length
          },
          performance: {
            totalTime: totalTime,
            breakdown: {
              permissionCheck: 'logged separately',
              queryPreviousSchedules: 'logged separately',
              dataProcessing: 'logged separately',
              bulkOperations: 'logged separately',
              postProcessing: 'logged separately'
            }
          }
        }
      };

    } catch (error) {
      console.error('Inherit schedule creation error:', error);
      return {
        success: false,
        message: {
          head: 'Lỗi hệ thống',
          body: `Có lỗi xảy ra khi tạo lịch kế thừa: ${error.message}`
        },
        data: null
      };
    }
  }

  /**
   * Tạo lịch kế thừa từ tuần trước - Helper method (deprecated, sử dụng createInheritedWeeklySchedule)
   * @param {Array} userIds - Danh sách ID cán bộ
   * @param {Array} weekDates - Danh sách ngày trong tuần (DD-MM-YYYY)
   * @param {Object} targetWeek - Thông tin tuần đích
   * @returns {Object} Kết quả tạo lịch kế thừa
   */
  async generateInheritedWeeklySchedule(userIds, weekDates, targetWeek) {
    try {
      // Tính toán tuần trước
      const previousWeekStart = moment(targetWeek.startDateYYYYMMDD).subtract(1, 'week').startOf('isoWeek');
      const previousWeekEnd = moment(targetWeek.startDateYYYYMMDD).subtract(1, 'week').endOf('isoWeek');

      const prevStartDate = DateUtils.convertYYYYMMDDtoDDMMYYYY(previousWeekStart.format('YYYY-MM-DD'));
      const prevEndDate = DateUtils.convertYYYYMMDDtoDDMMYYYY(previousWeekEnd.format('YYYY-MM-DD'));

      // Lấy lịch tuần trước cho tất cả users
      const previousSchedules = await WorkSchedule.find({
        user: { $in: userIds },
        date: {
          $gte: prevStartDate,
          $lte: prevEndDate
        },
        status: 1
      }).lean();

      // Nhóm lịch theo user và ngày
      const schedulesByUser = {};

      previousSchedules.forEach(schedule => {
        const userId = schedule.user.toString();
        if (!schedulesByUser[userId]) {
          schedulesByUser[userId] = {};
        }

        // Tính toán ngày tương ứng trong tuần mới
        const prevDate = moment(DateUtils.convertDDMMYYYYtoYYYYMMDD(schedule.date));
        const dayOfWeek = prevDate.isoWeekday(); // 1 = Monday, 7 = Sunday

        // Tính ngày tương ứng trong tuần mới
        const newDate = moment(targetWeek.startDateYYYYMMDD).isoWeekday(dayOfWeek);
        const newDateDDMMYYYY = DateUtils.convertYYYYMMDDtoDDMMYYYY(newDate.format('YYYY-MM-DD'));

        schedulesByUser[userId][newDateDDMMYYYY] = schedule.shifts.map(shift => shift.type);
      });

      // Tạo lịch cho tuần mới - Nhóm tất cả users theo shifts cho mỗi ngày
      const schedules = [];

      // Duyệt qua từng ngày trong tuần
      for (const date of weekDates) {
        // Map để nhóm users theo shifts
        const usersByShifts = new Map();

        // Duyệt qua tất cả users
        for (const userId of userIds) {
          let userShifts = [];

          // Kiểm tra user có lịch tuần trước không
          if (schedulesByUser[userId] && schedulesByUser[userId][date]) {
            userShifts = schedulesByUser[userId][date];
          }

          // Nếu user không có lịch hoặc không có ca nào, mặc định full day
          if (!userShifts || userShifts.length === 0) {
            userShifts = ['morning', 'afternoon'];
          }

          // Tạo key từ shifts (sắp xếp để đảm bảo consistency)
          const shiftsKey = [...userShifts].sort().join(',');

          // Nhóm users theo shifts
          if (!usersByShifts.has(shiftsKey)) {
            usersByShifts.set(shiftsKey, {
              shifts: [...userShifts].sort(),
              userIds: []
            });
          }

          usersByShifts.get(shiftsKey).userIds.push(userId);
        }

        // Tạo schedule entries cho ngày này
        usersByShifts.forEach(group => {
          if (group.userIds.length > 0) {
            schedules.push({
              date: date,
              userIds: group.userIds,
              shifts: group.shifts
            });
          }
        });
      }

      return {
        success: true,
        message: {
          head: 'Tạo lịch kế thừa thành công',
          body: `Đã tạo lịch kế thừa từ tuần ${previousWeekStart.format('DD/MM/YYYY')} - ${previousWeekEnd.format('DD/MM/YYYY')}`
        },
        data: {
          schedules: schedules,
          previousWeekInfo: {
            startDate: prevStartDate,
            endDate: prevEndDate,
            totalPreviousSchedules: previousSchedules.length
          }
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Lấy mô tả loại lịch làm việc
   * @param {String} scheduleType - Loại lịch
   * @returns {String} Mô tả loại lịch
   */
  getScheduleTypeDescription(scheduleType) {
    const descriptions = {
      'morning_only': 'ca sáng (8:00)',
      'afternoon_only': 'ca chiều (14:00)',
      'full_day': 'cả ngày (ca sáng và ca chiều)',
      'inherit_previous': 'kế thừa từ tuần trước'
    };
    return descriptions[scheduleType] || 'không xác định';
  }

  /**
   * Cập nhật lịch làm việc linh hoạt (khác nhau theo từng ngày)
   * @param {String} updaterId - ID người cập nhật
   * @param {Array} schedules - Mảng lịch: [{ date, userIds, shifts }]
   * @returns {Object} Kết quả cập nhật lịch
   */
  async updateFlexibleWorkSchedule(updaterId, schedules) {
    try {
      const updatedSchedules = [];
      const errors = [];
      const allUserIds = [...new Set(schedules.flatMap(s => s.userIds))];

      // Kiểm tra quyền cập nhật lịch cho tất cả users
      const permissionCheck = await attendancePermission.checkSchedulePermission(updaterId, allUserIds);

      if (!permissionCheck.canCreate) {
        return {
          success: false,
          message: {
            head: 'Không có quyền',
            body: permissionCheck.message
          },
          data: null
        };
      }

      const allowedUserIds = permissionCheck.allowedUsers;

      // Cập nhật lịch cho từng ngày
      for (const daySchedule of schedules) {
        const { date, userIds, shifts } = daySchedule;

        // Validate định dạng date DD-MM-YYYY
        if (!DateUtils.isValidDDMMYYYY(date)) {
          errors.push({
            date,
            error: 'Định dạng ngày không hợp lệ. Cần định dạng DD-MM-YYYY'
          });
          continue;
        }

        const dateDDMMYYYY = date;

        // Lọc chỉ những user được phép
        const validUserIds = userIds.filter(userId => allowedUserIds.includes(userId));

        if (validUserIds.length === 0) {
          errors.push({
            date,
            error: 'Không có user nào được phép cập nhật lịch'
          });
          continue;
        }

        // Validate shifts - hỗ trợ cả format cũ (string) và format mới (object)
        const validShifts = shifts.filter(shift => {
          const shiftType = typeof shift === 'string' ? shift : shift.type;
          return ['morning', 'afternoon'].includes(shiftType);
        });
        if (validShifts.length === 0) {
          errors.push({
            date,
            error: 'Không có ca làm việc hợp lệ'
          });
          continue;
        }

        // Cập nhật lịch cho từng user trong ngày này
        for (const userId of validUserIds) {
          try {
            // Tìm lịch hiện có (sử dụng định dạng DD-MM-YYYY)
            const existingSchedule = await WorkSchedule.findOne({
              user: userId,
              date: dateDDMMYYYY,
              status: 1
            });

            if (existingSchedule) {
              // Cập nhật lịch hiện có
              const newShifts = validShifts.map(shift => {
                // Xử lý cả format cũ (string) và format mới (object)
                if (typeof shift === 'string') {
                  return {
                    type: shift,
                    startTime: shift === 'morning' ? '08:00' : '14:00',
                    status: 'scheduled'
                  };
                } else {
                  return {
                    type: shift.type,
                    startTime: shift.type === 'morning' ? '08:00' : '14:00',
                    status: shift.status || 'scheduled'
                  };
                }
              });

              existingSchedule.shifts = newShifts;
              existingSchedule.updatedAt = Date.now();
              await existingSchedule.save();
              updatedSchedules.push(existingSchedule);
            } else {
              // Tạo lịch mới nếu chưa có (sử dụng định dạng DD-MM-YYYY)
              const scheduleData = {
                user: userId,
                date: dateDDMMYYYY,
                workAt: DateUtils.convertDDMMYYYYtoTimestamp(dateDDMMYYYY), // Thêm timestamp của ngày
                shifts: validShifts.map(shift => {
                  // Xử lý cả format cũ (string) và format mới (object)
                  if (typeof shift === 'string') {
                    return {
                      type: shift,
                      startTime: shift === 'morning' ? '08:00' : '14:00',
                      status: 'scheduled'
                    };
                  } else {
                    return {
                      type: shift.type,
                      startTime: shift.type === 'morning' ? '08:00' : '14:00',
                      status: shift.status || 'scheduled'
                    };
                  }
                }),
                createdBy: updaterId,
                status: 1
              };

              const newSchedule = await WorkSchedule.create(scheduleData);
              updatedSchedules.push(newSchedule);
            }

            // Invalidate cache
            attendanceCache.invalidateUserSchedule(userId);

          } catch (error) {
            errors.push({
              date: dateDDMMYYYY,
              userId,
              error: error.message
            });
          }
        }
      }

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: `Đã cập nhật lịch làm việc cho ${updatedSchedules.length} ca làm việc`
        },
        data: {
          updated: updatedSchedules.length,
          errors: errors,
          schedules: updatedSchedules
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Cập nhật lịch làm việc theo danh sách ID - Phiên bản đơn giản hóa
   * @param {String} updaterId - ID người cập nhật
   * @param {Array} schedules - Mảng lịch: [{ scheduleId, shifts }]
   * @returns {Object} Kết quả cập nhật lịch
   *
   * Mô tả:
   * - Chỉ cho phép cập nhật ca trực (shifts) của lịch làm việc
   * - Tự động cập nhật thời gian bắt đầu ca theo loại ca (morning: 08:00, afternoon: 14:00)
   * - Không thay đổi các thông tin khác như ngày, user, status
   * - Kiểm tra quyền cập nhật lịch của user
   */
  async updateWorkSchedulesByIds(updaterId, schedules) {
    try {
      const updatedSchedules = [];
      const errors = [];

      // Lấy danh sách tất cả scheduleId để kiểm tra quyền
      const scheduleIds = schedules.map(s => s.scheduleId);

      // Tìm tất cả lịch cần cập nhật
      const existingSchedules = await WorkSchedule.find({
        _id: { $in: scheduleIds },
        status: 1
      }).populate('user', 'name idNumber');

      if (existingSchedules.length === 0) {
        return {
          success: false,
          message: {
            head: 'Không tìm thấy lịch',
            body: 'Không tìm thấy lịch làm việc nào để cập nhật'
          },
          data: null
        };
      }

      // Kiểm tra quyền cập nhật cho tất cả user có lịch
      const affectedUserIds = [...new Set(existingSchedules.map(s => s.user._id.toString()))];
      const permissionCheck = await attendancePermission.checkSchedulePermission(updaterId, affectedUserIds);

      if (!permissionCheck.canCreate) {
        return {
          success: false,
          message: {
            head: 'Không có quyền',
            body: permissionCheck.message
          },
          data: null
        };
      }

      const allowedUserIds = permissionCheck.allowedUsers.map(id => id.toString());

      // Xử lý từng lịch cần cập nhật
      for (const scheduleUpdate of schedules) {
        const { scheduleId, shifts } = scheduleUpdate;

        try {
          // Tìm lịch tương ứng
          const existingSchedule = existingSchedules.find(s => s._id.toString() === scheduleId);

          if (!existingSchedule) {
            errors.push({
              scheduleId,
              error: 'Không tìm thấy lịch làm việc'
            });
            continue;
          }

          // Kiểm tra quyền cập nhật lịch của user này
          if (!allowedUserIds.includes(existingSchedule.user._id.toString())) {
            errors.push({
              scheduleId,
              error: 'Không có quyền cập nhật lịch của user này'
            });
            continue;
          }

          // Validate shifts - hỗ trợ cả format cũ (string) và format mới (object)
          const validShifts = shifts.filter(shift => {
            const shiftType = typeof shift === 'string' ? shift : shift.type;
            return ['morning', 'afternoon'].includes(shiftType);
          });

          if (validShifts.length === 0) {
            errors.push({
              scheduleId,
              error: 'Không có ca làm việc hợp lệ'
            });
            continue;
          }

          // Tạo danh sách ca trực mới với thời gian tự động
          const newShifts = validShifts.map(shift => {
            // Xử lý cả format cũ (string) và format mới (object)
            if (typeof shift === 'string') {
              return {
                type: shift,
                startTime: shift === 'morning' ? '08:00' : '14:00',
                status: 'scheduled'
              };
            } else {
              return {
                type: shift.type,
                startTime: shift.type === 'morning' ? '08:00' : '14:00',
                status: shift.status || 'scheduled'
              };
            }
          });

          // Cập nhật lịch
          existingSchedule.shifts = newShifts;
          existingSchedule.updatedAt = Date.now();
          await existingSchedule.save();

          updatedSchedules.push(existingSchedule);

          // Invalidate cache cho user này
          attendanceCache.invalidateUserSchedule(existingSchedule.user._id);

        } catch (error) {
          errors.push({
            scheduleId,
            error: error.message
          });
        }
      }

      return {
        success: true,
        message: {
          head: 'Cập nhật thành công',
          body: `Đã cập nhật ${updatedSchedules.length}/${schedules.length} lịch làm việc`
        },
        data: {
          updated: updatedSchedules.length,
          total: schedules.length,
          errors: errors,
          schedules: updatedSchedules
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }
}

module.exports = new ScheduleService();
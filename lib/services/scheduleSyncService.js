const LeaveRequest = require('../models/leaveRequest');
const WorkSchedule = require('../models/workSchedule');
const DateUtils = require('../utils/dateUtils');

/**
 * Service xử lý đồng bộ giữa Leave Request và Work Schedule
 * Đảm bảo tính nhất quán dữ liệu giữa hai hệ thống lịch
 */
class ScheduleSyncService {

  /**
   * L<PERSON>y danh sách Leave Request trong khoảng thời gian
   * @param {String} userId - ID của user
   * @param {String} startDate - <PERSON><PERSON><PERSON> bắt đầu (DD-MM-YYYY)
   * @param {String} endDate - <PERSON><PERSON><PERSON> kết thúc (DD-MM-YYYY)
   * @returns {Array} Danh sách Leave Request
   */
  async getLeaveRequestsInDateRange(userId, startDate, endDate) {
    try {
      // Convert DD-MM-YYYY sang timestamp để query MongoDB
      const rangeStartTime = DateUtils.convertDDMMYYYYtoTimestamp(startDate);
      const rangeEndTime = DateUtils.convertDDMMYYYYtoTimestamp(endDate) + (24 * 60 * 60 * 1000); // +1 ngày

      // Query MongoDB với timestamp - hiệu quả hơn nhiều
      const leaveRequests = await LeaveRequest.find({
        user: userId,
        status: 'approved', // Chỉ lấy các đơn đã được duyệt
        $or: [
          // Leave request bắt đầu trong khoảng thời gian
          {
            startTime: { $gte: rangeStartTime, $lte: rangeEndTime }
          },
          // Leave request kết thúc trong khoảng thời gian
          {
            endTime: { $gte: rangeStartTime, $lte: rangeEndTime }
          },
          // Leave request bao trùm cả khoảng thời gian
          {
            startTime: { $lte: rangeStartTime },
            endTime: { $gte: rangeEndTime }
          }
        ]
      }).lean();

      return leaveRequests;
    } catch (error) {
      console.error('Error getting leave requests in date range:', error);
      return [];
    }
  }

  /**
   * Tạo danh sách ngày từ startDate đến endDate
   * @param {String} startDate - Ngày bắt đầu (DD-MM-YYYY)
   * @param {String} endDate - Ngày kết thúc (DD-MM-YYYY)
   * @returns {Array} Danh sách ngày (DD-MM-YYYY)
   */
  generateDateRange(startDate, endDate) {
    try {
      return DateUtils.generateDateRangeDDMMYYYY(startDate, endDate);
    } catch (error) {
      console.error('Error generating date range:', error);
      return [];
    }
  }

  /**
   * Cập nhật Work Schedule dựa trên Leave Request
   * @param {Object} leaveRequest - Leave Request object
   * @param {String} action - Hành động: 'create', 'update', 'delete'
   * @returns {Object} Kết quả cập nhật
   */
  async updateWorkScheduleFromLeave(leaveRequest, action = 'create') {
    try {
      const { user, type, startDate, endDate } = leaveRequest;

      // Tạo danh sách ngày cần cập nhật
      const dateRange = this.generateDateRange(startDate, endDate);

      if (dateRange.length === 0) {
        return {
          success: false,
          message: 'Không thể tạo danh sách ngày từ khoảng thời gian'
        };
      }

      // Xác định status mới cho work schedule
      let newStatus;
      if (action === 'delete') {
        newStatus = 'scheduled'; // Reset về trạng thái ban đầu
      } else {
        newStatus = type === 'leave' ? 'excused' : 'business_trip';
      }

      // Tìm và cập nhật tất cả work schedule trong khoảng thời gian
      const updateResult = await WorkSchedule.updateMany(
        {
          user: user,
          date: { $in: dateRange },
          status: 1 // Chỉ cập nhật lịch đang active
        },
        {
          $set: {
            'shifts.$[].status': newStatus,
            updatedAt: Date.now()
          }
        }
      );

      return {
        success: true,
        message: `Đã cập nhật ${updateResult.modifiedCount} lịch làm việc`,
        data: {
          modifiedCount: updateResult.modifiedCount,
          dateRange: dateRange,
          newStatus: newStatus
        }
      };

    } catch (error) {
      console.error('Error updating work schedule from leave:', error);
      return {
        success: false,
        message: 'Lỗi khi cập nhật lịch làm việc'
      };
    }
  }

  /**
   * Kiểm tra và set status cho Work Schedule khi tạo mới
   * @param {Array} workSchedules - Danh sách work schedule cần kiểm tra
   * @returns {Array} Danh sách work schedule đã được cập nhật status
   */
  async checkAndSetWorkScheduleStatus(workSchedules) {
    try {
      const updatedSchedules = [];

      for (const schedule of workSchedules) {
        const { user, date } = schedule;

        // Kiểm tra có leave request nào trong ngày này không
        const leaveRequests = await this.getLeaveRequestsInDateRange(user, date, date);

        if (leaveRequests.length > 0) {
          // Có leave request, cập nhật status cho tất cả shifts
          const leaveRequest = leaveRequests[0]; // Lấy leave request đầu tiên
          const newStatus = leaveRequest.type === 'leave' ? 'excused' : 'business_trip';

          // Cập nhật status cho tất cả shifts trong schedule
          const updatedShifts = schedule.shifts.map(shift => ({
            ...shift,
            status: newStatus
          }));

          updatedSchedules.push({
            ...schedule,
            shifts: updatedShifts
          });
        } else {
          // Không có leave request, giữ nguyên
          updatedSchedules.push(schedule);
        }
      }

      return updatedSchedules;
    } catch (error) {
      console.error('Error checking and setting work schedule status:', error);
      return workSchedules; // Trả về danh sách gốc nếu có lỗi
    }
  }

  /**
   * Đồng bộ ngược từ Work Schedule sang Leave Request
   * Kiểm tra xem có conflict không khi tạo work schedule
   * @param {String} userId - ID của user
   * @param {String} date - Ngày (DD-MM-YYYY)
   * @returns {Object} Thông tin về leave request (nếu có)
   */
  async checkLeaveConflict(userId, date) {
    try {
      const leaveRequests = await this.getLeaveRequestsInDateRange(userId, date, date);

      if (leaveRequests.length > 0) {
        const leaveRequest = leaveRequests[0];
        return {
          hasConflict: true,
          leaveRequest: leaveRequest,
          suggestedStatus: leaveRequest.type === 'leave' ? 'excused' : 'business_trip'
        };
      }

      return {
        hasConflict: false,
        leaveRequest: null,
        suggestedStatus: 'scheduled'
      };
    } catch (error) {
      console.error('Error checking leave conflict:', error);
      return {
        hasConflict: false,
        leaveRequest: null,
        suggestedStatus: 'scheduled'
      };
    }
  }

  /**
   * Cập nhật Work Schedule khi Leave Request thay đổi status
   * @param {Object} leaveRequest - Leave Request object
   * @param {String} oldStatus - Status cũ
   * @param {String} newStatus - Status mới
   * @returns {Object} Kết quả cập nhật
   */
  async handleLeaveStatusChange(leaveRequest, oldStatus, newStatus) {
    try {
      const { user, type, startDate, endDate } = leaveRequest;

      // Nếu từ approved sang rejected/deleted -> reset work schedule
      if (oldStatus === 'approved' && (newStatus === 'rejected' || newStatus === 'deleted')) {
        return await this.updateWorkScheduleFromLeave(leaveRequest, 'delete');
      }

      // Nếu từ pending/rejected sang approved -> cập nhật work schedule
      if ((oldStatus === 'pending' || oldStatus === 'rejected') && newStatus === 'approved') {
        return await this.updateWorkScheduleFromLeave(leaveRequest, 'create');
      }

      return {
        success: true,
        message: 'Không cần cập nhật work schedule',
        data: null
      };
    } catch (error) {
      console.error('Error handling leave status change:', error);
      return {
        success: false,
        message: 'Lỗi khi xử lý thay đổi trạng thái leave request'
      };
    }
  }
}

module.exports = new ScheduleSyncService();

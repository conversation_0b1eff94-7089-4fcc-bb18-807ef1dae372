/**
 * Service đồng bộ trạng thái attendance
 * X<PERSON> lý cập nhật trạng thái absent cho các ca đã hết giờ
 */

const WorkSchedule = require('../models/workSchedule');
const AttendanceRecord = require('../models/attendanceRecord');
const LeaveRequest = require('../models/leaveRequest');
const DateUtils = require('../utils/dateUtils');

class AttendanceStatusSyncService {
  /**
   * Cập nhật trạng thái absent cho các ca đã hết giờ
   * @param {String} targetDate - <PERSON><PERSON>y cần kiểm tra (DD-MM-YYYY), mặc định là hôm nay
   * @returns {Object} Kết quả cập nhật
   */
  async syncAbsentStatus(targetDate = null) {
    try {
      // Nếu không có targetDate, sử dụng ngày hôm nay
      if (!targetDate) {
        const today = new Date();
        targetDate = DateUtils.convertYYYYMMDDtoDDMMYYYY(today.toISOString().split('T')[0]);
      }

      console.log(`[SYNC_STATUS] Starting sync for date: ${targetDate}`);

      // Lấy tất cả WorkSchedule của ngày đó
      const workSchedules = await WorkSchedule.find({
        date: targetDate,
        status: 1
      }).populate('user', 'name idNumber').lean();

      if (workSchedules.length === 0) {
        console.log(`[SYNC_STATUS] No work schedules found for ${targetDate}`);
        return {
          success: true,
          message: 'No work schedules to process',
          data: { processed: 0, updated: 0 }
        };
      }

      let processedCount = 0;
      let updatedCount = 0;

      // Xử lý từng WorkSchedule
      for (const schedule of workSchedules) {
        const result = await this.processWorkSchedule(schedule, targetDate);
        processedCount++;
        updatedCount += result.updatedShifts;
      }

      console.log(`[SYNC_STATUS] Completed sync for ${targetDate}: processed ${processedCount} schedules, updated ${updatedCount} shifts`);

      return {
        success: true,
        message: `Sync completed for ${targetDate}`,
        data: {
          date: targetDate,
          processed: processedCount,
          updated: updatedCount
        }
      };

    } catch (error) {
      console.error('[SYNC_STATUS] Error in syncAbsentStatus:', error);
      return {
        success: false,
        message: error.message,
        data: null
      };
    }
  }

  /**
   * Xử lý một WorkSchedule cụ thể
   * @param {Object} schedule - WorkSchedule
   * @param {String} date - Ngày (DD-MM-YYYY)
   * @returns {Object} Kết quả xử lý
   */
  async processWorkSchedule(schedule, date) {
    let updatedShifts = 0;

    // Lấy danh sách đơn nghỉ được phê duyệt cho user này
    const approvedLeaves = await this.getApprovedLeavesForDate(schedule.user._id, date);

    for (const shift of schedule.shifts) {
      // Kiểm tra ca này đã kết thúc chưa
      if (!this.isShiftCompleted(date, shift.type)) {
        // Ca chưa kết thúc, bỏ qua
        continue;
      }

      // Kiểm tra ca này đã có AttendanceRecord chưa
      const attendanceRecord = await AttendanceRecord.findOne({
        user: schedule.user._id,
        date: date,
        shift: shift.type
      });

      if (shift.status !== 'scheduled' || attendanceRecord) {
        // Đã có attendance record hoặc đã được cập nhật trước đó, bỏ qua
        continue;
      }

      // Kiểm tra có đơn nghỉ được phê duyệt không
      const hasApprovedLeave = this.hasApprovedLeaveForShift(approvedLeaves, date, shift.type);

      if (hasApprovedLeave) {
        // Có đơn nghỉ được phê duyệt -> cập nhật thành 'excused'
        if (shift.status !== 'excused') {
          await this.updateShiftStatus(schedule._id, shift.type, 'excused');
          updatedShifts++;
          console.log(`[SYNC_STATUS] Updated shift ${shift.type} to excused for user ${schedule.user.name} on ${date}`);
        }
      } else {
        // Không có đơn nghỉ và không có attendance -> cập nhật thành 'absent'
        if (shift.status !== 'absent') {
          await this.updateShiftStatus(schedule._id, shift.type, 'absent');
          updatedShifts++;
          console.log(`[SYNC_STATUS] Updated shift ${shift.type} to absent for user ${schedule.user.name} on ${date}`);
        }
      }
    }

    return { updatedShifts };
  }

  /**
   * Lấy danh sách đơn nghỉ được phê duyệt cho ngày cụ thể
   * @param {String} userId - ID user
   * @param {String} date - Ngày (DD-MM-YYYY)
   * @returns {Array} Danh sách đơn nghỉ
   */
  async getApprovedLeavesForDate(userId, date) {
    try {
      const approvedLeaves = await LeaveRequest.find({
        user: userId,
        status: 'approved',
        $or: [
          // Đơn nghỉ phép có khoảng thời gian
          {
            type: 'leave',
            startDate: { $lte: date },
            endDate: { $gte: date }
          },
          // Đơn nghỉ đột xuất hoặc đi muộn trong ngày cụ thể
          {
            type: { $in: ['emergency_leave', 'late_arrival'] },
            startDate: date
          }
        ]
      }).lean();

      return approvedLeaves;
    } catch (error) {
      console.error('Error getting approved leaves:', error);
      return [];
    }
  }

  /**
   * Kiểm tra có đơn nghỉ được phê duyệt cho ca cụ thể
   * @param {Array} approvedLeaves - Danh sách đơn nghỉ
   * @param {String} date - Ngày (DD-MM-YYYY)
   * @param {String} shiftType - Loại ca
   * @returns {Boolean}
   */
  hasApprovedLeaveForShift(approvedLeaves, date, shiftType) {
    return approvedLeaves.some(leave => {
      // Đơn nghỉ phép (có thể nhiều ngày)
      if (leave.type === 'leave') {
        return DateUtils.compareDDMMYYYY(leave.startDate, date) <= 0 &&
               DateUtils.compareDDMMYYYY(leave.endDate, date) >= 0;
      }

      // Đơn nghỉ đột xuất hoặc đi muộn (trong ngày)
      if (leave.type === 'emergency_leave' || leave.type === 'late_arrival') {
        if (leave.startDate !== date) {
          return false;
        }

        // Kiểm tra ca làm việc
        if (leave.shift === 'both') {
          return true; // Nghỉ cả ngày
        }

        return leave.shift === shiftType;
      }

      return false;
    });
  }

  /**
   * Cập nhật trạng thái shift trong WorkSchedule
   * @param {String} scheduleId - ID WorkSchedule
   * @param {String} shiftType - Loại ca
   * @param {String} status - Trạng thái mới
   */
  async updateShiftStatus(scheduleId, shiftType, status) {
    await WorkSchedule.updateOne(
      {
        _id: scheduleId,
        'shifts.type': shiftType
      },
      {
        $set: {
          'shifts.$.status': status,
          updatedAt: Date.now()
        }
      }
    );
  }

  /**
   * Kiểm tra ca làm việc đã kết thúc chưa
   * @param {String} date - Ngày (DD-MM-YYYY)
   * @param {String} shiftType - Loại ca ('morning' | 'afternoon')
   * @returns {Boolean} Ca làm việc đã kết thúc hay chưa
   */
  isShiftCompleted(date, shiftType) {
    const now = new Date();
    const currentDate = DateUtils.getCurrentDateDDMMYYYY();

    // Nếu là ngày trong tương lai, chưa kết thúc
    if (DateUtils.compareDDMMYYYY(date, currentDate) > 0) {
      return false;
    }

    // Nếu là ngày trong quá khứ, đã kết thúc
    if (DateUtils.compareDDMMYYYY(date, currentDate) < 0) {
      return true;
    }

    // Nếu là ngày hôm nay, kiểm tra thời gian
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    const currentTimeInMinutes = currentHour * 60 + currentMinute;

    // Thời gian kết thúc ca làm việc
    let shiftEndTime;
    if (shiftType === 'morning') {
      shiftEndTime = 12 * 60; // 12:00
    } else if (shiftType === 'afternoon') {
      shiftEndTime = 18 * 60; // 18:00
    }

    return currentTimeInMinutes >= shiftEndTime;
  }

  /**
   * Chạy sync cho nhiều ngày (batch processing)
   * @param {String} startDate - Ngày bắt đầu (DD-MM-YYYY)
   * @param {String} endDate - Ngày kết thúc (DD-MM-YYYY)
   * @returns {Object} Kết quả batch sync
   */
  async batchSyncAbsentStatus(startDate, endDate) {
    try {
      console.log(`[BATCH_SYNC] Starting batch sync from ${startDate} to ${endDate}`);

      const results = [];
      let currentDate = new Date(DateUtils.convertDDMMYYYYtoYYYYMMDD(startDate));
      const finalDate = new Date(DateUtils.convertDDMMYYYYtoYYYYMMDD(endDate));

      while (currentDate <= finalDate) {
        const dateStr = DateUtils.convertYYYYMMDDtoDDMMYYYY(currentDate.toISOString().split('T')[0]);
        const result = await this.syncAbsentStatus(dateStr);
        results.push(result);

        currentDate.setDate(currentDate.getDate() + 1);
      }

      const totalProcessed = results.reduce((sum, r) => sum + (r.data?.processed || 0), 0);
      const totalUpdated = results.reduce((sum, r) => sum + (r.data?.updated || 0), 0);

      console.log(`[BATCH_SYNC] Completed batch sync: processed ${totalProcessed} schedules, updated ${totalUpdated} shifts`);

      return {
        success: true,
        message: `Batch sync completed from ${startDate} to ${endDate}`,
        data: {
          dateRange: { startDate, endDate },
          totalProcessed,
          totalUpdated,
          details: results
        }
      };

    } catch (error) {
      console.error('[BATCH_SYNC] Error in batchSyncAbsentStatus:', error);
      return {
        success: false,
        message: error.message,
        data: null
      };
    }
  }
}

module.exports = new AttendanceStatusSyncService();

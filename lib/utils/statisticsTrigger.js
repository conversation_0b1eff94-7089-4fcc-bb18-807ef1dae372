const statisticsMetadataJob = require('../jobs/statisticsMetadataJob');
const statisticsService = require('../services/statisticsService');
const statisticsMetadataService = require('../services/statisticsMetadataService');
const socketManager = require('../socket/socketManager');
const CONSTANTS = require('../const');

// 🚀 NEW: Import debouncer system
const { debounceStatisticsUpdate, setRecalculationCallback } = require('./debouncer');

/**
 * Utility helper để trigger statistics events từ các API khác
 * Cung cấp interface đơn giản để các module khác có thể trigger cập nhật thống kê
 *
 * 🚀 UPDATED: Sử dụng StatisticsDebouncer với Triple Trigger System
 * - Debounce Trigger (3s): Chờ không có event mới
 * - Max Delay Trigger (15s): Force execute sau thời gian tối đa
 * - Batch Size Trigger (10 events): Execute khi đủ số lượng events
 * + Mandatory Queue System để tránh race conditions
 */
class StatisticsTrigger {

  // 🚀 NEW: Initialize debouncer callback
  static {
    // Set callback để debouncer có thể gọi statistics calculation
    setRecalculationCallback(async (taskKey, eventData) => {
      await StatisticsTrigger.executeStatisticsCalculation(taskKey, eventData);
    });

    console.log('📊 StatisticsTrigger initialized with new debouncer system');
  }

  /**
   * Trigger khi có cập nhật ca trực (DutyShift)
   * @param {String} action - Hành động: 'create', 'update', 'delete'
   * @param {Object} dutyShiftData - Dữ liệu ca trực
   */
  static triggerDutyShiftUpdate(action, dutyShiftData = {}) {
    try {
      const eventData = {
        action,
        dutyShiftId: dutyShiftData._id || dutyShiftData.id,
        officerId: dutyShiftData.officer,
        startTime: dutyShiftData.startTime,
        endTime: dutyShiftData.endTime,
        status: dutyShiftData.status,
        timestamp: Date.now()
      };

      // 🚀 NEW: Use debouncer system for officer statistics
      debounceStatisticsUpdate('on_duty_officers', {
        type: 'duty_shift_updated',
        ...eventData
      });

      debounceStatisticsUpdate('officers_by_area', {
        type: 'duty_shift_updated',
        ...eventData
      });

      debounceStatisticsUpdate('officer_summary', {
        type: 'duty_shift_updated',
        ...eventData
      });

      // ❌ REMOVED: Traditional event - Replaced by debouncer system
      // statisticsMetadataJob.triggerEvent('duty_shift_updated', eventData);

      console.log(`[StatisticsTrigger] Triggered duty_shift_updated: ${action} (via debouncer)`);
    } catch (error) {
      console.error('[StatisticsTrigger] Error triggering duty_shift_updated:', error);
    }
  }

  /**
   * Trigger khi có cập nhật lịch trực (8 loại DutySchedule)
   * @param {String} scheduleType - Loại lịch trực: 'main', 'sub', 'location', etc.
   * @param {String} action - Hành động: 'create', 'update', 'delete', 'updateShift', 'updateTemplate'
   * @param {Object} scheduleData - Dữ liệu lịch trực
   */
  static triggerDutyScheduleUpdate(scheduleType, action, scheduleData = {}) {
    try {
      // ❌ REMOVED: Traditional event - Replaced by debouncer system
      // statisticsMetadataJob.triggerEvent('duty_schedule_updated', {...});

      console.log(`[StatisticsTrigger] Triggered duty_schedule_updated: ${scheduleType} - ${action}`);
    } catch (error) {
      console.error('[StatisticsTrigger] Error triggering duty_schedule_updated:', error);
    }
  }

  /**
   * Trigger khi có cập nhật điểm danh (AttendanceRecord)
   * @param {String} action - Hành động: 'create', 'update', 'delete'
   * @param {Object} attendanceData - Dữ liệu điểm danh
   */
  static triggerAttendanceUpdate(action, attendanceData = {}) {
    try {
      const eventData = {
        action,
        attendanceId: attendanceData._id || attendanceData.id,
        userId: attendanceData.user,
        date: attendanceData.date,
        shift: attendanceData.shift,
        status: attendanceData.status,
        checkinTime: attendanceData.checkinTime,
        timestamp: Date.now()
      };

      // 🚀 NEW: Use debouncer system
      debounceStatisticsUpdate('attendance', {
        type: 'attendance_updated',
        ...eventData
      });

      // ❌ REMOVED: Traditional event - Replaced by debouncer system
      // statisticsMetadataJob.triggerEvent('attendance_updated', eventData);

      console.log(`[StatisticsTrigger] Triggered attendance_updated: ${action} (via debouncer)`);
    } catch (error) {
      console.error('[StatisticsTrigger] Error triggering attendance_updated:', error);
    }
  }

  /**
   * Trigger khi có cập nhật lịch làm việc (WorkSchedule)
   * @param {String} action - Hành động: 'create', 'update', 'delete'
   * @param {Object} scheduleData - Dữ liệu lịch làm việc
   */
  static triggerWorkScheduleUpdate(action, scheduleData = {}) {
    try {
      const eventData = {
        action,
        scheduleId: scheduleData._id || scheduleData.id,
        userId: scheduleData.user,
        date: scheduleData.date,
        shifts: scheduleData.shifts,
        timestamp: Date.now()
      };

      // 🚀 NEW: Use debouncer system
      debounceStatisticsUpdate('attendance', {
        type: 'work_schedule_updated',
        ...eventData
      });

      // ❌ REMOVED: Traditional event - Replaced by debouncer system
      // statisticsMetadataJob.triggerEvent('work_schedule_updated', eventData);

      console.log(`[StatisticsTrigger] Triggered work_schedule_updated: ${action} (via debouncer)`);
    } catch (error) {
      console.error('[StatisticsTrigger] Error triggering work_schedule_updated:', error);
    }
  }

  /**
   * Trigger khi có cập nhật thông tin cán bộ (User)
   * @param {String} action - Hành động: 'create', 'update', 'inactive', 'activate'
   * @param {Object} userData - Dữ liệu cán bộ
   */
  static triggerUserUpdate(action, userData = {}) {
    try {
      const eventData = {
        action,
        userId: userData._id || userData.id,
        status: userData.status,
        units: userData.units,
        areas: userData.areas,
        positions: userData.positions,
        timestamp: Date.now()
      };

      // 🚀 NEW: Use debouncer system for officer statistics
      debounceStatisticsUpdate('officers_by_area', {
        type: 'user_updated',
        ...eventData
      });

      debounceStatisticsUpdate('officer_summary', {
        type: 'user_updated',
        ...eventData
      });

      debounceStatisticsUpdate('on_duty_officers', {
        type: 'user_updated',
        ...eventData
      });

      // ❌ REMOVED: Traditional event - Replaced by debouncer system
      // statisticsMetadataJob.triggerEvent('user_updated', eventData);

      console.log(`[StatisticsTrigger] Triggered user_updated: ${action} (via debouncer)`);
    } catch (error) {
      console.error('[StatisticsTrigger] Error triggering user_updated:', error);
    }
  }

  /**
   * Trigger khi có cập nhật đơn xin nghỉ (LeaveRequest)
   * @param {String} action - Hành động: 'create', 'approve', 'reject', 'cancel'
   * @param {Object} leaveData - Dữ liệu đơn xin nghỉ
   */
  static triggerLeaveRequestUpdate(action, leaveData = {}) {
    try {
      // ❌ REMOVED: Traditional event - Replaced by debouncer system
      // statisticsMetadataJob.triggerEvent('leave_request_updated', {...});

      console.log(`[StatisticsTrigger] Triggered leave_request_updated: ${action}`);
    } catch (error) {
      console.error('[StatisticsTrigger] Error triggering leave_request_updated:', error);
    }
  }

  /**
   * Trigger khi có cập nhật khu vực (Area)
   * @param {String} action - Hành động: 'create', 'update', 'delete'
   * @param {Object} areaData - Dữ liệu khu vực
   */
  static triggerAreaUpdate(action, areaData = {}) {
    try {
      const eventData = {
        action,
        areaId: areaData._id || areaData.id,
        name: areaData.name,
        level: areaData.level,
        status: areaData.status,
        timestamp: Date.now()
      };

      // 🚀 NEW: Use debouncer system for area-related statistics
      debounceStatisticsUpdate('officers_by_area', {
        type: 'area_updated',
        ...eventData
      });

      debounceStatisticsUpdate('reports_by_area', {
        type: 'area_updated',
        ...eventData
      });

      // ❌ REMOVED: Traditional event - Replaced by debouncer system
      // statisticsMetadataJob.triggerEvent('area_updated', eventData);

      console.log(`[StatisticsTrigger] Triggered area_updated: ${action} (via debouncer)`);
    } catch (error) {
      console.error('[StatisticsTrigger] Error triggering area_updated:', error);
    }
  }

  /**
   * Trigger khi có cập nhật báo cáo (Report)
   * @param {String} action - Hành động: 'create', 'update', 'delete', 'approve', 'reject'
   * @param {Object} reportData - Dữ liệu báo cáo
   */
  static triggerReportUpdate(action, reportData = {}) {
    try {
      const eventData = {
        action,
        reportId: reportData._id || reportData.id,
        reportType: reportData.reportType,
        jobType: reportData.jobType,
        status: reportData.status,
        workStatus: reportData.workStatus,
        createdBy: reportData.createdBy,
        unit: reportData.unit,
        metrics: reportData.metrics,
        areas: reportData.details?.map(d => d.location?.area).filter(Boolean) || [],
        timestamp: Date.now()
      };

      // 🚀 NEW: Use debouncer system for multiple statistics
      debounceStatisticsUpdate('reports_summary', {
        type: 'report_updated',
        ...eventData
      });

      debounceStatisticsUpdate('reports_status', {
        type: 'report_updated',
        ...eventData
      });

      debounceStatisticsUpdate('reports_by_area', {
        type: 'report_updated',
        ...eventData
      });

      // Trigger cho highlight reports
      debounceStatisticsUpdate('latest_incidents', {
        type: 'highlight_report_updated',
        ...eventData
      });

      debounceStatisticsUpdate('reports_incidents_highlight', {
        type: 'highlight_report_updated',
        ...eventData
      });

      // Trigger cho other reports (non-highlight)
      debounceStatisticsUpdate('reports_incidents_other', {
        type: 'other_report_updated',
        ...eventData
      });

      // 🚀 NEW: Trigger documents summary nếu là document report
      if (this.isDocumentReport(reportData)) {
        debounceStatisticsUpdate('documents_summary', {
          type: 'document_report_updated',
          ...eventData
        });
      }

      // ❌ REMOVED: Traditional event - Replaced by debouncer system
      // statisticsMetadataJob.triggerEvent('report_updated', eventData);

      console.log(`[StatisticsTrigger] Triggered report_updated: ${action} (via debouncer)`);
    } catch (error) {
      console.error('[StatisticsTrigger] Error triggering report_updated:', error);
    }
  }

  /**
   * Trigger khi có cập nhật báo cáo công văn (Report)
   * @param {String} action - Hành động: 'create', 'update', 'delete', 'process', 'complete'
   * @param {Object} reportData - Dữ liệu báo cáo
   * @param {Object} oldReportData - Dữ liệu báo cáo cũ (cho action 'update')
   */
  static triggerDocumentUpdate(action, reportData = {}, oldReportData = null) {
    try {
      // Chỉ xử lý nếu là report công văn
      const DOCUMENT_JOB_TYPE_ID = CONSTANTS.DOCUMENT_JOB_TYPE_ID;
      if (reportData.jobType !== DOCUMENT_JOB_TYPE_ID) {
        return;
      }

      // ❌ REMOVED: Traditional event - Replaced by debouncer system
      // statisticsMetadataJob.triggerEvent('document_updated', {...});

      // Cập nhật document metadata counter
      const documentMetadataService = require('../services/documentMetadataService');

      switch (action) {
        case 'create':
          documentMetadataService.handleReportCreated(reportData);
          break;
        case 'update':
          if (oldReportData) {
            documentMetadataService.handleReportUpdated(oldReportData, reportData);
          }
          break;
        // Có thể thêm xử lý cho 'delete' nếu cần
      }

      console.log(`[StatisticsTrigger] Triggered document_updated: ${action} for document report`);
    } catch (error) {
      console.error('[StatisticsTrigger] Error triggering document_updated:', error);
    }
  }

  /**
   * Kiểm tra xem báo cáo có phải là document report không
   * @param {Object} reportData - Dữ liệu báo cáo
   * @returns {Boolean} True nếu là document report
   */
  static isDocumentReport(reportData) {
    const DOCUMENT_JOB_TYPE_ID = CONSTANTS.DOCUMENT_JOB_TYPE_ID;
    return reportData.jobType === DOCUMENT_JOB_TYPE_ID;
  }

  /**
   * Trigger multiple events cùng lúc
   * @param {Array} events - Mảng các events: [{ type, action, data }]
   */
  static triggerMultipleEvents(events) {
    try {
      events.forEach(event => {
        const { type, action, data } = event;

        switch (type) {
          case 'duty_shift':
            this.triggerDutyShiftUpdate(action, data);
            break;
          case 'duty_schedule':
            this.triggerDutyScheduleUpdate(data.scheduleType, action, data);
            break;
          case 'attendance':
            this.triggerAttendanceUpdate(action, data);
            break;
          case 'work_schedule':
            this.triggerWorkScheduleUpdate(action, data);
            break;
          case 'user':
            this.triggerUserUpdate(action, data);
            break;
          case 'leave_request':
            this.triggerLeaveRequestUpdate(action, data);
            break;
          case 'area':
            this.triggerAreaUpdate(action, data);
            break;
          case 'report':
            this.triggerReportUpdate(action, data);
            break;

          default:
            console.warn(`[StatisticsTrigger] Unknown event type: ${type}`);
        }
      });

      console.log(`[StatisticsTrigger] Triggered ${events.length} multiple events`);
    } catch (error) {
      console.error('[StatisticsTrigger] Error triggering multiple events:', error);
    }
  }

  /**
   * Force invalidate cache cho một loại thống kê cụ thể
   * @param {String} type - Loại thống kê: 'on_duty_officers', 'attendance', etc.
   */
  static async invalidateCache(type) {
    try {
      await statisticsMetadataJob.invalidateCache(type);

      console.log(`[StatisticsTrigger] Cache invalidated for ${type}:`, { type });
      return { success: true, type };
    } catch (error) {
      console.error(`[StatisticsTrigger] Error invalidating cache for ${type}:`, error);
      return { success: false, error: error.message };
    }
  }

  // ========================================
  // 🚀 NEW DEBOUNCER SYSTEM INTEGRATION
  // ========================================

  /**
   * Execute statistics calculation - được gọi bởi debouncer
   * @param {String} taskKey - Task key (vd: 'reports_summary', 'attendance')
   * @param {Object} eventData - Event data
   */
  static async executeStatisticsCalculation(taskKey, eventData) {
    try {
      console.log(`🚀 Executing statistics calculation for ${taskKey}`);

      // Parse task type từ taskKey
      const taskType = taskKey.split(':')[0];

      switch (taskType) {
        case 'reports_summary':
          await this.recalculateReportsStatistics(eventData);
          break;

        case 'attendance':
          await this.recalculateAttendanceStatistics(eventData);
          break;

        case 'security_incidents':
          await this.recalculateSecurityStatistics(eventData);
          break;

        case 'reports_by_area':
          await this.recalculateAreaStatistics(eventData);
          break;

        case 'officer_stats':
          await this.recalculateOfficerStatistics(eventData);
          break;

        case 'latest_incidents':
          await this.recalculateLatestIncidents(eventData);
          break;

        case 'officers_by_area':
          await this.recalculateOfficersByArea(eventData);
          break;

        case 'officer_summary':
          await this.recalculateOfficerSummary(eventData);
          break;

        case 'on_duty_officers':
          await this.recalculateOnDutyOfficers(eventData);
          break;

        case 'reports_status':
          await this.recalculateReportsStatus(eventData);
          break;

        case 'documents_summary':
          await this.recalculateDocumentsSummary(eventData);
          break;

        case 'reports_incidents_highlight':
          await this.recalculateIncidentsHighlight(eventData);
          break;

        case 'reports_incidents_other':
          await this.recalculateIncidentsOther(eventData);
          break;

        default:
          // Fallback to general metadata calculation
          await statisticsMetadataJob.runMetadataCalculationNow();
      }

      console.log(`✅ Statistics calculation completed for ${taskKey}`);

    } catch (error) {
      console.error(`❌ Statistics calculation failed for ${taskKey}:`, error);
      throw error;
    }
  }

  /**
   * Recalculate reports statistics
   */
  static async recalculateReportsStatistics(eventData) {
    // Invalidate cache cho reports summary
    await this.invalidateCache('reports_summary');

    // Notify real-time updates via socket
    if (socketManager.io) {
      socketManager.io.emit('statistics_updated', {
        type: 'reports_summary',
        timestamp: Date.now()
      });
    }
  }

  /**
   * Recalculate attendance statistics
   */
  static async recalculateAttendanceStatistics(eventData) {
    // Invalidate cache cho attendance stats
    await this.invalidateCache('attendance');

    // Notify real-time updates via socket
    if (socketManager.io) {
      socketManager.io.emit('statistics_updated', {
        type: 'attendance',
        timestamp: Date.now()
      });
    }
  }

  /**
   * Recalculate security incidents statistics
   */
  static async recalculateSecurityStatistics(eventData) {
    // High priority - immediate calculation
    await statisticsMetadataJob.runMetadataCalculationNow();

    // Notify real-time updates via socket
    if (socketManager.io) {
      socketManager.io.emit('statistics_updated', {
        type: 'security_incidents',
        timestamp: Date.now(),
        priority: 'high'
      });
    }
  }

  /**
   * Recalculate area-based statistics
   */
  static async recalculateAreaStatistics(eventData) {
    // Invalidate cache cho reports by area
    await this.invalidateCache('reports_by_area');

    if (socketManager.io) {
      socketManager.io.emit('statistics_updated', {
        type: 'reports_by_area',
        timestamp: Date.now()
      });
    }
  }

  /**
   * Recalculate officer statistics
   */
  static async recalculateOfficerStatistics(eventData) {
    // Invalidate cache cho officer stats
    await this.invalidateCache('officer_stats');

    if (socketManager.io) {
      socketManager.io.emit('statistics_updated', {
        type: 'officer_stats',
        timestamp: Date.now()
      });
    }
  }

  /**
   * Recalculate latest incidents
   */
  static async recalculateLatestIncidents(eventData) {
    // Invalidate cache cho latest incidents
    await this.invalidateCache('latest_incidents');

    // Notify real-time updates via socket
    if (socketManager.io) {
      socketManager.io.emit('statistics_updated', {
        type: 'latest_incidents',
        timestamp: Date.now()
      });
    }
  }

  /**
   * Recalculate officers by area statistics
   */
  static async recalculateOfficersByArea(eventData) {
    await this.invalidateCache('officers_by_area');

    if (socketManager.io) {
      socketManager.io.emit('statistics_updated', {
        type: 'officers_by_area',
        timestamp: Date.now()
      });
    }
  }

  /**
   * Recalculate officer summary statistics
   */
  static async recalculateOfficerSummary(eventData) {
    await this.invalidateCache('officer_summary');

    if (socketManager.io) {
      socketManager.io.emit('statistics_updated', {
        type: 'officer_summary',
        timestamp: Date.now()
      });
    }
  }

  /**
   * Recalculate on duty officers statistics
   */
  static async recalculateOnDutyOfficers(eventData) {
    await this.invalidateCache('on_duty_officers');

    if (socketManager.io) {
      socketManager.io.emit('statistics_updated', {
        type: 'on_duty_officers',
        timestamp: Date.now()
      });
    }
  }

  /**
   * Recalculate reports status statistics
   */
  static async recalculateReportsStatus(eventData) {
    await this.invalidateCache('reports_status');

    if (socketManager.io) {
      socketManager.io.emit('statistics_updated', {
        type: 'reports_status',
        timestamp: Date.now()
      });
    }
  }

  /**
   * Recalculate documents summary statistics
   */
  static async recalculateDocumentsSummary(eventData) {
    await this.invalidateCache('documents_summary');

    if (socketManager.io) {
      socketManager.io.emit('statistics_updated', {
        type: 'documents_summary',
        timestamp: Date.now()
      });
    }
  }

  /**
   * Recalculate incidents highlight statistics
   */
  static async recalculateIncidentsHighlight(eventData) {
    await this.invalidateCache('reports_incidents_highlight');

    if (socketManager.io) {
      socketManager.io.emit('statistics_updated', {
        type: 'reports_incidents_highlight',
        timestamp: Date.now()
      });
    }
  }

  /**
   * Recalculate incidents other statistics
   */
  static async recalculateIncidentsOther(eventData) {
    await this.invalidateCache('reports_incidents_other');

    if (socketManager.io) {
      socketManager.io.emit('statistics_updated', {
        type: 'reports_incidents_other',
        timestamp: Date.now()
      });
    }
  }

  /**
   * Force recalculate metadata ngay lập tức
   */
  static async forceRecalculate() {
    try {
      const result = await statisticsMetadataJob.runMetadataCalculationNow();
      console.log('[StatisticsTrigger] Force recalculation completed:', result);
      return result;
    } catch (error) {
      console.error('[StatisticsTrigger] Error in force recalculation:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Lấy thống kê về trigger events
   */
  static getJobStats() {
    try {
      return statisticsMetadataJob.getJobStats();
    } catch (error) {
      console.error('[StatisticsTrigger] Error getting job stats:', error);
      return { error: error.message };
    }
  }
}

module.exports = StatisticsTrigger;

/**
 * Statistics Debouncer với Triple Trigger System
 *
 * 3 cơ chế kích hoạt:
 * 1. <PERSON><PERSON><PERSON><PERSON>gger (3s): Ch<PERSON> không có event mới
 * 2. Max Delay Trigger (15s): Force execute sau thời gian tối đa
 * 3. <PERSON><PERSON>ze Trigger (10 events): Execute khi đủ số lượng events
 *
 * + Mandatory Queue System để tránh race conditions
 */

const { getTaskConfig } = require('./config');

class StatisticsDebouncer {
  constructor() {
    // Core state management
    this.timers = new Map();              // taskKey -> setTimeout timer
    this.executingTasks = new Set();      // Set of taskKeys đang execute
    this.pendingQueues = new Map();       // taskKey -> [events] chờ xử lý

    // Event tracking
    this.firstEventTime = new Map();      // taskKey -> timestamp của event đầu tiên
    this.eventCounts = new Map();         // taskKey -> số lượng events hiện tại

    // Metrics tracking
    this.metrics = {
      totalEvents: 0,
      batchTriggers: 0,
      delayTriggers: 0,
      debounceTriggers: 0,
      queuedEvents: 0,
      executionErrors: 0
    };

    // Callback function để execute statistics calculation
    this.recalculationCallback = null;

    console.log('📊 StatisticsDebouncer initialized');
  }

  /**
   * Set callback function để thực hiện tính toán thống kê
   * @param {Function} callback - Function nhận (taskKey, eventData)
   */
  setRecalculationCallback(callback) {
    this.recalculationCallback = callback;
    console.log('🔗 Recalculation callback registered');
  }

  /**
   * Main entry point - Debounce statistics update với triple trigger
   * @param {string} taskKey - Unique key cho task (vd: 'reports_summary:day')
   * @param {object} event - Event data
   */
  debounceStatisticsUpdate(taskKey, event) {
    this.metrics.totalEvents++;

    console.log(`📥 Event received: ${taskKey}`, {
      type: event.type,
      timestamp: new Date().toISOString()
    });

    // 🔒 CRITICAL: Check if task đang execute
    if (this.executingTasks.has(taskKey)) {
      console.log(`⏳ Task ${taskKey} đang execute, adding to queue`);
      this.addToQueue(taskKey, event);
      return;
    }

    // Initialize tracking nếu chưa có
    if (!this.firstEventTime.has(taskKey)) {
      this.firstEventTime.set(taskKey, Date.now());
      this.eventCounts.set(taskKey, 0);
    }

    // Increment event count
    const currentCount = this.eventCounts.get(taskKey) + 1;
    this.eventCounts.set(taskKey, currentCount);

    // Clear existing timer
    if (this.timers.has(taskKey)) {
      clearTimeout(this.timers.get(taskKey));
    }

    const config = getTaskConfig(taskKey.split(':')[0]); // Get base task type

    // 🎯 CHECK 3 TRIGGERS:

    // 1️⃣ BATCH SIZE TRIGGER - Execute ngay nếu đủ events
    if (currentCount >= config.maxBatchSize) {
      console.log(`🚀 Batch trigger: ${taskKey} with ${currentCount} events (>= ${config.maxBatchSize})`);
      this.metrics.batchTriggers++;
      this.executeTask(taskKey, 'batch', event);
      return;
    }

    // 2️⃣ MAX DELAY TRIGGER - Force execute nếu chờ quá lâu
    const waitedTime = Date.now() - this.firstEventTime.get(taskKey);
    if (waitedTime >= config.maxDelay) {
      console.log(`⏰ Max delay trigger: ${taskKey} after ${waitedTime}ms (>= ${config.maxDelay}ms)`);
      this.metrics.delayTriggers++;
      this.executeTask(taskKey, 'maxDelay', event);
      return;
    }

    // 3️⃣ DEBOUNCE TRIGGER - Normal debounce
    const remainingMaxDelay = config.maxDelay - waitedTime;
    const delay = Math.min(config.debounceDelay, remainingMaxDelay);

    console.log(`🕐 Debounce timer set: ${taskKey} for ${delay}ms (count: ${currentCount})`);

    const timer = setTimeout(() => {
      console.log(`🕐 Debounce trigger: ${taskKey} after ${delay}ms with ${currentCount} events`);
      this.metrics.debounceTriggers++;
      this.executeTask(taskKey, 'debounce', event);
    }, delay);

    this.timers.set(taskKey, timer);
  }

  /**
   * Add event to pending queue (khi task đang execute)
   * @param {string} taskKey
   * @param {object} event
   */
  addToQueue(taskKey, event) {
    if (!this.pendingQueues.has(taskKey)) {
      this.pendingQueues.set(taskKey, []);
    }

    const queue = this.pendingQueues.get(taskKey);
    queue.push({
      ...event,
      timestamp: Date.now(),
      queuedAt: new Date().toISOString()
    });

    this.metrics.queuedEvents++;

    console.log(`📥 Event queued for ${taskKey}, queue size: ${queue.length}`);
  }

  /**
   * Execute statistics calculation task
   * @param {string} taskKey
   * @param {string} triggerType - 'batch' | 'maxDelay' | 'debounce'
   * @param {object} eventData
   */
  async executeTask(taskKey, triggerType, eventData) {
    // 🔒 Mark as executing (CRITICAL for race condition prevention)
    this.executingTasks.add(taskKey);

    const startTime = Date.now();

    try {
      console.log(`🚀 Starting execution: ${taskKey} (trigger: ${triggerType})`);

      // Call the actual statistics calculation
      if (this.recalculationCallback) {
        await this.recalculationCallback(taskKey, eventData);
      } else {
        console.warn('⚠️ No recalculation callback set, skipping execution');
      }

      const executionTime = Date.now() - startTime;
      console.log(`✅ Execution completed: ${taskKey} in ${executionTime}ms`);

    } catch (error) {
      this.metrics.executionErrors++;
      console.error(`❌ Execution failed for ${taskKey}:`, error);
      throw error;

    } finally {
      // 🔓 ALWAYS unlock (even if error occurs)
      this.executingTasks.delete(taskKey);
      this.resetTracking(taskKey);

      // 🔄 Process pending queue
      await this.processQueue(taskKey);
    }
  }

  /**
   * Process pending queue sau khi task hoàn thành
   * @param {string} taskKey
   */
  async processQueue(taskKey) {
    const queue = this.pendingQueues.get(taskKey);

    if (!queue || queue.length === 0) {
      console.log(`📭 No queued events for ${taskKey}`);
      return;
    }

    console.log(`📤 Processing ${queue.length} queued events for ${taskKey}`);

    // Merge strategy: Lấy event mới nhất
    const latestEvent = queue[queue.length - 1];
    const queueAge = Date.now() - latestEvent.timestamp;

    // Clear queue
    this.pendingQueues.delete(taskKey);

    // Delay nhỏ để tránh spam, nhưng không quá lâu
    const delay = Math.min(2000, Math.max(500, 3000 - queueAge));

    console.log(`🔄 Re-triggering debounce for queued events after ${delay}ms`);

    setTimeout(() => {
      this.debounceStatisticsUpdate(taskKey, latestEvent);
    }, delay);
  }

  /**
   * Reset tracking data cho task
   * @param {string} taskKey
   */
  resetTracking(taskKey) {
    this.timers.delete(taskKey);
    this.firstEventTime.delete(taskKey);
    this.eventCounts.delete(taskKey);
  }

  /**
   * Get current system status (for monitoring)
   * @returns {object} Status object
   */
  getStatus() {
    return {
      timestamp: new Date().toISOString(),

      // Current state
      executingTasks: Array.from(this.executingTasks),
      queueSizes: Object.fromEntries(
        Array.from(this.pendingQueues.entries())
          .map(([key, queue]) => [key, queue.length])
      ),
      activeTimers: this.timers.size,

      // Metrics
      metrics: { ...this.metrics },

      // Health indicators
      health: {
        isHealthy: this.metrics.executionErrors < 10, // < 10 errors = healthy
        hasStuckTasks: this.executingTasks.size > 0, // Any stuck tasks?
        memoryUsage: process.memoryUsage()
      }
    };
  }

  /**
   * Force cleanup (for emergency situations)
   * @param {string} taskKey - Optional, nếu không có sẽ cleanup all
   */
  forceCleanup(taskKey = null) {
    if (taskKey) {
      console.log(`🧹 Force cleanup for ${taskKey}`);
      this.executingTasks.delete(taskKey);
      this.resetTracking(taskKey);
      this.pendingQueues.delete(taskKey);
    } else {
      console.log('🧹 Force cleanup all tasks');
      this.executingTasks.clear();
      this.timers.clear();
      this.firstEventTime.clear();
      this.eventCounts.clear();
      this.pendingQueues.clear();
    }
  }
}

module.exports = StatisticsDebouncer;
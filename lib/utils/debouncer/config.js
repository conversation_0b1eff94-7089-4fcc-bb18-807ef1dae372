/**
 * Configuration cho Statistics Debouncer System
 * Định nghĩa các thông số cho từng loại task
 */

const DEBOUNCE_CONFIG = {
  // Báo cáo tổng hợp
  'reports_summary': {
    debounceDelay: 3000,    // 3s - chờ không có event mới
    maxDelay: 15000,        // 15s - tối đa phải execute
    maxBatchSize: 10,       // 10 events - execute ngay
    enabled: true
  },

  // Thống kê điểm danh
  'attendance': {
    debounceDelay: 2000,    // 2s - nhanh hơn vì real-time
    maxDelay: 10000,        // 10s - tối đa
    maxBatchSize: 20,       // 20 events - có thể batch nhiều hơn
    enabled: true
  },

  // Sự cố an ninh - ưu tiên cao
  'security_incidents': {
    debounceDelay: 1000,    // 1s - rất nhanh
    maxDelay: 5000,         // 5s - tối đa ngắn
    maxBatchSize: 3,        // 3 events - <PERSON>t thôi vì quan trọng
    enabled: true
  },

  // Báo cáo theo khu vực
  'reports_by_area': {
    debounceDelay: 3000,
    maxDelay: 15000,
    maxBatchSize: 8,
    enabled: true
  },

  // Thống kê cán bộ
  'officer_stats': {
    debounceDelay: 4000,    // 4s - có thể chờ lâu hơn
    maxDelay: 20000,        // 20s
    maxBatchSize: 15,
    enabled: true
  },

  // Default config cho các task không được định nghĩa
  'default': {
    debounceDelay: 3000,
    maxDelay: 15000,
    maxBatchSize: 10,
    enabled: true
  }
};

// Environment overrides
const ENV_OVERRIDES = {
  development: {
    // Dev environment - nhanh hơn để test
    'reports_summary': {
      debounceDelay: 1000,
      maxDelay: 5000,
      maxBatchSize: 5
    }
  },

  production: {
    // Production - conservative hơn
    'reports_summary': {
      debounceDelay: 5000,
      maxDelay: 20000,
      maxBatchSize: 15
    }
  }
};

/**
 * Get config cho một task key
 * @param {string} taskKey - Key của task (vd: 'reports_summary')
 * @returns {object} Configuration object
 */
function getTaskConfig(taskKey) {
  const env = process.env.NODE_ENV || 'development';

  // Base config
  let config = DEBOUNCE_CONFIG[taskKey] || DEBOUNCE_CONFIG.default;

  // Apply environment overrides
  if (ENV_OVERRIDES[env] && ENV_OVERRIDES[env][taskKey]) {
    config = { ...config, ...ENV_OVERRIDES[env][taskKey] };
  }

  return config;
}

/**
 * Get all available task keys
 * @returns {string[]} Array of task keys
 */
function getAllTaskKeys() {
  return Object.keys(DEBOUNCE_CONFIG).filter(key => key !== 'default');
}

module.exports = {
  DEBOUNCE_CONFIG,
  ENV_OVERRIDES,
  getTaskConfig,
  getAllTaskKeys
};
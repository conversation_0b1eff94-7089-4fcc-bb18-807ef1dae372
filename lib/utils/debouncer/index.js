/**
 * Statistics Debouncer - Main Export
 *
 * Provides global singleton instance của StatisticsDebouncer
 * để sử dụng trong toàn bộ application
 */

const StatisticsDebouncer = require('./StatisticsDebouncer');
const { getTaskConfig, getAllTaskKeys } = require('./config');

// Global singleton instance
const statisticsDebouncer = new StatisticsDebouncer();

// Export both instance và utilities
module.exports = {
  // Main debouncer instance
  debouncer: statisticsDebouncer,

  // Convenience methods
  debounceStatisticsUpdate: (taskKey, event) => {
    return statisticsDebouncer.debounceStatisticsUpdate(taskKey, event);
  },

  setRecalculationCallback: (callback) => {
    return statisticsDebouncer.setRecalculationCallback(callback);
  },

  getStatus: () => {
    return statisticsDebouncer.getStatus();
  },

  forceCleanup: (taskKey) => {
    return statisticsDebouncer.forceCleanup(taskKey);
  },

  // Config utilities
  getTaskConfig,
  getAllTaskKeys,

  // Direct access to class (for testing)
  StatisticsDebouncer
};

// Log initialization
console.log('📊 Statistics Debouncer module loaded');
console.log('🔧 Available task types:', getAllTaskKeys());